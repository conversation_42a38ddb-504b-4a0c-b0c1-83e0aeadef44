// Copyright (C) 2024 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

// SoftBus系统服务Java库
java_library {
    name: "services.softbus",
    
    srcs: [
        "com/android/server/softbus/**/*.java",
    ],
    
    libs: [
        "services.core",
        "framework-softbus",
    ],
    
    static_libs: [
        "modules-utils-build",
    ],
    
    installable: true,
    
    plugins: ["java_api_finder"],
    
    lint: {
        strict_updatability_linting: true,
    },
    
    visibility: [
        "//frameworks/base/services",
    ],
}

// SoftBus Native接口
java_library {
    name: "softbus-native-interface",
    
    srcs: [
        "com/android/server/softbus/SoftBusNativeInterface.java",
    ],
    
    libs: [
        "framework-softbus",
    ],
    
    installable: true,
}

// SoftBus发现管理器
java_library {
    name: "softbus-discovery-manager",
    
    srcs: [
        "com/android/server/softbus/DiscoveryManager.java",
    ],
    
    libs: [
        "services.core",
        "framework-softbus",
    ],
    
    static_libs: [
        "softbus-native-interface",
    ],
    
    installable: true,
}

// SoftBus连接管理器
java_library {
    name: "softbus-connection-manager",
    
    srcs: [
        "com/android/server/softbus/ConnectionManager.java",
    ],
    
    libs: [
        "services.core",
        "framework-softbus",
    ],
    
    static_libs: [
        "softbus-native-interface",
    ],
    
    installable: true,
}

// SoftBus传输管理器
java_library {
    name: "softbus-transmission-manager",
    
    srcs: [
        "com/android/server/softbus/TransmissionManager.java",
    ],
    
    libs: [
        "services.core",
        "framework-softbus",
    ],
    
    static_libs: [
        "softbus-native-interface",
    ],
    
    installable: true,
}

// SoftBus设备管理器
java_library {
    name: "softbus-device-manager",
    
    srcs: [
        "com/android/server/softbus/DeviceManager.java",
    ],
    
    libs: [
        "services.core",
        "framework-softbus",
    ],
    
    static_libs: [
        "softbus-native-interface",
    ],
    
    installable: true,
}

// SoftBus安全管理器
java_library {
    name: "softbus-security-manager",
    
    srcs: [
        "com/android/server/softbus/SecurityManager.java",
    ],
    
    libs: [
        "services.core",
        "framework-softbus",
    ],
    
    static_libs: [
        "softbus-native-interface",
    ],
    
    installable: true,
}

// SoftBus服务测试
android_test {
    name: "SoftBusServiceTests",
    
    srcs: [
        "tests/**/*Test.java",
    ],
    
    libs: [
        "android.test.runner",
        "android.test.base",
        "services.core",
    ],
    
    static_libs: [
        "services.softbus",
        "framework-softbus",
        "androidx.test.rules",
        "mockito-target-minus-junit4",
        "truth-prebuilt",
    ],
    
    test_suites: ["device-tests"],
    platform_apis: true,
    
    jni_libs: [
        "libsoftbus_service",
    ],
}

// SoftBus服务集成测试
android_test {
    name: "SoftBusServiceIntegrationTests",
    
    srcs: [
        "integration_tests/**/*.java",
    ],
    
    libs: [
        "android.test.runner",
        "android.test.base",
        "services.core",
    ],
    
    static_libs: [
        "services.softbus",
        "framework-softbus",
        "androidx.test.rules",
        "androidx.test.uiautomator_uiautomator",
    ],
    
    test_suites: ["device-tests"],
    platform_apis: true,
    
    jni_libs: [
        "libsoftbus_service",
    ],
}

// SoftBus服务性能测试
android_test {
    name: "SoftBusServicePerformanceTests",
    
    srcs: [
        "performance_tests/**/*.java",
    ],
    
    libs: [
        "android.test.runner",
        "android.test.base",
        "services.core",
    ],
    
    static_libs: [
        "services.softbus",
        "framework-softbus",
        "androidx.test.rules",
        "androidx.benchmark_benchmark-junit4",
    ],
    
    test_suites: ["device-tests"],
    platform_apis: true,
    
    jni_libs: [
        "libsoftbus_service",
    ],
}
