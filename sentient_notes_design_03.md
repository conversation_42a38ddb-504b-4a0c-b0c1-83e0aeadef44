# "灵境笔记 (Sentient Notes)" AI系统设计 - 第三部分

## 03. 总体工作流程总览 (SN-Core内部模块化架构)

### 3.1 SN-Core核心模块架构设计

基于对SoftBus特性和"灵境笔记"核心需求的深入分析，我重新设计了SN-Core的模块化架构，确保每个模块职责清晰，数据流转高效，并充分利用SoftBus的低延迟、高带宽特性。

#### 3.1.1 核心模块定义

**1. MultimodalInputFusionAndAtomizationEngine (MIFAE)**
- **功能职责**: 接收和解析来自SoftBus的各种原始输入，将其转化为标准化的笔记原子
- **主要输入**: SN_EVENT_STREAM的各种特化形式
- **主要输出**: 标准化的NoteAtom对象，带有初步语义标记
- **SoftBus依赖**: 
  - 高频数据流接收（AR手势、Pencil笔迹）
  - 多设备并发输入处理
  - 低延迟要求：<50ms端到端处理时间

**2. SpatialKnowledgeGraphEngine (SKG-Engine)**
- **功能职责**: 笔记原子及其关系的存储、索引、查询、以及动态演化
- **主要输入**: NoteAtom对象、用户交互事件、AI分析结果
- **主要输出**: 知识图谱查询结果、关联推荐、空间布局建议
- **SoftBus依赖**:
  - 分布式图数据同步
  - 多用户协同编辑的一致性保证
  - 支持大规模图数据的增量传输

**3. AIInsightAndEmergenceEngine (AIE2)**
- **功能职责**: 深度语义理解、关联发现、模式识别、智能摘要、问题生成、个性化推荐
- **主要输入**: 知识图谱数据、用户行为模式、外部知识源
- **主要输出**: 智能洞察、创意建议、知识缺口分析、自动生成内容
- **SoftBus依赖**:
  - 分布式AI计算任务调度
  - 大模型推理结果的高效传输
  - 支持AI模型的动态加载和卸载

**4. XRInteractionAndSpatialUIManager (XR-ISM)**
- **功能职责**: 将SKG和AIE2的输出转化为AR/VR设备的具体渲染指令和Spatial UI更新
- **主要输入**: 知识图谱布局、AI推荐结果、用户交互事件
- **主要输出**: AR可视化指令、Spatial UI更新、交互反馈
- **SoftBus依赖**:
  - 超低延迟渲染指令传输（<20ms）
  - 高频空间状态同步
  - 支持多用户AR空间的实时同步

**5. DistributedCollaborationAndSyncEngine (DCS-Engine)**
- **功能职责**: 多用户协同场景下的笔记空间状态同步、操作冲突解决、权限管理
- **主要输入**: 多用户操作事件、权限配置、冲突检测结果
- **主要输出**: 同步状态更新、冲突解决方案、权限验证结果
- **SoftBus依赖**:
  - 可靠的组播消息传递
  - 支持CRDT算法的分布式状态同步
  - 细粒度的设备间权限控制

**6. DistributedAICapabilityOrchestrator (DACO)**
- **功能职责**: 发现近场可用的分布式AI能力，智能调度AI子任务给最适合的能力节点
- **主要输入**: AI任务请求、设备能力清单、网络状态信息
- **主要输出**: 任务分配决策、负载均衡策略、结果聚合
- **SoftBus依赖**:
  - 动态服务发现和注册
  - 任务数据的安全分发
  - 结果收集和聚合的可靠传输

#### 3.1.2 模块间数据流转架构

```mermaid
graph TB
    subgraph "输入层"
        A[AR眼镜] --> MIFAE
        B[智能笔] --> MIFAE
        C[手机/平板] --> MIFAE
        D[语音输入] --> MIFAE
    end
    
    subgraph "SN-Core核心处理层"
        MIFAE --> SKG[SKG-Engine]
        SKG --> AIE2[AIE2]
        AIE2 --> SKG
        SKG --> XR[XR-ISM]
        SKG --> DCS[DCS-Engine]
        AIE2 --> DACO[DACO]
        DACO --> AIE2
        DCS --> SKG
    end
    
    subgraph "输出层"
        XR --> E[AR渲染]
        DCS --> F[多用户同步]
        DACO --> G[分布式AI节点]
    end
    
    subgraph "SoftBus传输层"
        H[P2P直连] 
        I[组播同步]
        J[服务发现]
    end
    
    MIFAE -.-> H
    SKG -.-> I
    DACO -.-> J
```

### 3.2 核心模块详细工作流程

#### 3.2.1 MultimodalInputFusionAndAtomizationEngine 工作流程

```mermaid
stateDiagram-v2
    [*] --> 接收SoftBus输入流
    接收SoftBus输入流 --> 输入类型识别
    输入类型识别 --> AR手势处理: AR_GESTURE
    输入类型识别 --> 语音处理: VOICE_COMMAND
    输入类型识别 --> 图像处理: IMAGE_ROI
    输入类型识别 --> 笔迹处理: PENCIL_STROKE
    
    AR手势处理 --> 手势语义解析
    语音处理 --> ASR和NLU
    图像处理 --> OCR和视觉理解
    笔迹处理 --> 笔迹识别和矢量化
    
    手势语义解析 --> 多模态融合
    ASR和NLU --> 多模态融合
    OCR和视觉理解 --> 多模态融合
    笔迹识别和矢量化 --> 多模态融合
    
    多模态融合 --> 原子化处理
    原子化处理 --> 语义标记
    语义标记 --> 输出NoteAtom
    输出NoteAtom --> [*]
```

**关键算法步骤：**
1. **输入流解析**: 根据SN_EVENT_STREAM的input_type字段路由到对应处理器
2. **时间戳对齐**: 确保多模态输入的时间同步（精度±5ms）
3. **上下文融合**: 结合current_xr_context信息进行语义消歧
4. **原子化规则**: 基于内容完整性和语义边界进行智能分割
5. **质量评估**: 对原子化结果进行置信度评分

**SoftBus交互协议：**
- **输入接收**: 通过SoftBus CoDHC通道接收高频数据流
- **AI能力调用**: 通过DACO模块调用分布式OCR/ASR服务
- **结果输出**: 将NoteAtom通过MESSAGE_BUS发送给SKG-Engine

#### 3.2.2 SpatialKnowledgeGraphEngine 工作流程

```mermaid
stateDiagram-v2
    [*] --> 接收NoteAtom
    接收NoteAtom --> 图谱索引更新
    图谱索引更新 --> 语义关联分析
    语义关联分析 --> 空间位置计算
    空间位置计算 --> 关系强度评估
    关系强度评估 --> 图谱结构优化
    图谱结构优化 --> 持久化存储
    持久化存储 --> 变更通知
    变更通知 --> [*]
    
    图谱索引更新 --> 查询处理: 外部查询
    查询处理 --> 结果排序
    结果排序 --> 返回查询结果
    返回查询结果 --> [*]
```

**核心算法：**
1. **语义嵌入**: 使用多模态Transformer生成768维语义向量
2. **关联发现**: 基于余弦相似度和图神经网络的关联强度计算
3. **空间布局**: 使用力导向算法优化AR空间中的原子位置
4. **增量更新**: 支持图结构的增量修改和版本管理

**SoftBus优化：**
- **分布式存储**: 图数据分片存储在多个设备上
- **一致性保证**: 使用CRDT确保多设备图状态一致
- **查询优化**: 本地缓存热点数据，减少SoftBus查询延迟

#### 3.2.3 AIInsightAndEmergenceEngine 工作流程

```mermaid
stateDiagram-v2
    [*] --> 知识图谱分析
    知识图谱分析 --> 模式识别
    模式识别 --> 缺口检测
    缺口检测 --> 关联推理
    关联推理 --> 洞察生成
    洞察生成 --> 置信度评估
    置信度评估 --> 个性化过滤
    个性化过滤 --> 输出智能建议
    输出智能建议 --> [*]
    
    知识图谱分析 --> 实时推荐: 用户查询
    实时推荐 --> 上下文适配
    上下文适配 --> 返回推荐结果
    返回推荐结果 --> [*]
```

**核心AI能力：**
1. **大语言模型推理**: 基于用户笔记内容进行深度语义理解
2. **图神经网络**: 发现复杂的多跳关联关系
3. **生成式模型**: 创建摘要、问题、创意建议
4. **个性化模型**: 学习用户偏好和思维模式

**分布式AI策略：**
- **本地轻量模型**: 在用户设备上运行小型推理模型
- **边缘重型计算**: 复杂推理任务卸载到边缘服务器
- **云端知识增强**: 可选的外部知识库查询和模型更新

### 3.3 核心颠覆性用户场景的详细交互时序

#### 3.3.1 场景一：AR智能手势OCR与空间贴附

```mermaid
sequenceDiagram
    participant U as 用户
    participant AR as AR眼镜
    participant SB as SoftBus
    participant MIFAE as MIFAE模块
    participant DACO as DACO模块
    participant OCR as 边缘OCR服务
    participant SKG as SKG-Engine
    participant XR as XR-ISM
    
    U->>AR: 注视文档+手势圈选
    AR->>SB: SN_EVENT_STREAM(AR_GESTURE) [<10ms]
    SB->>MIFAE: 传输手势数据 [<15ms]
    MIFAE->>DACO: 请求OCR能力
    DACO->>SB: 发现OCR服务 [<20ms]
    DACO->>OCR: 分发图像ROI [<30ms]
    OCR->>DACO: 返回文本结果 [<150ms]
    DACO->>SKG: 创建文本原子
    SKG->>XR: 空间位置计算
    XR->>SB: AR渲染指令 [<20ms]
    SB->>AR: 显示贴附文本 [<10ms]
    AR->>U: 可视化结果
    
    Note over U,AR: 总延迟 <250ms
```

#### 3.3.2 场景二：多人AR头脑风暴与灵感实时汇聚

```mermaid
sequenceDiagram
    participant U1 as 用户1
    participant U2 as 用户2
    participant AR1 as AR设备1
    participant AR2 as AR设备2
    participant SB as SoftBus
    participant DCS as DCS-Engine
    participant AIE2 as AIE2
    participant SKG as SKG-Engine
    
    U1->>AR1: 语音输入想法
    U2->>AR2: 手势创建连接
    
    par 并行处理
        AR1->>SB: 语音事件流
        AR2->>SB: 手势事件流
    end
    
    SB->>DCS: 多用户事件同步
    DCS->>SKG: 更新共享图谱
    SKG->>AIE2: 触发关联分析
    AIE2->>SKG: 返回智能建议
    SKG->>DCS: 广播状态更新
    
    par 同步渲染
        DCS->>AR1: 更新AR场景
        DCS->>AR2: 更新AR场景
    end
    
    AR1->>U1: 显示他人想法+AI建议
    AR2->>U2: 显示他人想法+AI建议
    
    Note over U1,U2: 协同延迟 <100ms
```

#### 3.3.3 场景三：AI基于笔记上下文主动推荐相关历史笔记并AR高亮

```mermaid
sequenceDiagram
    participant U as 用户
    participant AR as AR眼镜
    participant SB as SoftBus
    participant SKG as SKG-Engine
    participant AIE2 as AIE2
    participant XR as XR-ISM
    
    U->>AR: 正在查看当前笔记
    AR->>SB: 用户注视事件
    SB->>SKG: 查询当前上下文
    SKG->>AIE2: 请求关联推荐
    
    AIE2->>AIE2: 语义相似度计算
    AIE2->>AIE2: 时间关联分析
    AIE2->>AIE2: 用户偏好匹配
    
    AIE2->>SKG: 返回推荐列表
    SKG->>XR: 计算AR高亮位置
    XR->>SB: 生成渲染指令
    SB->>AR: 高亮相关笔记
    AR->>U: 显示智能推荐
    
    Note over U,AR: 推荐延迟 <200ms
    Note over AIE2: 推荐准确率 >85%
```

### 3.4 SoftBus在SN-Core工作流程中的关键作用分析

#### 3.4.1 低延迟数据流支撑

**关键要求：**
- AR手势识别：<20ms端到端延迟
- 多用户协同同步：<50ms状态一致性
- AI推荐响应：<200ms智能建议生成

**SoftBus优化策略：**
- **P2P直连**：绕过网络路由，直接设备间通信
- **数据压缩**：LZ4算法实现3:1压缩比，减少传输时间
- **优先级队列**：关键交互数据优先传输
- **预测性缓存**：基于用户行为模式预加载数据

#### 3.4.2 高带宽多媒体传输

**数据类型与带宽需求：**
- 高频手势数据：~2Mbps持续传输
- AR场景同步：~10Mbps峰值带宽
- 音视频原子：~50Mbps短时突发

**SoftBus带宽管理：**
- **自适应QoS**：根据网络状况动态调整数据质量
- **多通道并行**：同时使用WiFi和有线连接
- **智能分片**：大文件自动分片并行传输

#### 3.4.3 高同步精度保证

**同步挑战：**
- 多设备时钟偏差：±10ms
- 网络抖动影响：±5ms
- 处理延迟差异：±15ms

**SoftBus同步机制：**
- **全局时钟同步**：NTP协议校准设备时钟
- **向量时钟**：CRDT算法确保操作顺序一致性
- **补偿算法**：动态调整传输延迟补偿

#### 3.4.4 安全可靠的分布式通信

**安全要求：**
- 端到端加密：AES-256加密所有传输数据
- 设备认证：基于证书的设备身份验证
- 权限控制：细粒度的数据访问权限管理

**可靠性保证：**
- **重传机制**：关键数据包丢失自动重传
- **故障转移**：主通道故障时自动切换备用通道
- **数据完整性**：CRC校验确保数据传输正确性

### 3.5 交付物总结

1. **SN-Core详细模块化架构图**：已完成6个核心模块的清晰划分和接口定义
2. **每个核心模块的详细功能描述和工作流程图**：已完成MIFAE、SKG-Engine、AIE2等关键模块的详细设计
3. **3个核心颠覆性用户场景的详细时序图**：已完成AR OCR、多人协同、智能推荐三个场景的详细交互设计
4. **SoftBus关键作用分析报告**：已完成对低延迟、高带宽、高同步、安全可靠四个方面的详细分析
