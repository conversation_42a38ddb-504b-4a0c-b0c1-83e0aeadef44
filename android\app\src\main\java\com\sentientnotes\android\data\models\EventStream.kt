package com.sentientnotes.android.data.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import com.google.gson.annotations.SerializedName

/**
 * 灵境笔记事件流数据模型
 * 对应服务端的SN_EVENT_STREAM
 */

enum class InputType {
    @SerializedName("AR_GESTURE")
    AR_GESTURE,
    
    @SerializedName("PENCIL_STROKE")
    PENCIL_STROKE,
    
    @SerializedName("VOICE_COMMAND")
    VOICE_COMMAND,
    
    @SerializedName("IMAGE_ROI")
    IMAGE_ROI,
    
    @SerializedName("TEXT_INPUT")
    TEXT_INPUT,
    
    @SerializedName("SPATIAL_INTERACTION")
    SPATIAL_INTERACTION,
    
    @SerializedName("PHYSICAL_OBJECT")
    PHYSICAL_OBJECT,
    
    @SerializedName("BIOMETRIC_SIGNAL")
    BIOMETRIC_SIGNAL
}

@Parcelize
data class Pose6DOF(
    val position: List<Float>,
    val rotation: List<Float>
) : Parcelable

@Parcelize
data class HandKeypoint(
    @SerializedName("timestamp_us")
    val timestampUs: Long,
    
    @SerializedName("left_hand_keypoints")
    val leftHandKeypoints: List<List<Float>>? = null,
    
    @SerializedName("right_hand_keypoints")
    val rightHandKeypoints: List<List<Float>>? = null,
    
    @SerializedName("confidence_scores")
    val confidenceScores: List<Float>
) : Parcelable

@Parcelize
data class GestureTrajectory(
    @SerializedName("start_position")
    val startPosition: List<Float>,
    
    @SerializedName("end_position")
    val endPosition: List<Float>,
    
    @SerializedName("trajectory_points")
    val trajectoryPoints: List<List<Float>>
) : Parcelable

@Parcelize
data class SpatialRegion(
    @SerializedName("region_type")
    val regionType: String,
    
    val center: List<Float>,
    val dimensions: List<Float>
) : Parcelable

@Parcelize
data class UserGazeFocus(
    @SerializedName("gaze_direction")
    val gazeDirection: List<Float>,
    
    @SerializedName("focus_object_id")
    val focusObjectId: String? = null,
    
    @SerializedName("focus_confidence")
    val focusConfidence: Float
) : Parcelable

@Parcelize
data class ARGestureCommand(
    @SerializedName("hand_skeleton_sequence")
    val handSkeletonSequence: List<HandKeypoint>,
    
    @SerializedName("gesture_start_timestamp")
    val gestureStartTimestamp: Long,
    
    @SerializedName("gesture_end_timestamp")
    val gestureEndTimestamp: Long,
    
    @SerializedName("gesture_3d_space_vector")
    val gesture3dSpaceVector: GestureTrajectory,
    
    @SerializedName("target_object_id")
    val targetObjectId: String? = null,
    
    @SerializedName("target_spatial_region")
    val targetSpatialRegion: SpatialRegion? = null,
    
    @SerializedName("user_gaze_focus")
    val userGazeFocus: UserGazeFocus? = null
) : Parcelable

@Parcelize
data class PencilStrokePoint(
    @SerializedName("timestamp_us")
    val timestampUs: Long,
    
    val position: List<Float>,
    val pressure: Float,
    
    @SerializedName("tilt_x")
    val tiltX: Float? = null,
    
    @SerializedName("tilt_y")
    val tiltY: Float? = null,
    
    val azimuth: Float? = null
) : Parcelable

@Parcelize
data class PencilStroke(
    @SerializedName("stroke_id")
    val strokeId: String,
    
    @SerializedName("sampling_points")
    val samplingPoints: List<PencilStrokePoint>,
    
    @SerializedName("stroke_start_timestamp")
    val strokeStartTimestamp: Long,
    
    @SerializedName("stroke_end_timestamp")
    val strokeEndTimestamp: Long
) : Parcelable

@Parcelize
data class VirtualInkProperties(
    @SerializedName("color_rgba")
    val colorRgba: List<Float>,
    
    @SerializedName("brush_size")
    val brushSize: Float,
    
    @SerializedName("brush_type")
    val brushType: String
) : Parcelable

@Parcelize
data class WritingSurface(
    @SerializedName("surface_type")
    val surfaceType: String,
    
    @SerializedName("surface_id")
    val surfaceId: String,
    
    @SerializedName("texture_roughness")
    val textureRoughness: Float? = null,
    
    @SerializedName("friction_coefficient")
    val frictionCoefficient: Float? = null
) : Parcelable

@Parcelize
data class PencilStrokeSequence(
    @SerializedName("pencil_unique_id")
    val pencilUniqueId: String,
    
    @SerializedName("stroke_sequence")
    val strokeSequence: List<PencilStroke>,
    
    @SerializedName("virtual_ink_properties")
    val virtualInkProperties: VirtualInkProperties,
    
    @SerializedName("writing_surface")
    val writingSurface: WritingSurface,
    
    @SerializedName("sampling_rate_hz")
    val samplingRateHz: Float = 120.0f
) : Parcelable

@Parcelize
data class AudioStream(
    val format: String,
    
    @SerializedName("sample_rate")
    val sampleRate: Int,
    
    @SerializedName("bit_depth")
    val bitDepth: Int,
    
    val channels: Int,
    
    @SerializedName("duration_ms")
    val durationMs: Int,
    
    @SerializedName("audio_data_uri")
    val audioDataUri: String
) : Parcelable

@Parcelize
data class ASRResult(
    val transcription: String,
    
    @SerializedName("confidence_score")
    val confidenceScore: Float,
    
    @SerializedName("language_detected")
    val languageDetected: String,
    
    @SerializedName("processing_device_id")
    val processingDeviceId: String
) : Parcelable

@Parcelize
data class SpatialTarget(
    @SerializedName("target_type")
    val targetType: String,
    
    @SerializedName("target_id")
    val targetId: String,
    
    @SerializedName("target_position")
    val targetPosition: List<Float>
) : Parcelable

@Parcelize
data class PointingGesture(
    @SerializedName("is_pointing")
    val isPointing: Boolean,
    
    @SerializedName("pointing_direction")
    val pointingDirection: List<Float>? = null,
    
    @SerializedName("pointed_object_id")
    val pointedObjectId: String? = null
) : Parcelable

@Parcelize
data class NearbyNoteAtom(
    @SerializedName("atom_id")
    val atomId: String,
    
    @SerializedName("distance_from_user")
    val distanceFromUser: Float,
    
    @SerializedName("relevance_score")
    val relevanceScore: Float
) : Parcelable

@Parcelize
data class SpatialContext(
    @SerializedName("user_gaze_target")
    val userGazeTarget: SpatialTarget? = null,
    
    @SerializedName("pointing_gesture")
    val pointingGesture: PointingGesture? = null,
    
    @SerializedName("nearby_note_atoms")
    val nearbyNoteAtoms: List<NearbyNoteAtom> = emptyList()
) : Parcelable

@Parcelize
data class VoiceCommandWithSpatialContext(
    @SerializedName("audio_stream")
    val audioStream: AudioStream,
    
    @SerializedName("preliminary_asr_result")
    val preliminaryAsrResult: ASRResult? = null,
    
    @SerializedName("spatial_context")
    val spatialContext: SpatialContext? = null,
    
    @SerializedName("signal_to_noise_ratio")
    val signalToNoiseRatio: Float? = null,
    
    @SerializedName("background_noise_level")
    val backgroundNoiseLevel: Float? = null,
    
    @SerializedName("speech_clarity_score")
    val speechClarityScore: Float? = null
) : Parcelable

@Parcelize
data class TextInputWithContext(
    @SerializedName("text_content")
    val textContent: String,
    
    @SerializedName("input_method")
    val inputMethod: String,
    
    val language: String? = null,
    
    @SerializedName("context_tags")
    val contextTags: List<String> = emptyList()
) : Parcelable

@Parcelize
data class XRContext(
    @SerializedName("head_pose_6dof")
    val headPose6dof: Pose6DOF? = null,
    
    @SerializedName("left_hand_pose_6dof")
    val leftHandPose6dof: Pose6DOF? = null,
    
    @SerializedName("right_hand_pose_6dof")
    val rightHandPose6dof: Pose6DOF? = null,
    
    @SerializedName("current_ar_scene_id")
    val currentArSceneId: String? = null,
    
    @SerializedName("active_spatial_ui_elements")
    val activeSpatialUiElements: List<String> = emptyList(),
    
    @SerializedName("ambient_light_level")
    val ambientLightLevel: Float? = null,
    
    @SerializedName("dominant_light_direction")
    val dominantLightDirection: List<Float>? = null,
    
    @SerializedName("noise_level_db")
    val noiseLevelDb: Float? = null,
    
    @SerializedName("reverberation_time")
    val reverberationTime: Float? = null
) : Parcelable

@Parcelize
data class SoftBusTransmissionMetadata(
    @SerializedName("channel_type")
    val channelType: String,
    
    @SerializedName("compression_algorithm")
    val compressionAlgorithm: String = "LZ4",
    
    @SerializedName("encryption_enabled")
    val encryptionEnabled: Boolean = true,
    
    @SerializedName("transmission_priority")
    val transmissionPriority: Int = 5,
    
    @SerializedName("expected_data_size_bytes")
    val expectedDataSizeBytes: Int? = null
) : Parcelable

@Parcelize
data class SNEventStream(
    @SerializedName("event_id")
    val eventId: String,
    
    val timestamp: Long,
    
    @SerializedName("device_id")
    val deviceId: String,
    
    @SerializedName("user_id")
    val userId: String? = null,
    
    @SerializedName("input_type")
    val inputType: InputType,
    
    @SerializedName("input_data")
    val inputData: Any, // 根据inputType的不同，这里会是不同的数据类型
    
    @SerializedName("current_xr_context_optional")
    val currentXrContextOptional: XRContext? = null,
    
    @SerializedName("softbus_transmission_metadata")
    val softbusTransmissionMetadata: SoftBusTransmissionMetadata? = null
) : Parcelable
