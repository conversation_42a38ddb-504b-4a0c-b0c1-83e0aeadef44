/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef SOFTBUS_CORE_H
#define SOFTBUS_CORE_H

#include <stdint.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>

namespace android {

// 前向声明
class DiscoveryManager;
class ConnectionManager;
class TransmissionManager;
class DeviceManager;
class SecurityManager;
class BusCenterManager;

// 常量定义
constexpr int SOFTBUS_OK = 0;
constexpr int SOFTBUS_ERR = -1;
constexpr int SOFTBUS_INVALID_PARAM = -2;
constexpr int SOFTBUS_NO_INIT = -3;

constexpr size_t DEVICE_ID_SIZE_MAX = 65;
constexpr size_t DEVICE_NAME_SIZE_MAX = 129;
constexpr size_t CAPABILITY_SIZE_MAX = 513;
constexpr size_t SESSION_ID_SIZE_MAX = 65;

// 设备类型
enum DeviceType {
    DEVICE_TYPE_UNKNOWN = 0,
    DEVICE_TYPE_PHONE = 1,
    DEVICE_TYPE_TABLET = 2,
    DEVICE_TYPE_TV = 3,
    DEVICE_TYPE_PC = 4,
    DEVICE_TYPE_WATCH = 5,
    DEVICE_TYPE_CAR = 6,
    DEVICE_TYPE_SPEAKER = 7
};

// 发现模式
enum DiscoveryMode {
    DISCOVERY_MODE_PASSIVE = 0,
    DISCOVERY_MODE_ACTIVE = 1
};

// 传输介质
enum Medium {
    MEDIUM_WIFI = 1,
    MEDIUM_BLUETOOTH = 2,
    MEDIUM_BLE = 4,
    MEDIUM_USB = 8,
    MEDIUM_ALL = 0xFF
};

// 发现频率
enum ExchangeFreq {
    FREQ_LOW = 0,
    FREQ_MID = 1,
    FREQ_HIGH = 2,
    FREQ_SUPER_HIGH = 3
};

// 连接类型
enum LinkType {
    LINK_TYPE_WIFI = 0,
    LINK_TYPE_BR = 1,
    LINK_TYPE_BLE = 2,
    LINK_TYPE_P2P = 3,
    LINK_TYPE_USB = 4
};

// QoS类型
enum QosType {
    QOS_TYPE_MIN_BW = 0,
    QOS_TYPE_MAX_LATENCY = 1,
    QOS_TYPE_MIN_LATENCY = 2
};

// 数据类型
enum DataType {
    DATA_TYPE_BYTES = 0,
    DATA_TYPE_MESSAGE = 1,
    DATA_TYPE_FILE = 2,
    DATA_TYPE_STREAM = 3
};

// 连接状态
enum ConnectionState {
    CONNECTION_STATE_DISCONNECTED = 0,
    CONNECTION_STATE_CONNECTING = 1,
    CONNECTION_STATE_CONNECTED = 2,
    CONNECTION_STATE_DISCONNECTING = 3,
    CONNECTION_STATE_FAILED = 4
};

// 设备信息结构
struct DeviceInfo {
    char deviceId[DEVICE_ID_SIZE_MAX];
    char deviceName[DEVICE_NAME_SIZE_MAX];
    DeviceType deviceType;
    char capability[CAPABILITY_SIZE_MAX];
    uint32_t capabilityBitmap;
    bool isOnline;
    uint64_t timestamp;
    
    DeviceInfo() {
        memset(deviceId, 0, sizeof(deviceId));
        memset(deviceName, 0, sizeof(deviceName));
        memset(capability, 0, sizeof(capability));
        deviceType = DEVICE_TYPE_UNKNOWN;
        capabilityBitmap = 0;
        isOnline = false;
        timestamp = 0;
    }
};

// 发现配置结构
struct DiscoveryConfig {
    DiscoveryMode mode;
    Medium medium;
    ExchangeFreq freq;
    char capability[CAPABILITY_SIZE_MAX];
    uint32_t capabilityData;
    bool isSameAccount;
    bool isWakeRemote;
    
    DiscoveryConfig() {
        mode = DISCOVERY_MODE_ACTIVE;
        medium = MEDIUM_ALL;
        freq = FREQ_MID;
        memset(capability, 0, sizeof(capability));
        capabilityData = 0;
        isSameAccount = false;
        isWakeRemote = false;
    }
};

// 连接配置结构
struct ConnectionConfig {
    LinkType linkType;
    QosType qos;
    uint32_t timeout;
    bool isReliable;
    
    ConnectionConfig() {
        linkType = LINK_TYPE_WIFI;
        qos = QOS_TYPE_MIN_LATENCY;
        timeout = 30000; // 30秒
        isReliable = true;
    }
};

// 传输配置结构
struct TransmissionConfig {
    DataType dataType;
    uint32_t maxPacketSize;
    bool enableEncryption;
    bool enableCompression;
    
    TransmissionConfig() {
        dataType = DATA_TYPE_BYTES;
        maxPacketSize = 65536; // 64KB
        enableEncryption = true;
        enableCompression = false;
    }
};

// 回调函数类型定义
using DeviceFoundCallback = std::function<void(const DeviceInfo& device)>;
using ConnectionStateCallback = std::function<void(const std::string& sessionId, ConnectionState state, int reason)>;
using DataReceivedCallback = std::function<void(const std::string& sessionId, const uint8_t* data, uint32_t dataLen, DataType dataType)>;

// SoftBus回调结构
struct SoftBusCallbacks {
    std::function<void(const DeviceInfo* device)> onDeviceFound;
    std::function<void(const char* sessionId, int state, int reason)> onConnectionStateChanged;
    std::function<void(const char* sessionId, const uint8_t* data, uint32_t dataLen, int dataType)> onDataReceived;
};

/**
 * SoftBus核心类
 * 
 * 提供SoftBus的核心功能，包括设备发现、连接管理、数据传输等。
 * 基于OpenHarmony DSoftBus架构，适配Android平台。
 */
class SoftBusCore {
public:
    SoftBusCore();
    ~SoftBusCore();
    
    // 禁用拷贝构造和赋值
    SoftBusCore(const SoftBusCore&) = delete;
    SoftBusCore& operator=(const SoftBusCore&) = delete;
    
    /**
     * 初始化SoftBus核心
     * 
     * @param callbacks 回调函数结构
     * @return true 成功，false 失败
     */
    bool Initialize(const SoftBusCallbacks& callbacks);
    
    /**
     * 反初始化SoftBus核心
     */
    void Deinitialize();
    
    /**
     * 检查是否已初始化
     * 
     * @return true 已初始化，false 未初始化
     */
    bool IsInitialized() const { return mInitialized; }
    
    // ==================== 设备发现接口 ====================
    
    /**
     * 开始设备发现
     * 
     * @param config 发现配置
     * @return 发现ID，负数表示失败
     */
    int StartDiscovery(const DiscoveryConfig& config);
    
    /**
     * 停止设备发现
     * 
     * @param discoveryId 发现ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int StopDiscovery(int discoveryId);
    
    /**
     * 发布服务能力
     * 
     * @param capability 服务能力
     * @return 发布ID，负数表示失败
     */
    int PublishService(const std::string& capability);
    
    /**
     * 停止发布服务
     * 
     * @param publishId 发布ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int StopPublishService(int publishId);
    
    // ==================== 连接管理接口 ====================
    
    /**
     * 连接到指定设备
     * 
     * @param deviceId 设备ID
     * @param config 连接配置
     * @return 会话ID，空字符串表示失败
     */
    std::string ConnectDevice(const std::string& deviceId, const ConnectionConfig& config);
    
    /**
     * 断开设备连接
     * 
     * @param sessionId 会话ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int DisconnectDevice(const std::string& sessionId);
    
    /**
     * 创建会话服务器
     * 
     * @param sessionName 会话名称
     * @return 服务器ID，负数表示失败
     */
    int CreateSessionServer(const std::string& sessionName);
    
    /**
     * 移除会话服务器
     * 
     * @param serverId 服务器ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int RemoveSessionServer(int serverId);
    
    // ==================== 数据传输接口 ====================
    
    /**
     * 发送字节数据
     * 
     * @param sessionId 会话ID
     * @param data 数据指针
     * @param dataLen 数据长度
     * @return true 成功，false 失败
     */
    bool SendBytes(const std::string& sessionId, const uint8_t* data, uint32_t dataLen);
    
    /**
     * 发送消息
     * 
     * @param sessionId 会话ID
     * @param message 消息内容
     * @return true 成功，false 失败
     */
    bool SendMessage(const std::string& sessionId, const std::string& message);
    
    /**
     * 发送文件
     * 
     * @param sessionId 会话ID
     * @param filePath 文件路径
     * @return true 成功，false 失败
     */
    bool SendFile(const std::string& sessionId, const std::string& filePath);
    
    /**
     * 发送流数据
     * 
     * @param sessionId 会话ID
     * @param streamData 流数据
     * @param dataLen 数据长度
     * @return true 成功，false 失败
     */
    bool SendStream(const std::string& sessionId, const uint8_t* streamData, uint32_t dataLen);
    
    // ==================== 设备管理接口 ====================
    
    /**
     * 获取已连接的设备列表
     * 
     * @return 设备信息列表
     */
    std::vector<DeviceInfo> GetConnectedDevices() const;
    
    /**
     * 获取本地设备信息
     * 
     * @return 本地设备信息
     */
    DeviceInfo GetLocalDeviceInfo() const;
    
    /**
     * 获取指定设备信息
     * 
     * @param deviceId 设备ID
     * @return 设备信息，如果设备不存在则返回空的DeviceInfo
     */
    DeviceInfo GetDeviceInfo(const std::string& deviceId) const;
    
    // ==================== 网络管理接口 ====================
    
    /**
     * 加入LNN网络
     * 
     * @param networkId 网络ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int JoinLNN(const std::string& networkId);
    
    /**
     * 离开LNN网络
     * 
     * @param networkId 网络ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int LeaveLNN(const std::string& networkId);
    
    /**
     * 获取网络列表
     * 
     * @return 网络ID列表
     */
    std::vector<std::string> GetNetworkList() const;
    
    // ==================== 安全管理接口 ====================
    
    /**
     * 设置设备认证信息
     * 
     * @param deviceId 设备ID
     * @param authInfo 认证信息
     * @param authInfoLen 认证信息长度
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int SetDeviceAuthInfo(const std::string& deviceId, const uint8_t* authInfo, uint32_t authInfoLen);
    
    /**
     * 获取设备认证状态
     * 
     * @param deviceId 设备ID
     * @return 认证状态
     */
    int GetDeviceAuthState(const std::string& deviceId) const;
    
    /**
     * 清除设备认证信息
     * 
     * @param deviceId 设备ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int ClearDeviceAuthInfo(const std::string& deviceId);
    
private:
    bool mInitialized;
    SoftBusCallbacks mCallbacks;
    
    // 各个管理器实例
    std::unique_ptr<DiscoveryManager> mDiscoveryManager;
    std::unique_ptr<ConnectionManager> mConnectionManager;
    std::unique_ptr<TransmissionManager> mTransmissionManager;
    std::unique_ptr<DeviceManager> mDeviceManager;
    std::unique_ptr<SecurityManager> mSecurityManager;
    std::unique_ptr<BusCenterManager> mBusCenterManager;
    
    // 内部方法
    bool InitializeManagers();
    void DeinitializeManagers();
    std::string GenerateSessionId();
    
    // 静态计数器
    static int sNextDiscoveryId;
    static int sNextPublishId;
    static int sNextServerId;
    static int sNextSessionId;
};

} // namespace android

#endif // SOFTBUS_CORE_H
