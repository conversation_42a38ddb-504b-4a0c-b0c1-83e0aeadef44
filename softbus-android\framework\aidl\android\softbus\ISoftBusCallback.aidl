/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.softbus;

import android.softbus.device.DeviceInfo;

/**
 * SoftBus通用回调接口
 * 
 * 定义了SoftBus服务向客户端发送事件通知的回调接口。
 * 包括设备发现、连接状态变化、数据接收等事件。
 * 
 * @hide
 */
oneway interface ISoftBusCallback {
    
    /**
     * 设备发现回调
     * 
     * @param device 发现的设备信息
     */
    void onDeviceFound(in DeviceInfo device);
    
    /**
     * 设备丢失回调
     * 
     * @param deviceId 丢失的设备ID
     */
    void onDeviceLost(String deviceId);
    
    /**
     * 发现状态变化回调
     * 
     * @param discoveryId 发现ID
     * @param state 发现状态
     * @param reason 状态变化原因
     */
    void onDiscoveryStateChanged(int discoveryId, int state, int reason);
    
    /**
     * 连接状态变化回调
     * 
     * @param sessionId 会话ID
     * @param state 连接状态
     * @param reason 状态变化原因
     */
    void onConnectionStateChanged(String sessionId, int state, int reason);
    
    /**
     * 数据接收回调
     * 
     * @param sessionId 会话ID
     * @param data 接收的数据
     * @param dataType 数据类型
     */
    void onDataReceived(String sessionId, in byte[] data, int dataType);
    
    /**
     * 会话打开回调
     * 
     * @param sessionId 会话ID
     * @param result 打开结果
     */
    void onSessionOpened(String sessionId, int result);
    
    /**
     * 会话关闭回调
     * 
     * @param sessionId 会话ID
     * @param reason 关闭原因
     */
    void onSessionClosed(String sessionId, int reason);
    
    /**
     * 网络状态变化回调
     * 
     * @param networkId 网络ID
     * @param state 网络状态
     */
    void onNetworkStateChanged(String networkId, int state);
    
    /**
     * QoS事件回调
     * 
     * @param sessionId 会话ID
     * @param eventType 事件类型
     * @param eventData 事件数据
     */
    void onQosEvent(String sessionId, int eventType, in byte[] eventData);
    
    /**
     * 错误事件回调
     * 
     * @param errorCode 错误码
     * @param errorMessage 错误消息
     */
    void onError(int errorCode, String errorMessage);
}
