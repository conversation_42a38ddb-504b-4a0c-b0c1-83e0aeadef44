"""
SN-Core 核心引擎
灵境笔记的核心AI处理引擎，整合所有子模块
"""

import asyncio
import uuid
from typing import Optional, Dict, Any, List
from loguru import logger
import time

from models.events import SN_EVENT_STREAM, InputType
from models.responses import SN_ACTION_AND_STATE_UPDATE, UpdateType, SimpleResponse
from core.modules.mifae import MultimodalInputFusionAndAtomizationEngine
from core.modules.skg_engine import SpatialKnowledgeGraphEngine
from core.modules.aie2 import AIInsightAndEmergenceEngine
from core.modules.xr_ism import XRInteractionAndSpatialUIManager
from core.modules.dcs_engine import DistributedCollaborationAndSyncEngine
from core.modules.daco import DistributedAICapabilityOrchestrator
from core.config import settings


class SNCore:
    """SN-Core 核心引擎主类"""
    
    def __init__(self):
        self.initialized = False
        self.start_time = time.time()
        
        # 核心模块实例
        self.mifae: Optional[MultimodalInputFusionAndAtomizationEngine] = None
        self.skg_engine: Optional[SpatialKnowledgeGraphEngine] = None
        self.aie2: Optional[AIInsightAndEmergenceEngine] = None
        self.xr_ism: Optional[XRInteractionAndSpatialUIManager] = None
        self.dcs_engine: Optional[DistributedCollaborationAndSyncEngine] = None
        self.daco: Optional[DistributedAICapabilityOrchestrator] = None
        
        # 性能监控
        self.processing_stats = {
            "total_events_processed": 0,
            "average_processing_time_ms": 0.0,
            "last_processing_time_ms": 0.0,
            "error_count": 0
        }
        
    async def initialize(self):
        """初始化SN-Core引擎和所有子模块"""
        if self.initialized:
            logger.warning("SN-Core已经初始化，跳过重复初始化")
            return
            
        logger.info("开始初始化SN-Core引擎...")
        
        try:
            # 按依赖顺序初始化模块
            
            # 1. 初始化分布式AI能力协调器 (其他模块可能需要AI能力)
            logger.info("初始化DACO模块...")
            self.daco = DistributedAICapabilityOrchestrator()
            await self.daco.initialize()
            
            # 2. 初始化空间知识图谱引擎 (核心数据存储)
            logger.info("初始化SKG-Engine模块...")
            self.skg_engine = SpatialKnowledgeGraphEngine()
            await self.skg_engine.initialize()
            
            # 3. 初始化多模态输入融合引擎
            logger.info("初始化MIFAE模块...")
            self.mifae = MultimodalInputFusionAndAtomizationEngine(
                skg_engine=self.skg_engine,
                daco=self.daco
            )
            await self.mifae.initialize()
            
            # 4. 初始化AI洞察引擎
            logger.info("初始化AIE2模块...")
            self.aie2 = AIInsightAndEmergenceEngine(
                skg_engine=self.skg_engine,
                daco=self.daco
            )
            await self.aie2.initialize()
            
            # 5. 初始化XR交互管理器
            logger.info("初始化XR-ISM模块...")
            self.xr_ism = XRInteractionAndSpatialUIManager(
                skg_engine=self.skg_engine,
                aie2=self.aie2
            )
            await self.xr_ism.initialize()
            
            # 6. 初始化分布式协同引擎
            logger.info("初始化DCS-Engine模块...")
            self.dcs_engine = DistributedCollaborationAndSyncEngine(
                skg_engine=self.skg_engine
            )
            await self.dcs_engine.initialize()
            
            self.initialized = True
            logger.info("SN-Core引擎初始化完成")
            
        except Exception as e:
            logger.error(f"SN-Core引擎初始化失败: {e}")
            await self.shutdown()
            raise
    
    async def shutdown(self):
        """关闭SN-Core引擎和所有子模块"""
        logger.info("开始关闭SN-Core引擎...")
        
        # 按相反顺序关闭模块
        modules = [
            ("DCS-Engine", self.dcs_engine),
            ("XR-ISM", self.xr_ism),
            ("AIE2", self.aie2),
            ("MIFAE", self.mifae),
            ("SKG-Engine", self.skg_engine),
            ("DACO", self.daco)
        ]
        
        for module_name, module in modules:
            if module:
                try:
                    logger.info(f"关闭{module_name}模块...")
                    await module.shutdown()
                except Exception as e:
                    logger.error(f"关闭{module_name}模块时发生错误: {e}")
        
        self.initialized = False
        logger.info("SN-Core引擎关闭完成")
    
    async def process_event_stream(self, event_stream: SN_EVENT_STREAM) -> Optional[SN_ACTION_AND_STATE_UPDATE]:
        """处理输入事件流的主要方法"""
        if not self.initialized:
            raise RuntimeError("SN-Core引擎未初始化")
        
        start_time = time.time()
        
        try:
            logger.info(f"开始处理事件: {event_stream.event_id}, 类型: {event_stream.input_type}")
            
            # 更新统计信息
            self.processing_stats["total_events_processed"] += 1
            
            # 根据输入类型路由到相应的处理流程
            response = await self._route_event_processing(event_stream)
            
            # 计算处理时间
            processing_time = (time.time() - start_time) * 1000  # 转换为毫秒
            self.processing_stats["last_processing_time_ms"] = processing_time
            
            # 更新平均处理时间
            total_events = self.processing_stats["total_events_processed"]
            current_avg = self.processing_stats["average_processing_time_ms"]
            self.processing_stats["average_processing_time_ms"] = (
                (current_avg * (total_events - 1) + processing_time) / total_events
            )
            
            logger.info(f"事件处理完成: {event_stream.event_id}, 耗时: {processing_time:.2f}ms")
            
            return response
            
        except Exception as e:
            self.processing_stats["error_count"] += 1
            logger.error(f"处理事件流时发生错误: {e}")
            
            # 返回错误响应
            return SN_ACTION_AND_STATE_UPDATE(
                update_id=str(uuid.uuid4()),
                update_type=UpdateType.SYSTEM_STATE_CHANGE,
                target_devices=[event_stream.device_id],
                correlation_event_id=event_stream.event_id,
                system_notifications=[{
                    "notification_id": str(uuid.uuid4()),
                    "type": "ERROR",
                    "title": "事件处理失败",
                    "message": f"处理事件时发生错误: {str(e)}",
                    "action_required": False
                }]
            )
    
    async def _route_event_processing(self, event_stream: SN_EVENT_STREAM) -> Optional[SN_ACTION_AND_STATE_UPDATE]:
        """根据事件类型路由到相应的处理流程"""
        
        # 第一步：通过MIFAE进行多模态融合和原子化
        note_atoms = await self.mifae.process_input_stream(event_stream)
        
        if not note_atoms:
            logger.warning(f"MIFAE未能从事件中提取笔记原子: {event_stream.event_id}")
            return None
        
        # 第二步：将原子添加到知识图谱
        await self.skg_engine.add_note_atoms(note_atoms)
        
        # 第三步：触发AI洞察分析
        ai_insights = await self.aie2.analyze_new_atoms(note_atoms, event_stream)
        
        # 第四步：生成AR可视化指令
        ar_commands = await self.xr_ism.generate_visualization_commands(
            note_atoms, ai_insights, event_stream.current_xr_context_optional
        )
        
        # 第五步：处理协同同步（如果是多用户场景）
        sync_updates = None
        if event_stream.user_id and len(await self._get_active_users()) > 1:
            sync_updates = await self.dcs_engine.sync_collaborative_changes(
                note_atoms, event_stream.user_id, event_stream.device_id
            )
        
        # 构建响应
        response = SN_ACTION_AND_STATE_UPDATE(
            update_id=str(uuid.uuid4()),
            correlation_event_id=event_stream.event_id,
            update_type=UpdateType.NOTE_ATOM_CREATION,
            target_devices=[event_stream.device_id],
            note_atoms_updates=note_atoms,
            ar_visualization_commands_optional=ar_commands,
            system_notifications=[]
        )
        
        # 添加AI洞察通知
        if ai_insights:
            for insight in ai_insights:
                response.system_notifications.append({
                    "notification_id": str(uuid.uuid4()),
                    "type": "INFO",
                    "title": "AI洞察",
                    "message": insight.get("summary", "发现新的关联"),
                    "action_required": False,
                    "metadata": insight
                })
        
        # 如果有协同更新，添加到目标设备列表
        if sync_updates:
            active_users = await self._get_active_users()
            response.target_devices.extend([
                device_id for device_id in active_users 
                if device_id != event_stream.device_id
            ])
        
        return response
    
    async def _get_active_users(self) -> List[str]:
        """获取当前活跃用户列表"""
        # 这里应该从DCS-Engine获取活跃用户信息
        # 暂时返回空列表作为占位符
        if self.dcs_engine:
            return await self.dcs_engine.get_active_devices()
        return []
    
    async def get_health_status(self) -> Dict[str, Any]:
        """获取SN-Core健康状态"""
        if not self.initialized:
            return {"status": "not_initialized"}
        
        try:
            # 检查各个模块状态
            module_status = {}
            
            modules = [
                ("mifae", self.mifae),
                ("skg_engine", self.skg_engine),
                ("aie2", self.aie2),
                ("xr_ism", self.xr_ism),
                ("dcs_engine", self.dcs_engine),
                ("daco", self.daco)
            ]
            
            for module_name, module in modules:
                if module and hasattr(module, 'get_status'):
                    module_status[module_name] = await module.get_status()
                else:
                    module_status[module_name] = "unknown"
            
            return {
                "status": "healthy",
                "uptime_seconds": time.time() - self.start_time,
                "modules": module_status,
                "processing_stats": self.processing_stats,
                "memory_usage_mb": self._get_memory_usage(),
                "cpu_usage_percent": self._get_cpu_usage()
            }
            
        except Exception as e:
            logger.error(f"获取健康状态时发生错误: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    def _get_memory_usage(self) -> float:
        """获取内存使用情况（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        try:
            import psutil
            return psutil.cpu_percent(interval=0.1)
        except ImportError:
            return 0.0
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return self.processing_stats.copy()
    
    async def reset_stats(self):
        """重置统计信息"""
        self.processing_stats = {
            "total_events_processed": 0,
            "average_processing_time_ms": 0.0,
            "last_processing_time_ms": 0.0,
            "error_count": 0
        }
        logger.info("处理统计信息已重置")
