<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <!-- 音频权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <!-- 位置权限（用于AR功能） -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- 传感器权限 -->
    <uses-permission android:name="android.permission.BODY_SENSORS" />

    <!-- AR Core 功能 -->
    <uses-feature
        android:name="android.hardware.camera.ar"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.sensor.accelerometer"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.sensor.gyroscope"
        android:required="true" />

    <!-- 触控笔支持 -->
    <uses-feature
        android:name="android.hardware.touchscreen.multitouch"
        android:required="false" />

    <!-- 蓝牙权限（用于SoftBus） -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

    <!-- 近场通信权限 -->
    <uses-permission android:name="android.permission.NFC" />
    <uses-feature
        android:name="android.hardware.nfc"
        android:required="false" />

    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <!-- 唤醒锁权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:name=".SentientNotesApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SentientNotes"
        android:hardwareAccelerated="true"
        tools:targetApi="31">

        <!-- AR Core 元数据 -->
        <meta-data
            android:name="com.google.ar.core"
            android:value="optional" />

        <!-- 主Activity -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.SentientNotes"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|keyboardHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- AR Activity -->
        <activity
            android:name=".ui.ar.ARActivity"
            android:exported="false"
            android:theme="@style/Theme.SentientNotes.NoActionBar"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|keyboardHidden" />

        <!-- 相机Activity -->
        <activity
            android:name=".ui.camera.CameraActivity"
            android:exported="false"
            android:theme="@style/Theme.SentientNotes.NoActionBar"
            android:screenOrientation="portrait" />

        <!-- 笔记编辑Activity -->
        <activity
            android:name=".ui.notes.NoteEditActivity"
            android:exported="false"
            android:theme="@style/Theme.SentientNotes"
            android:windowSoftInputMode="adjustResize" />

        <!-- SoftBus连接服务 -->
        <service
            android:name=".services.SoftBusService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- AI处理服务 -->
        <service
            android:name=".services.AIProcessingService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- 多模态输入服务 -->
        <service
            android:name=".services.MultimodalInputService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="camera|microphone" />

        <!-- 文件提供者 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 广播接收器 -->
        <receiver
            android:name=".receivers.NetworkChangeReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <!-- 设备发现广播接收器 -->
        <receiver
            android:name=".receivers.DeviceDiscoveryReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.sentientnotes.DEVICE_DISCOVERED" />
                <action android:name="com.sentientnotes.DEVICE_LOST" />
            </intent-filter>
        </receiver>

    </application>

</manifest>
