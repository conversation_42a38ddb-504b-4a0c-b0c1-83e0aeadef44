package com.sentientnotes.android.softbus

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * SoftBus Native管理器
 * 负责与Native SoftBus库的交互和Android系统集成
 */
@Singleton
class SoftBusNativeManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "SoftBusNativeManager"
        private const val SOFTBUS_LIBRARY_NAME = "softbus_client"
        
        // 加载Native库
        init {
            try {
                System.loadLibrary(SOFTBUS_LIBRARY_NAME)
                Timber.i("SoftBus native library loaded successfully")
            } catch (e: UnsatisfiedLinkError) {
                Timber.e(e, "Failed to load SoftBus native library")
            }
        }
    }
    
    // 管理器状态
    private val _isInitialized = MutableStateFlow(false)
    val isInitialized: StateFlow<Boolean> = _isInitialized.asStateFlow()
    
    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()
    
    // 设备管理
    private val _discoveredDevices = MutableStateFlow<Map<String, DeviceInfo>>(emptyMap())
    val discoveredDevices: StateFlow<Map<String, DeviceInfo>> = _discoveredDevices.asStateFlow()
    
    private val _connectedDevices = MutableStateFlow<Map<String, SessionInfo>>(emptyMap())
    val connectedDevices: StateFlow<Map<String, SessionInfo>> = _connectedDevices.asStateFlow()
    
    // 内部状态
    private val deviceCache = ConcurrentHashMap<String, DeviceInfo>()
    private val sessionCache = ConcurrentHashMap<Int, SessionInfo>()
    private val discoveryCallbacks = ConcurrentHashMap<Int, DiscoveryCallback>()
    private val sessionCallbacks = ConcurrentHashMap<Int, SessionCallback>()
    
    // 协程作用域
    private val managerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 包名
    private val packageName: String by lazy {
        context.packageName
    }
    
    /**
     * 初始化SoftBus Native管理器
     */
    suspend fun initialize(): Result<Unit> = withContext(Dispatchers.IO) {
        if (_isInitialized.value) {
            return@withContext Result.success(Unit)
        }
        
        try {
            Timber.i("$TAG: 初始化SoftBus Native管理器...")
            
            // 初始化Native客户端
            val result = nativeInit(packageName)
            if (result != 0) {
                throw Exception("Native initialization failed with code: $result")
            }
            
            // 启动状态监控
            startStatusMonitoring()
            
            _isInitialized.value = true
            _isConnected.value = true
            
            Timber.i("$TAG: SoftBus Native管理器初始化完成")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: SoftBus Native管理器初始化失败")
            Result.failure(e)
        }
    }
    
    /**
     * 反初始化SoftBus Native管理器
     */
    suspend fun deinitialize(): Result<Unit> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.success(Unit)
        }
        
        try {
            Timber.i("$TAG: 反初始化SoftBus Native管理器...")
            
            // 停止所有发现
            stopAllDiscovery()
            
            // 关闭所有会话
            closeAllSessions()
            
            // 反初始化Native客户端
            nativeDeinit()
            
            // 清理状态
            deviceCache.clear()
            sessionCache.clear()
            discoveryCallbacks.clear()
            sessionCallbacks.clear()
            
            _isInitialized.value = false
            _isConnected.value = false
            
            Timber.i("$TAG: SoftBus Native管理器反初始化完成")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: SoftBus Native管理器反初始化失败")
            Result.failure(e)
        }
    }
    
    /**
     * 开始设备发现
     */
    suspend fun startDiscovery(
        subscribeInfo: SubscribeInfo,
        callback: DiscoveryCallback
    ): Result<Int> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            val subscribeId = nativeStartDiscovery(
                packageName,
                subscribeInfo.mode.ordinal,
                subscribeInfo.freq.ordinal,
                subscribeInfo.capability,
                subscribeInfo.capabilityData,
                subscribeInfo.isSameAccount,
                subscribeInfo.isWakeRemote
            )
            
            if (subscribeId > 0) {
                discoveryCallbacks[subscribeId] = callback
                Timber.i("$TAG: 设备发现已启动，subscribeId: $subscribeId")
                Result.success(subscribeId)
            } else {
                Result.failure(Exception("Failed to start discovery, error code: $subscribeId"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 启动设备发现失败")
            Result.failure(e)
        }
    }
    
    /**
     * 停止设备发现
     */
    suspend fun stopDiscovery(subscribeId: Int): Result<Unit> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            val result = nativeStopDiscovery(packageName, subscribeId)
            if (result == 0) {
                discoveryCallbacks.remove(subscribeId)
                Timber.i("$TAG: 设备发现已停止，subscribeId: $subscribeId")
                Result.success(Unit)
            } else {
                Result.failure(Exception("Failed to stop discovery, error code: $result"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 停止设备发现失败")
            Result.failure(e)
        }
    }
    
    /**
     * 发布服务
     */
    suspend fun publishService(
        publishInfo: PublishInfo,
        callback: PublishCallback
    ): Result<Int> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            val publishId = nativePublishService(
                packageName,
                publishInfo.publishId,
                publishInfo.mode.ordinal,
                publishInfo.freq.ordinal,
                publishInfo.capability,
                publishInfo.capabilityData,
                publishInfo.ranging
            )
            
            if (publishId > 0) {
                Timber.i("$TAG: 服务发布成功，publishId: $publishId")
                Result.success(publishId)
            } else {
                Result.failure(Exception("Failed to publish service, error code: $publishId"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 发布服务失败")
            Result.failure(e)
        }
    }
    
    /**
     * 停止发布服务
     */
    suspend fun stopPublishService(publishId: Int): Result<Unit> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            val result = nativeStopPublishService(packageName, publishId)
            if (result == 0) {
                Timber.i("$TAG: 服务发布已停止，publishId: $publishId")
                Result.success(Unit)
            } else {
                Result.failure(Exception("Failed to stop publish service, error code: $result"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 停止发布服务失败")
            Result.failure(e)
        }
    }
    
    /**
     * 创建会话服务器
     */
    suspend fun createSessionServer(
        sessionName: String,
        callback: SessionCallback
    ): Result<Unit> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            val result = nativeCreateSessionServer(packageName, sessionName)
            if (result == 0) {
                Timber.i("$TAG: 会话服务器创建成功: $sessionName")
                Result.success(Unit)
            } else {
                Result.failure(Exception("Failed to create session server, error code: $result"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 创建会话服务器失败")
            Result.failure(e)
        }
    }
    
    /**
     * 打开会话
     */
    suspend fun openSession(
        mySessionName: String,
        peerSessionName: String,
        peerNetworkId: String,
        groupId: String? = null,
        callback: SessionCallback
    ): Result<Int> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            val sessionId = nativeOpenSession(
                mySessionName,
                peerSessionName,
                peerNetworkId,
                groupId ?: "",
                0 // sessionType: TYPE_BYTES
            )
            
            if (sessionId > 0) {
                sessionCallbacks[sessionId] = callback
                val sessionInfo = SessionInfo(
                    sessionId = sessionId,
                    mySessionName = mySessionName,
                    peerSessionName = peerSessionName,
                    peerNetworkId = peerNetworkId,
                    groupId = groupId,
                    isConnected = true,
                    createTime = System.currentTimeMillis()
                )
                sessionCache[sessionId] = sessionInfo
                updateConnectedDevices()
                
                Timber.i("$TAG: 会话打开成功，sessionId: $sessionId")
                Result.success(sessionId)
            } else {
                Result.failure(Exception("Failed to open session, error code: $sessionId"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 打开会话失败")
            Result.failure(e)
        }
    }
    
    /**
     * 关闭会话
     */
    suspend fun closeSession(sessionId: Int): Result<Unit> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            nativeCloseSession(sessionId)
            sessionCallbacks.remove(sessionId)
            sessionCache.remove(sessionId)
            updateConnectedDevices()
            
            Timber.i("$TAG: 会话关闭成功，sessionId: $sessionId")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 关闭会话失败")
            Result.failure(e)
        }
    }
    
    /**
     * 发送数据
     */
    suspend fun sendBytes(
        sessionId: Int,
        data: ByteArray
    ): Result<Unit> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            val result = nativeSendBytes(sessionId, data, data.size)
            if (result == 0) {
                Timber.d("$TAG: 数据发送成功，sessionId: $sessionId, size: ${data.size}")
                Result.success(Unit)
            } else {
                Result.failure(Exception("Failed to send bytes, error code: $result"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 发送数据失败")
            Result.failure(e)
        }
    }
    
    /**
     * 获取本地设备信息
     */
    suspend fun getLocalDeviceInfo(): Result<DeviceInfo> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            val deviceInfo = nativeGetLocalDeviceInfo()
            if (deviceInfo != null) {
                Result.success(deviceInfo)
            } else {
                Result.failure(Exception("Failed to get local device info"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 获取本地设备信息失败")
            Result.failure(e)
        }
    }
    
    /**
     * 获取统计信息
     */
    suspend fun getStatistics(): Result<SoftBusStatistics> = withContext(Dispatchers.IO) {
        if (!_isInitialized.value) {
            return@withContext Result.failure(IllegalStateException("Manager not initialized"))
        }
        
        try {
            val stats = nativeGetStatistics()
            if (stats != null) {
                Result.success(stats)
            } else {
                Result.failure(Exception("Failed to get statistics"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 获取统计信息失败")
            Result.failure(e)
        }
    }
    
    // ==================== 私有方法 ====================
    
    private fun startStatusMonitoring() {
        managerScope.launch {
            while (isActive && _isInitialized.value) {
                try {
                    // 检查连接状态
                    val isConnected = nativeIsConnected()
                    _isConnected.value = isConnected
                    
                    delay(5000) // 每5秒检查一次
                } catch (e: Exception) {
                    Timber.e(e, "$TAG: 状态监控异常")
                }
            }
        }
    }
    
    private suspend fun stopAllDiscovery() {
        discoveryCallbacks.keys.forEach { subscribeId ->
            try {
                stopDiscovery(subscribeId)
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 停止发现失败: $subscribeId")
            }
        }
    }
    
    private suspend fun closeAllSessions() {
        sessionCache.keys.forEach { sessionId ->
            try {
                closeSession(sessionId)
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 关闭会话失败: $sessionId")
            }
        }
    }
    
    private fun updateDiscoveredDevices() {
        _discoveredDevices.value = deviceCache.toMap()
    }
    
    private fun updateConnectedDevices() {
        _connectedDevices.value = sessionCache.toMap()
    }
    
    // ==================== JNI回调方法 ====================
    
    /**
     * 设备发现回调（由Native层调用）
     */
    @Suppress("unused")
    private fun onDeviceFound(
        subscribeId: Int,
        deviceId: String,
        deviceName: String,
        deviceType: Int,
        capability: String,
        ipAddress: String,
        port: Int
    ) {
        managerScope.launch {
            try {
                val deviceInfo = DeviceInfo(
                    deviceId = deviceId,
                    deviceName = deviceName,
                    deviceType = DeviceType.values().getOrElse(deviceType) { DeviceType.UNKNOWN },
                    capability = capability,
                    ipAddress = ipAddress,
                    port = port,
                    isOnline = true,
                    lastSeen = System.currentTimeMillis()
                )
                
                deviceCache[deviceId] = deviceInfo
                updateDiscoveredDevices()
                
                discoveryCallbacks[subscribeId]?.onDeviceFound(deviceInfo)
                
                Timber.d("$TAG: 设备发现回调: $deviceName ($deviceId)")
                
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 处理设备发现回调失败")
            }
        }
    }
    
    /**
     * 会话打开回调（由Native层调用）
     */
    @Suppress("unused")
    private fun onSessionOpened(sessionId: Int, result: Int) {
        managerScope.launch {
            try {
                sessionCallbacks[sessionId]?.onSessionOpened(sessionId, result == 0)
                Timber.d("$TAG: 会话打开回调: sessionId=$sessionId, result=$result")
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 处理会话打开回调失败")
            }
        }
    }
    
    /**
     * 会话关闭回调（由Native层调用）
     */
    @Suppress("unused")
    private fun onSessionClosed(sessionId: Int) {
        managerScope.launch {
            try {
                sessionCallbacks[sessionId]?.onSessionClosed(sessionId)
                sessionCallbacks.remove(sessionId)
                sessionCache.remove(sessionId)
                updateConnectedDevices()
                
                Timber.d("$TAG: 会话关闭回调: sessionId=$sessionId")
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 处理会话关闭回调失败")
            }
        }
    }
    
    /**
     * 数据接收回调（由Native层调用）
     */
    @Suppress("unused")
    private fun onBytesReceived(sessionId: Int, data: ByteArray) {
        managerScope.launch {
            try {
                sessionCallbacks[sessionId]?.onBytesReceived(sessionId, data)
                Timber.d("$TAG: 数据接收回调: sessionId=$sessionId, size=${data.size}")
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 处理数据接收回调失败")
            }
        }
    }
    
    // ==================== Native方法声明 ====================
    
    private external fun nativeInit(packageName: String): Int
    private external fun nativeDeinit()
    private external fun nativeIsConnected(): Boolean
    
    private external fun nativeStartDiscovery(
        packageName: String,
        mode: Int,
        freq: Int,
        capability: String,
        capabilityData: Int,
        isSameAccount: Boolean,
        isWakeRemote: Boolean
    ): Int
    
    private external fun nativeStopDiscovery(packageName: String, subscribeId: Int): Int
    
    private external fun nativePublishService(
        packageName: String,
        publishId: Int,
        mode: Int,
        freq: Int,
        capability: String,
        capabilityData: Int,
        ranging: Boolean
    ): Int
    
    private external fun nativeStopPublishService(packageName: String, publishId: Int): Int
    
    private external fun nativeCreateSessionServer(packageName: String, sessionName: String): Int
    private external fun nativeRemoveSessionServer(packageName: String, sessionName: String): Int
    
    private external fun nativeOpenSession(
        mySessionName: String,
        peerSessionName: String,
        peerNetworkId: String,
        groupId: String,
        sessionType: Int
    ): Int
    
    private external fun nativeCloseSession(sessionId: Int)
    private external fun nativeSendBytes(sessionId: Int, data: ByteArray, len: Int): Int
    
    private external fun nativeGetLocalDeviceInfo(): DeviceInfo?
    private external fun nativeGetStatistics(): SoftBusStatistics?
}
