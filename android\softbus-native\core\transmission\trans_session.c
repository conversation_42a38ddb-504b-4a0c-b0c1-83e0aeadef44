/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * Transmission Session - 传输会话管理实现
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#include "trans_session.h"
#include "softbus_common.h"
#include "session.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <errno.h>

#define MAX_SESSION_SERVERS 32
#define MAX_SESSIONS 128
#define SESSION_TIMEOUT_MS 300000  // 5分钟
#define MAX_PACKET_SIZE 65536

/* 会话服务器信息 */
typedef struct {
    bool isUsed;
    char pkgName[PKG_NAME_SIZE_MAX];
    char sessionName[SESSION_NAME_SIZE_MAX];
    ISessionListener listener;
    uint64_t createTime;
} SessionServer;

/* 会话信息 */
typedef struct {
    bool isUsed;
    int32_t sessionId;
    char mySessionName[SESSION_NAME_SIZE_MAX];
    char peerSessionName[SESSION_NAME_SIZE_MAX];
    char peerNetworkId[NETWORK_ID_SIZE_MAX];
    char groupId[GROUP_ID_SIZE_MAX];
    SessionType sessionType;
    SessionAttribute attr;
    uint32_t connectionId;
    bool isServer;
    uint64_t createTime;
    uint64_t lastActiveTime;
    uint64_t totalBytesSent;
    uint64_t totalBytesReceived;
    ISessionListener* listener;
    pthread_mutex_t sessionMutex;
} SessionInfo;

/* 传输管理器上下文 */
typedef struct {
    bool isInitialized;
    pthread_mutex_t mutex;
    SessionServer sessionServers[MAX_SESSION_SERVERS];
    SessionInfo sessions[MAX_SESSIONS];
    int32_t nextSessionId;
    uint32_t totalSessions;
    uint64_t totalDataTransmitted;
} TransmissionManager;

static TransmissionManager g_transMgr = {0};

/* 获取当前时间戳（毫秒） */
static uint64_t GetCurrentTimeMs(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

/* 查找空闲的会话服务器槽 */
static SessionServer* FindFreeSessionServer(void) {
    for (int32_t i = 0; i < MAX_SESSION_SERVERS; i++) {
        if (!g_transMgr.sessionServers[i].isUsed) {
            return &g_transMgr.sessionServers[i];
        }
    }
    return NULL;
}

/* 根据包名和会话名查找会话服务器 */
static SessionServer* FindSessionServer(const char* pkgName, const char* sessionName) {
    for (int32_t i = 0; i < MAX_SESSION_SERVERS; i++) {
        SessionServer* server = &g_transMgr.sessionServers[i];
        if (server->isUsed && 
            strcmp(server->pkgName, pkgName) == 0 &&
            strcmp(server->sessionName, sessionName) == 0) {
            return server;
        }
    }
    return NULL;
}

/* 查找空闲的会话槽 */
static SessionInfo* FindFreeSession(void) {
    for (int32_t i = 0; i < MAX_SESSIONS; i++) {
        if (!g_transMgr.sessions[i].isUsed) {
            return &g_transMgr.sessions[i];
        }
    }
    return NULL;
}

/* 根据会话ID查找会话 */
static SessionInfo* FindSession(int32_t sessionId) {
    for (int32_t i = 0; i < MAX_SESSIONS; i++) {
        if (g_transMgr.sessions[i].isUsed && 
            g_transMgr.sessions[i].sessionId == sessionId) {
            return &g_transMgr.sessions[i];
        }
    }
    return NULL;
}

/* 生成会话ID */
static int32_t GenerateSessionId(void) {
    return g_transMgr.nextSessionId++;
}

/* 模拟连接建立 */
static uint32_t SimulateConnection(const char* peerNetworkId) {
    /* 这里应该调用连接管理器建立实际连接 */
    /* 为了简化，我们返回一个模拟的连接ID */
    static uint32_t mockConnectionId = 1000;
    (void)peerNetworkId;
    return mockConnectionId++;
}

/* 初始化传输管理器 */
int32_t InitTransmissionManager(void) {
    if (g_transMgr.isInitialized) {
        return SOFTBUS_OK;
    }
    
    memset(&g_transMgr, 0, sizeof(g_transMgr));
    
    if (pthread_mutex_init(&g_transMgr.mutex, NULL) != 0) {
        SOFTBUS_LOG_ERROR("Failed to initialize transmission mutex");
        return SOFTBUS_ERR;
    }
    
    /* 初始化所有会话的互斥锁 */
    for (int32_t i = 0; i < MAX_SESSIONS; i++) {
        if (pthread_mutex_init(&g_transMgr.sessions[i].sessionMutex, NULL) != 0) {
            SOFTBUS_LOG_ERROR("Failed to initialize session mutex %d", i);
            /* 清理已初始化的互斥锁 */
            for (int32_t j = 0; j < i; j++) {
                pthread_mutex_destroy(&g_transMgr.sessions[j].sessionMutex);
            }
            pthread_mutex_destroy(&g_transMgr.mutex);
            return SOFTBUS_ERR;
        }
    }
    
    g_transMgr.nextSessionId = 1;
    g_transMgr.isInitialized = true;
    
    SOFTBUS_LOG_INFO("Transmission manager initialized");
    return SOFTBUS_OK;
}

/* 反初始化传输管理器 */
void DeinitTransmissionManager(void) {
    if (!g_transMgr.isInitialized) {
        return;
    }
    
    /* 关闭所有会话 */
    pthread_mutex_lock(&g_transMgr.mutex);
    for (int32_t i = 0; i < MAX_SESSIONS; i++) {
        if (g_transMgr.sessions[i].isUsed) {
            CloseSession(g_transMgr.sessions[i].sessionId);
        }
    }
    pthread_mutex_unlock(&g_transMgr.mutex);
    
    /* 销毁所有互斥锁 */
    for (int32_t i = 0; i < MAX_SESSIONS; i++) {
        pthread_mutex_destroy(&g_transMgr.sessions[i].sessionMutex);
    }
    
    pthread_mutex_destroy(&g_transMgr.mutex);
    
    memset(&g_transMgr, 0, sizeof(g_transMgr));
    
    SOFTBUS_LOG_INFO("Transmission manager deinitialized");
}

/* 创建会话服务器 */
int32_t CreateSessionServer(const char* pkgName, const char* sessionName, 
                           const ISessionListener* listener) {
    if (!g_transMgr.isInitialized || pkgName == NULL || 
        sessionName == NULL || listener == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_transMgr.mutex);
    
    /* 检查是否已存在 */
    SessionServer* existingServer = FindSessionServer(pkgName, sessionName);
    if (existingServer != NULL) {
        pthread_mutex_unlock(&g_transMgr.mutex);
        SOFTBUS_LOG_ERROR("Session server already exists: %s:%s", pkgName, sessionName);
        return SOFTBUS_ERR;
    }
    
    /* 查找空闲槽 */
    SessionServer* server = FindFreeSessionServer();
    if (server == NULL) {
        pthread_mutex_unlock(&g_transMgr.mutex);
        SOFTBUS_LOG_ERROR("No free session server slot available");
        return SOFTBUS_ERR;
    }
    
    /* 初始化会话服务器 */
    memset(server, 0, sizeof(SessionServer));
    server->isUsed = true;
    strncpy(server->pkgName, pkgName, sizeof(server->pkgName) - 1);
    strncpy(server->sessionName, sessionName, sizeof(server->sessionName) - 1);
    memcpy(&server->listener, listener, sizeof(ISessionListener));
    server->createTime = GetCurrentTimeMs();
    
    pthread_mutex_unlock(&g_transMgr.mutex);
    
    SOFTBUS_LOG_INFO("Session server created: %s:%s", pkgName, sessionName);
    return SOFTBUS_OK;
}

/* 移除会话服务器 */
int32_t RemoveSessionServer(const char* pkgName, const char* sessionName) {
    if (!g_transMgr.isInitialized || pkgName == NULL || sessionName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_transMgr.mutex);
    
    SessionServer* server = FindSessionServer(pkgName, sessionName);
    if (server == NULL) {
        pthread_mutex_unlock(&g_transMgr.mutex);
        SOFTBUS_LOG_ERROR("Session server not found: %s:%s", pkgName, sessionName);
        return SOFTBUS_ERR;
    }
    
    /* 关闭相关的会话 */
    for (int32_t i = 0; i < MAX_SESSIONS; i++) {
        SessionInfo* session = &g_transMgr.sessions[i];
        if (session->isUsed && session->isServer &&
            strcmp(session->mySessionName, sessionName) == 0) {
            CloseSession(session->sessionId);
        }
    }
    
    /* 清理服务器信息 */
    SOFTBUS_LOG_INFO("Session server removed: %s:%s", pkgName, sessionName);
    memset(server, 0, sizeof(SessionServer));
    
    pthread_mutex_unlock(&g_transMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 打开会话 */
int32_t OpenSession(const char* mySessionName, const char* peerSessionName, 
                   const char* peerNetworkId, const char* groupId, 
                   const SessionAttribute* attr) {
    if (!g_transMgr.isInitialized || mySessionName == NULL || 
        peerSessionName == NULL || peerNetworkId == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_transMgr.mutex);
    
    /* 查找空闲会话槽 */
    SessionInfo* session = FindFreeSession();
    if (session == NULL) {
        pthread_mutex_unlock(&g_transMgr.mutex);
        SOFTBUS_LOG_ERROR("No free session slot available");
        return SOFTBUS_ERR;
    }
    
    /* 查找对应的会话服务器 */
    SessionServer* server = NULL;
    for (int32_t i = 0; i < MAX_SESSION_SERVERS; i++) {
        if (g_transMgr.sessionServers[i].isUsed &&
            strcmp(g_transMgr.sessionServers[i].sessionName, mySessionName) == 0) {
            server = &g_transMgr.sessionServers[i];
            break;
        }
    }
    
    /* 初始化会话信息 */
    memset(session, 0, sizeof(SessionInfo));
    session->isUsed = true;
    session->sessionId = GenerateSessionId();
    strncpy(session->mySessionName, mySessionName, sizeof(session->mySessionName) - 1);
    strncpy(session->peerSessionName, peerSessionName, sizeof(session->peerSessionName) - 1);
    strncpy(session->peerNetworkId, peerNetworkId, sizeof(session->peerNetworkId) - 1);
    
    if (groupId != NULL) {
        strncpy(session->groupId, groupId, sizeof(session->groupId) - 1);
    }
    
    if (attr != NULL) {
        memcpy(&session->attr, attr, sizeof(SessionAttribute));
        session->sessionType = attr->attr;
    } else {
        session->sessionType = TYPE_BYTES;
    }
    
    session->isServer = false;
    session->createTime = GetCurrentTimeMs();
    session->lastActiveTime = session->createTime;
    session->listener = server ? &server->listener : NULL;
    
    /* 建立连接 */
    session->connectionId = SimulateConnection(peerNetworkId);
    if (session->connectionId == 0) {
        memset(session, 0, sizeof(SessionInfo));
        pthread_mutex_unlock(&g_transMgr.mutex);
        SOFTBUS_LOG_ERROR("Failed to establish connection to %s", peerNetworkId);
        return SOFTBUS_ERR;
    }
    
    int32_t sessionId = session->sessionId;
    g_transMgr.totalSessions++;
    
    pthread_mutex_unlock(&g_transMgr.mutex);
    
    /* 调用会话打开回调 */
    if (session->listener && session->listener->OnSessionOpened) {
        int32_t result = session->listener->OnSessionOpened(sessionId, SOFTBUS_OK);
        if (result != SOFTBUS_OK) {
            CloseSession(sessionId);
            return SOFTBUS_ERR;
        }
    }
    
    SOFTBUS_LOG_INFO("Session opened: sessionId=%d, peer=%s", sessionId, peerNetworkId);
    return sessionId;
}

/* 关闭会话 */
void CloseSession(int32_t sessionId) {
    if (!g_transMgr.isInitialized) {
        return;
    }
    
    pthread_mutex_lock(&g_transMgr.mutex);
    
    SessionInfo* session = FindSession(sessionId);
    if (session == NULL) {
        pthread_mutex_unlock(&g_transMgr.mutex);
        SOFTBUS_LOG_ERROR("Session not found: %d", sessionId);
        return;
    }
    
    pthread_mutex_lock(&session->sessionMutex);
    
    /* 调用会话关闭回调 */
    if (session->listener && session->listener->OnSessionClosed) {
        session->listener->OnSessionClosed(sessionId);
    }
    
    /* 记录统计信息 */
    uint64_t duration = GetCurrentTimeMs() - session->createTime;
    SOFTBUS_LOG_INFO("Session closed: sessionId=%d, duration=%llu ms, sent=%llu bytes, received=%llu bytes",
                     sessionId, duration, session->totalBytesSent, session->totalBytesReceived);
    
    /* 清理会话信息 */
    memset(session, 0, sizeof(SessionInfo));
    
    pthread_mutex_unlock(&session->sessionMutex);
    pthread_mutex_unlock(&g_transMgr.mutex);
}

/* 发送字节数据 */
int32_t SendBytes(int32_t sessionId, const void* data, uint32_t len) {
    if (!g_transMgr.isInitialized || data == NULL || len == 0) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    if (len > MAX_PACKET_SIZE) {
        SOFTBUS_LOG_ERROR("Data size too large: %u > %d", len, MAX_PACKET_SIZE);
        return SOFTBUS_ERR;
    }
    
    pthread_mutex_lock(&g_transMgr.mutex);
    
    SessionInfo* session = FindSession(sessionId);
    if (session == NULL) {
        pthread_mutex_unlock(&g_transMgr.mutex);
        SOFTBUS_LOG_ERROR("Session not found: %d", sessionId);
        return SOFTBUS_ERR;
    }
    
    pthread_mutex_lock(&session->sessionMutex);
    pthread_mutex_unlock(&g_transMgr.mutex);
    
    /* 模拟数据发送 */
    /* 在实际实现中，这里会通过连接管理器发送数据 */
    session->totalBytesSent += len;
    session->lastActiveTime = GetCurrentTimeMs();
    g_transMgr.totalDataTransmitted += len;
    
    pthread_mutex_unlock(&session->sessionMutex);
    
    SOFTBUS_LOG_DEBUG("Sent %u bytes on session %d", len, sessionId);
    return SOFTBUS_OK;
}

/* 发送消息 */
int32_t SendMessage(int32_t sessionId, const void* data, uint32_t len) {
    /* 消息发送与字节发送类似，但可能有不同的处理逻辑 */
    return SendBytes(sessionId, data, len);
}

/* 获取会话名称 */
int32_t GetMySessionName(int32_t sessionId, char* sessionName, uint32_t len) {
    if (!g_transMgr.isInitialized || sessionName == NULL || len == 0) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_transMgr.mutex);
    
    SessionInfo* session = FindSession(sessionId);
    if (session == NULL) {
        pthread_mutex_unlock(&g_transMgr.mutex);
        return SOFTBUS_ERR;
    }
    
    strncpy(sessionName, session->mySessionName, len - 1);
    sessionName[len - 1] = '\0';
    
    pthread_mutex_unlock(&g_transMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 获取对端会话名称 */
int32_t GetPeerSessionName(int32_t sessionId, char* sessionName, uint32_t len) {
    if (!g_transMgr.isInitialized || sessionName == NULL || len == 0) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_transMgr.mutex);
    
    SessionInfo* session = FindSession(sessionId);
    if (session == NULL) {
        pthread_mutex_unlock(&g_transMgr.mutex);
        return SOFTBUS_ERR;
    }
    
    strncpy(sessionName, session->peerSessionName, len - 1);
    sessionName[len - 1] = '\0';
    
    pthread_mutex_unlock(&g_transMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 获取对端设备ID */
int32_t GetPeerDeviceId(int32_t sessionId, char* networkId, uint32_t len) {
    if (!g_transMgr.isInitialized || networkId == NULL || len == 0) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_transMgr.mutex);
    
    SessionInfo* session = FindSession(sessionId);
    if (session == NULL) {
        pthread_mutex_unlock(&g_transMgr.mutex);
        return SOFTBUS_ERR;
    }
    
    strncpy(networkId, session->peerNetworkId, len - 1);
    networkId[len - 1] = '\0';
    
    pthread_mutex_unlock(&g_transMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 获取传输统计信息 */
int32_t GetTransmissionStatistics(TransmissionStatistics* stats) {
    if (!g_transMgr.isInitialized || stats == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_transMgr.mutex);
    
    memset(stats, 0, sizeof(TransmissionStatistics));
    stats->totalSessions = g_transMgr.totalSessions;
    stats->totalDataTransmitted = g_transMgr.totalDataTransmitted;
    
    /* 统计当前活跃会话 */
    for (int32_t i = 0; i < MAX_SESSIONS; i++) {
        if (g_transMgr.sessions[i].isUsed) {
            stats->activeSessions++;
        }
    }
    
    /* 统计活跃服务器 */
    for (int32_t i = 0; i < MAX_SESSION_SERVERS; i++) {
        if (g_transMgr.sessionServers[i].isUsed) {
            stats->activeServers++;
        }
    }
    
    pthread_mutex_unlock(&g_transMgr.mutex);
    
    return SOFTBUS_OK;
}
