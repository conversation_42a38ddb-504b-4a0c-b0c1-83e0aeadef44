/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * SoftBus JNI Implementation - JNI接口实现
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#include <jni.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <android/log.h>

#include "softbus_common.h"
#include "softbus_bus_center.h"
#include "session.h"
#include "softbus_client.h"

#define LOG_TAG "SoftBusJNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

/* JNI全局引用 */
static JavaVM* g_jvm = NULL;
static jobject g_softBusManager = NULL;
static jclass g_softBusManagerClass = NULL;

/* 回调方法ID */
static jmethodID g_onDeviceFoundMethod = NULL;
static jmethodID g_onSessionOpenedMethod = NULL;
static jmethodID g_onSessionClosedMethod = NULL;
static jmethodID g_onBytesReceivedMethod = NULL;

/* 互斥锁 */
static pthread_mutex_t g_jniMutex = PTHREAD_MUTEX_INITIALIZER;

/* 获取JNI环境 */
static JNIEnv* GetJNIEnv(void) {
    JNIEnv* env = NULL;
    if (g_jvm == NULL) {
        return NULL;
    }
    
    int status = (*g_jvm)->GetEnv(g_jvm, (void**)&env, JNI_VERSION_1_6);
    if (status == JNI_EDETACHED) {
        status = (*g_jvm)->AttachCurrentThread(g_jvm, &env, NULL);
        if (status != JNI_OK) {
            LOGE("Failed to attach current thread");
            return NULL;
        }
    } else if (status != JNI_OK) {
        LOGE("Failed to get JNI environment");
        return NULL;
    }
    
    return env;
}

/* 设备发现回调 */
static void OnDeviceFoundCallback(const DeviceFoundInfo* device) {
    if (device == NULL || g_softBusManager == NULL || g_onDeviceFoundMethod == NULL) {
        return;
    }
    
    JNIEnv* env = GetJNIEnv();
    if (env == NULL) {
        return;
    }
    
    pthread_mutex_lock(&g_jniMutex);
    
    /* 创建Java字符串 */
    jstring jDeviceId = (*env)->NewStringUTF(env, device->deviceId);
    jstring jDeviceName = (*env)->NewStringUTF(env, device->deviceName);
    jstring jCapability = (*env)->NewStringUTF(env, device->capability);
    jstring jIpAddress = (*env)->NewStringUTF(env, "*************"); // 模拟IP地址
    
    /* 调用Java回调方法 */
    (*env)->CallVoidMethod(env, g_softBusManager, g_onDeviceFoundMethod,
                          (jint)device->subscribeId,
                          jDeviceId,
                          jDeviceName,
                          (jint)device->deviceTypeId,
                          jCapability,
                          jIpAddress,
                          (jint)8080);
    
    /* 清理本地引用 */
    (*env)->DeleteLocalRef(env, jDeviceId);
    (*env)->DeleteLocalRef(env, jDeviceName);
    (*env)->DeleteLocalRef(env, jCapability);
    (*env)->DeleteLocalRef(env, jIpAddress);
    
    pthread_mutex_unlock(&g_jniMutex);
    
    LOGD("Device found callback called: %s", device->deviceName);
}

/* 会话打开回调 */
static int32_t OnSessionOpenedCallback(int32_t sessionId, int32_t result) {
    if (g_softBusManager == NULL || g_onSessionOpenedMethod == NULL) {
        return SOFTBUS_ERR;
    }
    
    JNIEnv* env = GetJNIEnv();
    if (env == NULL) {
        return SOFTBUS_ERR;
    }
    
    pthread_mutex_lock(&g_jniMutex);
    
    /* 调用Java回调方法 */
    (*env)->CallVoidMethod(env, g_softBusManager, g_onSessionOpenedMethod,
                          (jint)sessionId, (jint)result);
    
    pthread_mutex_unlock(&g_jniMutex);
    
    LOGD("Session opened callback called: sessionId=%d, result=%d", sessionId, result);
    return SOFTBUS_OK;
}

/* 会话关闭回调 */
static void OnSessionClosedCallback(int32_t sessionId) {
    if (g_softBusManager == NULL || g_onSessionClosedMethod == NULL) {
        return;
    }
    
    JNIEnv* env = GetJNIEnv();
    if (env == NULL) {
        return;
    }
    
    pthread_mutex_lock(&g_jniMutex);
    
    /* 调用Java回调方法 */
    (*env)->CallVoidMethod(env, g_softBusManager, g_onSessionClosedMethod,
                          (jint)sessionId);
    
    pthread_mutex_unlock(&g_jniMutex);
    
    LOGD("Session closed callback called: sessionId=%d", sessionId);
}

/* 数据接收回调 */
static void OnBytesReceivedCallback(int32_t sessionId, const void* data, uint32_t dataLen) {
    if (data == NULL || dataLen == 0 || g_softBusManager == NULL || g_onBytesReceivedMethod == NULL) {
        return;
    }
    
    JNIEnv* env = GetJNIEnv();
    if (env == NULL) {
        return;
    }
    
    pthread_mutex_lock(&g_jniMutex);
    
    /* 创建Java字节数组 */
    jbyteArray jData = (*env)->NewByteArray(env, dataLen);
    if (jData != NULL) {
        (*env)->SetByteArrayRegion(env, jData, 0, dataLen, (const jbyte*)data);
        
        /* 调用Java回调方法 */
        (*env)->CallVoidMethod(env, g_softBusManager, g_onBytesReceivedMethod,
                              (jint)sessionId, jData);
        
        (*env)->DeleteLocalRef(env, jData);
    }
    
    pthread_mutex_unlock(&g_jniMutex);
    
    LOGD("Bytes received callback called: sessionId=%d, dataLen=%u", sessionId, dataLen);
}

/* 发现回调结构 */
static IDiscoveryCallback g_discoveryCallback = {
    .OnDeviceFound = OnDeviceFoundCallback,
    .OnDiscoverFailed = NULL,
    .OnDiscoverySuccess = NULL
};

/* 会话监听器结构 */
static ISessionListener g_sessionListener = {
    .OnSessionOpened = OnSessionOpenedCallback,
    .OnSessionClosed = OnSessionClosedCallback,
    .OnBytesReceived = OnBytesReceivedCallback,
    .OnMessageReceived = NULL,
    .OnStreamReceived = NULL,
    .OnQosEvent = NULL
};

/* JNI_OnLoad */
JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    (void)reserved;
    
    g_jvm = vm;
    
    JNIEnv* env = NULL;
    if ((*vm)->GetEnv(vm, (void**)&env, JNI_VERSION_1_6) != JNI_OK) {
        return JNI_ERR;
    }
    
    LOGI("SoftBus JNI library loaded");
    return JNI_VERSION_1_6;
}

/* JNI_OnUnload */
JNIEXPORT void JNICALL JNI_OnUnload(JavaVM* vm, void* reserved) {
    (void)vm;
    (void)reserved;
    
    JNIEnv* env = GetJNIEnv();
    if (env != NULL && g_softBusManagerClass != NULL) {
        (*env)->DeleteGlobalRef(env, g_softBusManagerClass);
        g_softBusManagerClass = NULL;
    }
    
    if (env != NULL && g_softBusManager != NULL) {
        (*env)->DeleteGlobalRef(env, g_softBusManager);
        g_softBusManager = NULL;
    }
    
    g_jvm = NULL;
    
    LOGI("SoftBus JNI library unloaded");
}

/* 初始化 */
JNIEXPORT jint JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeInit(JNIEnv* env, jobject thiz, jstring packageName) {
    const char* pkgName = (*env)->GetStringUTFChars(env, packageName, NULL);
    if (pkgName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 保存Java对象的全局引用 */
    g_softBusManager = (*env)->NewGlobalRef(env, thiz);
    if (g_softBusManager == NULL) {
        (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
        return SOFTBUS_ERR;
    }
    
    /* 获取类和方法ID */
    jclass clazz = (*env)->GetObjectClass(env, thiz);
    g_softBusManagerClass = (*env)->NewGlobalRef(env, clazz);
    
    g_onDeviceFoundMethod = (*env)->GetMethodID(env, clazz, "onDeviceFound", 
                                               "(ILjava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;I)V");
    g_onSessionOpenedMethod = (*env)->GetMethodID(env, clazz, "onSessionOpened", "(II)V");
    g_onSessionClosedMethod = (*env)->GetMethodID(env, clazz, "onSessionClosed", "(I)V");
    g_onBytesReceivedMethod = (*env)->GetMethodID(env, clazz, "onBytesReceived", "(I[B)V");
    
    (*env)->DeleteLocalRef(env, clazz);
    
    /* 初始化SoftBus客户端 */
    int32_t result = SoftBusClientInit(pkgName);
    
    (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
    
    LOGI("SoftBus native init result: %d", result);
    return result;
}

/* 反初始化 */
JNIEXPORT void JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeDeinit(JNIEnv* env, jobject thiz) {
    (void)env;
    (void)thiz;
    
    SoftBusClientDeinit();
    
    /* 清理全局引用 */
    if (g_softBusManager != NULL) {
        (*env)->DeleteGlobalRef(env, g_softBusManager);
        g_softBusManager = NULL;
    }
    
    if (g_softBusManagerClass != NULL) {
        (*env)->DeleteGlobalRef(env, g_softBusManagerClass);
        g_softBusManagerClass = NULL;
    }
    
    LOGI("SoftBus native deinitialized");
}

/* 检查连接状态 */
JNIEXPORT jboolean JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeIsConnected(JNIEnv* env, jobject thiz) {
    (void)env;
    (void)thiz;
    
    /* 简化实现，总是返回true */
    return JNI_TRUE;
}

/* 开始设备发现 */
JNIEXPORT jint JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeStartDiscovery(
    JNIEnv* env, jobject thiz, jstring packageName, jint mode, jint freq,
    jstring capability, jint capabilityData, jboolean isSameAccount, jboolean isWakeRemote) {
    
    (void)thiz;
    
    const char* pkgName = (*env)->GetStringUTFChars(env, packageName, NULL);
    const char* cap = (*env)->GetStringUTFChars(env, capability, NULL);
    
    if (pkgName == NULL || cap == NULL) {
        if (pkgName) (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
        if (cap) (*env)->ReleaseStringUTFChars(env, capability, cap);
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 构造订阅信息 */
    SubscribeInfo subscribeInfo;
    memset(&subscribeInfo, 0, sizeof(subscribeInfo));
    subscribeInfo.subscribeId = 1001; // 模拟订阅ID
    subscribeInfo.mode = (DiscoverMode)mode;
    subscribeInfo.freq = (ExchangeFreq)freq;
    subscribeInfo.isSameAccount = isSameAccount;
    subscribeInfo.isWakeRemote = isWakeRemote;
    strncpy(subscribeInfo.capability, cap, sizeof(subscribeInfo.capability) - 1);
    subscribeInfo.capabilityData = capabilityData;
    
    /* 开始发现 */
    int32_t result = StartDiscovery(pkgName, &subscribeInfo, &g_discoveryCallback);
    
    (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
    (*env)->ReleaseStringUTFChars(env, capability, cap);
    
    LOGD("Start discovery result: %d", result);
    return result;
}

/* 停止设备发现 */
JNIEXPORT jint JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeStopDiscovery(
    JNIEnv* env, jobject thiz, jstring packageName, jint subscribeId) {
    
    (void)thiz;
    
    const char* pkgName = (*env)->GetStringUTFChars(env, packageName, NULL);
    if (pkgName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    int32_t result = StopDiscovery(pkgName, subscribeId);
    
    (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
    
    LOGD("Stop discovery result: %d", result);
    return result;
}

/* 发布服务 */
JNIEXPORT jint JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativePublishService(
    JNIEnv* env, jobject thiz, jstring packageName, jint publishId, jint mode, jint freq,
    jstring capability, jint capabilityData, jboolean ranging) {
    
    (void)thiz;
    
    const char* pkgName = (*env)->GetStringUTFChars(env, packageName, NULL);
    const char* cap = (*env)->GetStringUTFChars(env, capability, NULL);
    
    if (pkgName == NULL || cap == NULL) {
        if (pkgName) (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
        if (cap) (*env)->ReleaseStringUTFChars(env, capability, cap);
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 构造发布信息 */
    PublishInfo publishInfo;
    memset(&publishInfo, 0, sizeof(publishInfo));
    publishInfo.publishId = publishId;
    publishInfo.mode = (DiscoverMode)mode;
    publishInfo.freq = (ExchangeFreq)freq;
    publishInfo.ranging = ranging;
    strncpy(publishInfo.capability, cap, sizeof(publishInfo.capability) - 1);
    publishInfo.capabilityData = capabilityData;
    
    /* 发布服务 */
    int32_t result = PublishLNN(pkgName, &publishInfo, NULL);
    
    (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
    (*env)->ReleaseStringUTFChars(env, capability, cap);
    
    LOGD("Publish service result: %d", result);
    return result;
}

/* 停止发布服务 */
JNIEXPORT jint JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeStopPublishService(
    JNIEnv* env, jobject thiz, jstring packageName, jint publishId) {
    
    (void)thiz;
    
    const char* pkgName = (*env)->GetStringUTFChars(env, packageName, NULL);
    if (pkgName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    int32_t result = StopPublishLNN(pkgName, publishId);
    
    (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
    
    LOGD("Stop publish service result: %d", result);
    return result;
}

/* 创建会话服务器 */
JNIEXPORT jint JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeCreateSessionServer(
    JNIEnv* env, jobject thiz, jstring packageName, jstring sessionName) {
    
    (void)thiz;
    
    const char* pkgName = (*env)->GetStringUTFChars(env, packageName, NULL);
    const char* sessName = (*env)->GetStringUTFChars(env, sessionName, NULL);
    
    if (pkgName == NULL || sessName == NULL) {
        if (pkgName) (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
        if (sessName) (*env)->ReleaseStringUTFChars(env, sessionName, sessName);
        return SOFTBUS_INVALID_PARAM;
    }
    
    int32_t result = CreateSessionServer(pkgName, sessName, &g_sessionListener);
    
    (*env)->ReleaseStringUTFChars(env, packageName, pkgName);
    (*env)->ReleaseStringUTFChars(env, sessionName, sessName);
    
    LOGD("Create session server result: %d", result);
    return result;
}

/* 打开会话 */
JNIEXPORT jint JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeOpenSession(
    JNIEnv* env, jobject thiz, jstring mySessionName, jstring peerSessionName,
    jstring peerNetworkId, jstring groupId, jint sessionType) {
    
    (void)thiz;
    
    const char* myName = (*env)->GetStringUTFChars(env, mySessionName, NULL);
    const char* peerName = (*env)->GetStringUTFChars(env, peerSessionName, NULL);
    const char* peerId = (*env)->GetStringUTFChars(env, peerNetworkId, NULL);
    const char* grpId = (*env)->GetStringUTFChars(env, groupId, NULL);
    
    if (myName == NULL || peerName == NULL || peerId == NULL) {
        if (myName) (*env)->ReleaseStringUTFChars(env, mySessionName, myName);
        if (peerName) (*env)->ReleaseStringUTFChars(env, peerSessionName, peerName);
        if (peerId) (*env)->ReleaseStringUTFChars(env, peerNetworkId, peerId);
        if (grpId) (*env)->ReleaseStringUTFChars(env, groupId, grpId);
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 构造会话属性 */
    SessionAttribute attr;
    memset(&attr, 0, sizeof(attr));
    attr.attr = (SessionType)sessionType;
    attr.dataType = TYPE_BYTES;
    
    int32_t result = OpenSession(myName, peerName, peerId, grpId, &attr);
    
    (*env)->ReleaseStringUTFChars(env, mySessionName, myName);
    (*env)->ReleaseStringUTFChars(env, peerSessionName, peerName);
    (*env)->ReleaseStringUTFChars(env, peerNetworkId, peerId);
    if (grpId) (*env)->ReleaseStringUTFChars(env, groupId, grpId);
    
    LOGD("Open session result: %d", result);
    return result;
}

/* 关闭会话 */
JNIEXPORT void JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeCloseSession(
    JNIEnv* env, jobject thiz, jint sessionId) {
    
    (void)env;
    (void)thiz;
    
    CloseSession(sessionId);
    
    LOGD("Close session: %d", sessionId);
}

/* 发送字节数据 */
JNIEXPORT jint JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeSendBytes(
    JNIEnv* env, jobject thiz, jint sessionId, jbyteArray data, jint len) {
    
    (void)thiz;
    
    if (data == NULL || len <= 0) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    jbyte* dataPtr = (*env)->GetByteArrayElements(env, data, NULL);
    if (dataPtr == NULL) {
        return SOFTBUS_ERR;
    }
    
    int32_t result = SendBytes(sessionId, dataPtr, len);
    
    (*env)->ReleaseByteArrayElements(env, data, dataPtr, JNI_ABORT);
    
    LOGD("Send bytes result: %d, sessionId: %d, len: %d", result, sessionId, len);
    return result;
}

/* 获取本地设备信息 */
JNIEXPORT jobject JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeGetLocalDeviceInfo(
    JNIEnv* env, jobject thiz) {
    
    (void)thiz;
    
    /* 简化实现，返回NULL */
    return NULL;
}

/* 获取统计信息 */
JNIEXPORT jobject JNICALL
Java_com_sentientnotes_android_softbus_SoftBusNativeManager_nativeGetStatistics(
    JNIEnv* env, jobject thiz) {
    
    (void)thiz;
    
    /* 简化实现，返回NULL */
    return NULL;
}
