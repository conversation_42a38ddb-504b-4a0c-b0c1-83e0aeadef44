/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <jni.h>
#include <nativehelper/JNIHelp.h>
#include <android_runtime/AndroidRuntime.h>
#include <utils/Log.h>
#include <utils/misc.h>

#include "softbus_core.h"
#include "softbus_discovery.h"
#include "softbus_connection.h"
#include "softbus_transmission.h"
#include "softbus_device.h"
#include "softbus_security.h"

#define LOG_TAG "SoftBusJNI"

namespace android {

// JNI全局引用
static JavaVM* g_jvm = nullptr;
static jclass g_softBusServiceClass = nullptr;
static jclass g_deviceInfoClass = nullptr;

// 回调方法ID
static jmethodID g_onDeviceFoundMethod = nullptr;
static jmethodID g_onConnectionStateChangedMethod = nullptr;
static jmethodID g_onDataReceivedMethod = nullptr;

// SoftBus核心实例
static SoftBusCore* g_softBusCore = nullptr;

// 获取JNI环境
static JNIEnv* GetJNIEnv() {
    JNIEnv* env = nullptr;
    if (g_jvm == nullptr) {
        return nullptr;
    }
    
    int status = g_jvm->GetEnv(reinterpret_cast<void**>(&env), JNI_VERSION_1_6);
    if (status == JNI_EDETACHED) {
        status = g_jvm->AttachCurrentThread(&env, nullptr);
        if (status != JNI_OK) {
            ALOGE("Failed to attach current thread");
            return nullptr;
        }
    } else if (status != JNI_OK) {
        ALOGE("Failed to get JNI environment");
        return nullptr;
    }
    
    return env;
}

// 设备发现回调
static void OnDeviceFoundCallback(const DeviceInfo* device) {
    if (device == nullptr || g_onDeviceFoundMethod == nullptr) {
        return;
    }
    
    JNIEnv* env = GetJNIEnv();
    if (env == nullptr) {
        return;
    }
    
    // 创建DeviceInfo Java对象
    jobject jDeviceInfo = env->NewObject(g_deviceInfoClass, 
        env->GetMethodID(g_deviceInfoClass, "<init>", "()V"));
    
    if (jDeviceInfo != nullptr) {
        // 设置设备信息字段
        jfieldID deviceIdField = env->GetFieldID(g_deviceInfoClass, "deviceId", "Ljava/lang/String;");
        jfieldID deviceNameField = env->GetFieldID(g_deviceInfoClass, "deviceName", "Ljava/lang/String;");
        jfieldID deviceTypeField = env->GetFieldID(g_deviceInfoClass, "deviceType", "I");
        
        env->SetObjectField(jDeviceInfo, deviceIdField, 
            env->NewStringUTF(device->deviceId));
        env->SetObjectField(jDeviceInfo, deviceNameField, 
            env->NewStringUTF(device->deviceName));
        env->SetIntField(jDeviceInfo, deviceTypeField, device->deviceType);
        
        // 调用Java回调方法
        env->CallStaticVoidMethod(g_softBusServiceClass, g_onDeviceFoundMethod, jDeviceInfo);
        
        env->DeleteLocalRef(jDeviceInfo);
    }
}

// 连接状态变化回调
static void OnConnectionStateChangedCallback(const char* sessionId, int state, int reason) {
    if (sessionId == nullptr || g_onConnectionStateChangedMethod == nullptr) {
        return;
    }
    
    JNIEnv* env = GetJNIEnv();
    if (env == nullptr) {
        return;
    }
    
    jstring jSessionId = env->NewStringUTF(sessionId);
    env->CallStaticVoidMethod(g_softBusServiceClass, g_onConnectionStateChangedMethod,
                             jSessionId, state, reason);
    env->DeleteLocalRef(jSessionId);
}

// 数据接收回调
static void OnDataReceivedCallback(const char* sessionId, const uint8_t* data, 
                                  uint32_t dataLen, int dataType) {
    if (sessionId == nullptr || data == nullptr || g_onDataReceivedMethod == nullptr) {
        return;
    }
    
    JNIEnv* env = GetJNIEnv();
    if (env == nullptr) {
        return;
    }
    
    jstring jSessionId = env->NewStringUTF(sessionId);
    jbyteArray jData = env->NewByteArray(dataLen);
    env->SetByteArrayRegion(jData, 0, dataLen, reinterpret_cast<const jbyte*>(data));
    
    env->CallStaticVoidMethod(g_softBusServiceClass, g_onDataReceivedMethod,
                             jSessionId, jData, dataType);
    
    env->DeleteLocalRef(jSessionId);
    env->DeleteLocalRef(jData);
}

// 初始化SoftBus
static jboolean nativeInitialize(JNIEnv* env, jclass clazz) {
    ALOGI("Initializing SoftBus native interface");
    
    if (g_softBusCore != nullptr) {
        ALOGW("SoftBus already initialized");
        return JNI_TRUE;
    }
    
    // 创建SoftBus核心实例
    g_softBusCore = new SoftBusCore();
    if (g_softBusCore == nullptr) {
        ALOGE("Failed to create SoftBus core instance");
        return JNI_FALSE;
    }
    
    // 设置回调函数
    SoftBusCallbacks callbacks = {
        .onDeviceFound = OnDeviceFoundCallback,
        .onConnectionStateChanged = OnConnectionStateChangedCallback,
        .onDataReceived = OnDataReceivedCallback
    };
    
    // 初始化SoftBus核心
    if (!g_softBusCore->Initialize(callbacks)) {
        ALOGE("Failed to initialize SoftBus core");
        delete g_softBusCore;
        g_softBusCore = nullptr;
        return JNI_FALSE;
    }
    
    ALOGI("SoftBus native interface initialized successfully");
    return JNI_TRUE;
}

// 反初始化SoftBus
static void nativeDeinitialize(JNIEnv* env, jclass clazz) {
    ALOGI("Deinitializing SoftBus native interface");
    
    if (g_softBusCore != nullptr) {
        g_softBusCore->Deinitialize();
        delete g_softBusCore;
        g_softBusCore = nullptr;
    }
    
    ALOGI("SoftBus native interface deinitialized");
}

// 开始设备发现
static jint nativeStartDiscovery(JNIEnv* env, jclass clazz, jobject config) {
    if (g_softBusCore == nullptr) {
        ALOGE("SoftBus not initialized");
        return -1;
    }
    
    // 解析发现配置
    DiscoveryConfig nativeConfig = {};
    
    jclass configClass = env->GetObjectClass(config);
    jfieldID modeField = env->GetFieldID(configClass, "mode", "I");
    jfieldID mediumField = env->GetFieldID(configClass, "medium", "I");
    jfieldID freqField = env->GetFieldID(configClass, "freq", "I");
    jfieldID capabilityField = env->GetFieldID(configClass, "capability", "Ljava/lang/String;");
    
    nativeConfig.mode = env->GetIntField(config, modeField);
    nativeConfig.medium = env->GetIntField(config, mediumField);
    nativeConfig.freq = env->GetIntField(config, freqField);
    
    jstring jCapability = static_cast<jstring>(env->GetObjectField(config, capabilityField));
    if (jCapability != nullptr) {
        const char* capability = env->GetStringUTFChars(jCapability, nullptr);
        strncpy(nativeConfig.capability, capability, sizeof(nativeConfig.capability) - 1);
        env->ReleaseStringUTFChars(jCapability, capability);
    }
    
    // 启动发现
    int discoveryId = g_softBusCore->StartDiscovery(nativeConfig);
    
    ALOGI("Discovery started with ID: %d", discoveryId);
    return discoveryId;
}

// 停止设备发现
static void nativeStopDiscovery(JNIEnv* env, jclass clazz, jint discoveryId) {
    if (g_softBusCore == nullptr) {
        ALOGE("SoftBus not initialized");
        return;
    }
    
    g_softBusCore->StopDiscovery(discoveryId);
    ALOGI("Discovery stopped: %d", discoveryId);
}

// 连接设备
static jstring nativeConnectDevice(JNIEnv* env, jclass clazz, jstring deviceId, jobject config) {
    if (g_softBusCore == nullptr) {
        ALOGE("SoftBus not initialized");
        return nullptr;
    }
    
    const char* nativeDeviceId = env->GetStringUTFChars(deviceId, nullptr);
    
    // 解析连接配置
    ConnectionConfig nativeConfig = {};
    
    jclass configClass = env->GetObjectClass(config);
    jfieldID linkTypeField = env->GetFieldID(configClass, "linkType", "I");
    jfieldID qosField = env->GetFieldID(configClass, "qos", "I");
    
    nativeConfig.linkType = env->GetIntField(config, linkTypeField);
    nativeConfig.qos = env->GetIntField(config, qosField);
    
    // 建立连接
    std::string sessionId = g_softBusCore->ConnectDevice(nativeDeviceId, nativeConfig);
    
    env->ReleaseStringUTFChars(deviceId, nativeDeviceId);
    
    if (sessionId.empty()) {
        ALOGE("Failed to connect device");
        return nullptr;
    }
    
    ALOGI("Device connected, session ID: %s", sessionId.c_str());
    return env->NewStringUTF(sessionId.c_str());
}

// 断开设备连接
static void nativeDisconnectDevice(JNIEnv* env, jclass clazz, jstring sessionId) {
    if (g_softBusCore == nullptr) {
        ALOGE("SoftBus not initialized");
        return;
    }
    
    const char* nativeSessionId = env->GetStringUTFChars(sessionId, nullptr);
    
    g_softBusCore->DisconnectDevice(nativeSessionId);
    
    env->ReleaseStringUTFChars(sessionId, nativeSessionId);
    
    ALOGI("Device disconnected: %s", nativeSessionId);
}

// 发送字节数据
static void nativeSendBytes(JNIEnv* env, jclass clazz, jstring sessionId, jbyteArray data) {
    if (g_softBusCore == nullptr) {
        ALOGE("SoftBus not initialized");
        return;
    }
    
    const char* nativeSessionId = env->GetStringUTFChars(sessionId, nullptr);
    
    jsize dataLen = env->GetArrayLength(data);
    jbyte* dataPtr = env->GetByteArrayElements(data, nullptr);
    
    bool result = g_softBusCore->SendBytes(nativeSessionId, 
                                          reinterpret_cast<uint8_t*>(dataPtr), dataLen);
    
    env->ReleaseByteArrayElements(data, dataPtr, JNI_ABORT);
    env->ReleaseStringUTFChars(sessionId, nativeSessionId);
    
    if (!result) {
        ALOGE("Failed to send bytes");
    }
}

// 发送消息
static void nativeSendMessage(JNIEnv* env, jclass clazz, jstring sessionId, jstring message) {
    if (g_softBusCore == nullptr) {
        ALOGE("SoftBus not initialized");
        return;
    }
    
    const char* nativeSessionId = env->GetStringUTFChars(sessionId, nullptr);
    const char* nativeMessage = env->GetStringUTFChars(message, nullptr);
    
    bool result = g_softBusCore->SendMessage(nativeSessionId, nativeMessage);
    
    env->ReleaseStringUTFChars(sessionId, nativeSessionId);
    env->ReleaseStringUTFChars(message, nativeMessage);
    
    if (!result) {
        ALOGE("Failed to send message");
    }
}

// 获取本地设备信息
static jobject nativeGetLocalDeviceInfo(JNIEnv* env, jclass clazz) {
    if (g_softBusCore == nullptr) {
        ALOGE("SoftBus not initialized");
        return nullptr;
    }
    
    DeviceInfo deviceInfo = g_softBusCore->GetLocalDeviceInfo();
    
    // 创建DeviceInfo Java对象
    jobject jDeviceInfo = env->NewObject(g_deviceInfoClass, 
        env->GetMethodID(g_deviceInfoClass, "<init>", "()V"));
    
    if (jDeviceInfo != nullptr) {
        jfieldID deviceIdField = env->GetFieldID(g_deviceInfoClass, "deviceId", "Ljava/lang/String;");
        jfieldID deviceNameField = env->GetFieldID(g_deviceInfoClass, "deviceName", "Ljava/lang/String;");
        jfieldID deviceTypeField = env->GetFieldID(g_deviceInfoClass, "deviceType", "I");
        
        env->SetObjectField(jDeviceInfo, deviceIdField, 
            env->NewStringUTF(deviceInfo.deviceId));
        env->SetObjectField(jDeviceInfo, deviceNameField, 
            env->NewStringUTF(deviceInfo.deviceName));
        env->SetIntField(jDeviceInfo, deviceTypeField, deviceInfo.deviceType);
    }
    
    return jDeviceInfo;
}

// JNI方法表
static const JNINativeMethod gMethods[] = {
    {"nativeInitialize", "()Z", (void*)nativeInitialize},
    {"nativeDeinitialize", "()V", (void*)nativeDeinitialize},
    {"nativeStartDiscovery", "(Landroid/softbus/discovery/DiscoveryConfig;)I", (void*)nativeStartDiscovery},
    {"nativeStopDiscovery", "(I)V", (void*)nativeStopDiscovery},
    {"nativeConnectDevice", "(Ljava/lang/String;Landroid/softbus/connection/ConnectionConfig;)Ljava/lang/String;", (void*)nativeConnectDevice},
    {"nativeDisconnectDevice", "(Ljava/lang/String;)V", (void*)nativeDisconnectDevice},
    {"nativeSendBytes", "(Ljava/lang/String;[B)V", (void*)nativeSendBytes},
    {"nativeSendMessage", "(Ljava/lang/String;Ljava/lang/String;)V", (void*)nativeSendMessage},
    {"nativeGetLocalDeviceInfo", "()Landroid/softbus/device/DeviceInfo;", (void*)nativeGetLocalDeviceInfo},
};

// JNI_OnLoad
jint JNI_OnLoad(JavaVM* vm, void* /* reserved */) {
    JNIEnv* env = nullptr;
    jint result = -1;
    
    if (vm->GetEnv(reinterpret_cast<void**>(&env), JNI_VERSION_1_6) != JNI_OK) {
        ALOGE("ERROR: GetEnv failed");
        goto bail;
    }
    
    g_jvm = vm;
    
    // 注册Native方法
    if (jniRegisterNativeMethods(env, "com/android/server/softbus/SoftBusNativeInterface",
                                gMethods, NELEM(gMethods)) < 0) {
        ALOGE("ERROR: SoftBus native registration failed");
        goto bail;
    }
    
    // 获取类引用
    jclass softBusServiceClass = env->FindClass("com/android/server/softbus/SoftBusService");
    if (softBusServiceClass != nullptr) {
        g_softBusServiceClass = static_cast<jclass>(env->NewGlobalRef(softBusServiceClass));
        
        // 获取回调方法ID
        g_onDeviceFoundMethod = env->GetStaticMethodID(g_softBusServiceClass, 
            "onDeviceFoundFromNative", "(Landroid/softbus/device/DeviceInfo;)V");
        g_onConnectionStateChangedMethod = env->GetStaticMethodID(g_softBusServiceClass,
            "onConnectionStateChangedFromNative", "(Ljava/lang/String;II)V");
        g_onDataReceivedMethod = env->GetStaticMethodID(g_softBusServiceClass,
            "onDataReceivedFromNative", "(Ljava/lang/String;[BI)V");
    }
    
    jclass deviceInfoClass = env->FindClass("android/softbus/device/DeviceInfo");
    if (deviceInfoClass != nullptr) {
        g_deviceInfoClass = static_cast<jclass>(env->NewGlobalRef(deviceInfoClass));
    }
    
    result = JNI_VERSION_1_6;
    
bail:
    return result;
}

} // namespace android
