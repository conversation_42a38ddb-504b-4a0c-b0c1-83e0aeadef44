"""
灵境笔记响应数据模型
基于设计文档中的SN_ACTION_AND_STATE_UPDATE定义
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from enum import Enum
import time


class UpdateType(str, Enum):
    """更新操作类型枚举"""
    NOTE_ATOM_CREATION = "NOTE_ATOM_CREATION"
    NOTE_ATOM_UPDATE = "NOTE_ATOM_UPDATE"
    LINK_CREATION = "LINK_CREATION"
    CLUSTER_UPDATE = "CLUSTER_UPDATE"
    AR_VISUALIZATION_UPDATE = "AR_VISUALIZATION_UPDATE"
    AI_PROCESSING_REQUEST = "AI_PROCESSING_REQUEST"
    SYSTEM_STATE_CHANGE = "SYSTEM_STATE_CHANGE"


class AtomType(str, Enum):
    """笔记原子类型枚举"""
    TEXT = "TEXT"
    IMAGE = "IMAGE"
    AUDIO = "AUDIO"
    VIDEO = "VIDEO"
    HANDWRITING = "HANDWRITING"
    THREE_D_MODEL = "3D_MODEL"
    LINK = "LINK"
    ANNOTATION = "ANNOTATION"


class PrivacyLevel(str, Enum):
    """隐私级别枚举"""
    PUBLIC = "PUBLIC"
    PRIVATE = "PRIVATE"
    SHARED = "SHARED"
    CONFIDENTIAL = "CONFIDENTIAL"


class AnchorType(str, Enum):
    """锚点类型枚举"""
    WORLD_LOCKED = "WORLD_LOCKED"
    USER_RELATIVE = "USER_RELATIVE"
    OBJECT_ATTACHED = "OBJECT_ATTACHED"


class ConflictResolutionStatus(str, Enum):
    """冲突解决状态枚举"""
    RESOLVED = "RESOLVED"
    PENDING = "PENDING"
    CONFLICTED = "CONFLICTED"


class NoteAtomMetadata(BaseModel):
    """笔记原子元数据"""
    creation_timestamp: int = Field(..., description="创建时间戳")
    last_modified_timestamp: int = Field(..., description="最后修改时间戳")
    creator_user_id: str = Field(..., description="创建者用户ID")
    source_device_id: str = Field(..., description="源设备ID")
    importance_score: float = Field(..., ge=0.0, le=1.0, description="重要性分数")
    privacy_level: PrivacyLevel = Field(..., description="隐私级别")


class TemporalLink(BaseModel):
    """时间链接"""
    source_media_uri: str = Field(..., description="源媒体URI")
    start_timestamp_ms: int = Field(..., description="开始时间戳（毫秒）")
    end_timestamp_ms: int = Field(..., description="结束时间戳（毫秒）")


class AIGeneratedInsights(BaseModel):
    """AI生成的洞察"""
    summary: Optional[str] = Field(None, description="摘要")
    keywords: List[str] = Field(default=[], description="关键词")
    related_questions: List[str] = Field(default=[], description="相关问题")
    potential_connections: List[str] = Field(default=[], description="潜在连接")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="置信度分数")


class ConnectedAtom(BaseModel):
    """连接的原子"""
    atom_id: str = Field(..., description="原子ID")
    relationship_type: str = Field(..., description="关系类型")
    spatial_distance: float = Field(..., description="空间距离")
    connection_strength: float = Field(..., ge=0.0, le=1.0, description="连接强度")


class SpatialNeighborhoodGraph(BaseModel):
    """空间邻域图"""
    connected_atoms: List[ConnectedAtom] = Field(default=[], description="连接的原子")


class VersionHistory(BaseModel):
    """版本历史"""
    current_version: str = Field(..., description="当前版本")
    version_history_uri: str = Field(..., description="版本历史URI")
    branching_info: Optional[Dict[str, Any]] = Field(None, description="分支信息")


class SoftBusSyncMetadata(BaseModel):
    """SoftBus同步元数据"""
    crdt_timestamp: int = Field(..., description="CRDT时间戳")
    last_modifier_device_id: str = Field(..., description="最后修改设备ID")
    conflict_resolution_status: ConflictResolutionStatus = Field(..., description="冲突解决状态")
    sync_vector_clock: Optional[Dict[str, int]] = Field(None, description="同步向量时钟")


class VisibilityRules(BaseModel):
    """可见性规则"""
    min_distance: Optional[float] = Field(None, description="最小距离")
    max_distance: Optional[float] = Field(None, description="最大距离")
    viewing_angle_constraints: Optional[Dict[str, Any]] = Field(None, description="视角约束")


class ARSpatialProperties(BaseModel):
    """AR空间属性"""
    position_3d: List[float] = Field(..., min_items=3, max_items=3, description="3D位置")
    rotation_quaternion: List[float] = Field(..., min_items=4, max_items=4, description="四元数旋转")
    scale_factor: float = Field(default=1.0, description="缩放因子")
    anchor_type: AnchorType = Field(..., description="锚点类型")
    visibility_rules: Optional[VisibilityRules] = Field(None, description="可见性规则")


class NoteAtom(BaseModel):
    """笔记原子完整数据结构"""
    atom_id: str = Field(..., description="原子ID")
    atom_type: AtomType = Field(..., description="原子类型")
    content: Dict[str, Any] = Field(..., description="原子内容")
    metadata: NoteAtomMetadata = Field(..., description="元数据")
    
    # 可选字段
    embedding_vector_multimodal_optional: Optional[List[float]] = Field(
        None, description="多模态语义向量嵌入"
    )
    temporal_link_to_raw_source_optional: Optional[TemporalLink] = Field(
        None, description="时间链接到原始源"
    )
    user_annotation_and_metadata_custom: Optional[Dict[str, Any]] = Field(
        None, description="用户自定义标注和元数据"
    )
    ai_generated_insights_on_atom: Optional[AIGeneratedInsights] = Field(
        None, description="AI生成的原子洞察"
    )
    spatial_neighborhood_graph_local_optional: Optional[SpatialNeighborhoodGraph] = Field(
        None, description="空间邻域图"
    )
    version_history_pointer_optional: Optional[VersionHistory] = Field(
        None, description="版本历史指针"
    )
    softbus_sync_metadata_optional: Optional[SoftBusSyncMetadata] = Field(
        None, description="SoftBus同步元数据"
    )
    ar_spatial_properties: Optional[ARSpatialProperties] = Field(
        None, description="AR空间属性"
    )


class LinkType(str, Enum):
    """链接类型枚举"""
    SEMANTIC_SIMILARITY = "SEMANTIC_SIMILARITY"
    TEMPORAL_SEQUENCE = "TEMPORAL_SEQUENCE"
    CAUSAL_RELATIONSHIP = "CAUSAL_RELATIONSHIP"
    HIERARCHICAL = "HIERARCHICAL"
    CROSS_REFERENCE = "CROSS_REFERENCE"
    USER_DEFINED = "USER_DEFINED"


class LinkOrCluster(BaseModel):
    """链接或聚类数据结构"""
    id: str = Field(..., description="链接或聚类ID")
    type: str = Field(..., description="类型：LINK或CLUSTER")
    source_atom_ids: List[str] = Field(..., description="源原子ID列表")
    target_atom_ids: List[str] = Field(..., description="目标原子ID列表")
    relationship_type: Optional[LinkType] = Field(None, description="关系类型")
    strength: float = Field(..., ge=0.0, le=1.0, description="连接强度")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")
    creation_timestamp: int = Field(default_factory=lambda: int(time.time() * 1000000))


class ARVisualizationCommand(BaseModel):
    """AR可视化指令"""
    command_id: str = Field(..., description="指令ID")
    command_type: str = Field(..., description="指令类型")
    target_atom_ids: List[str] = Field(..., description="目标原子ID列表")
    visualization_parameters: Dict[str, Any] = Field(..., description="可视化参数")
    duration_ms: Optional[int] = Field(None, description="持续时间（毫秒）")
    priority: int = Field(default=5, ge=1, le=10, description="优先级")


class DistributedAIRequest(BaseModel):
    """分布式AI处理请求"""
    request_id: str = Field(..., description="请求ID")
    ai_capability_required: str = Field(..., description="所需AI能力")
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    processing_requirements: Dict[str, Any] = Field(..., description="处理要求")
    target_device_id: Optional[str] = Field(None, description="目标设备ID")
    timeout_ms: int = Field(default=10000, description="超时时间（毫秒）")


class SystemNotificationType(str, Enum):
    """系统通知类型枚举"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    SUCCESS = "SUCCESS"


class SystemNotification(BaseModel):
    """系统级通知"""
    notification_id: str = Field(..., description="通知ID")
    type: SystemNotificationType = Field(..., description="通知类型")
    title: str = Field(..., description="通知标题")
    message: str = Field(..., description="通知消息")
    timestamp: int = Field(default_factory=lambda: int(time.time() * 1000000))
    target_user_ids: Optional[List[str]] = Field(None, description="目标用户ID列表")
    action_required: bool = Field(default=False, description="是否需要用户操作")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")


class DistributionMode(str, Enum):
    """分发模式枚举"""
    BROADCAST = "BROADCAST"
    MULTICAST = "MULTICAST"
    UNICAST = "UNICAST"
    P2P = "P2P"


class ReliabilityRequirement(str, Enum):
    """可靠性要求枚举"""
    RELIABLE = "RELIABLE"
    UNRELIABLE = "UNRELIABLE"
    BEST_EFFORT = "BEST_EFFORT"


class SoftBusDistributionStrategy(BaseModel):
    """SoftBus分发策略配置"""
    distribution_mode: DistributionMode = Field(..., description="分发模式")
    priority_level: int = Field(default=5, ge=1, le=10, description="优先级")
    reliability_requirement: ReliabilityRequirement = Field(..., description="可靠性要求")
    max_distribution_latency_ms: Optional[int] = Field(None, description="最大分发延迟")
    compression_enabled: bool = Field(default=True, description="是否启用压缩")
    delta_update_enabled: bool = Field(default=True, description="是否启用增量更新")


class SN_ACTION_AND_STATE_UPDATE(BaseModel):
    """灵境笔记统一输出/状态更新对象"""
    
    update_id: str = Field(..., description="更新操作的全局唯一标识符")
    timestamp: int = Field(default_factory=lambda: int(time.time() * 1000000), description="更新生成时间戳")
    correlation_event_id: Optional[str] = Field(None, description="关联的输入事件ID")
    update_type: UpdateType = Field(..., description="更新操作类型")
    target_devices: List[str] = Field(..., description="目标设备SoftBus ID列表")
    
    # 更新内容
    note_atoms_updates: Optional[List[NoteAtom]] = Field(None, description="笔记原子更新")
    links_and_clusters_updates: Optional[List[LinkOrCluster]] = Field(None, description="链接和聚类更新")
    ar_visualization_commands_optional: Optional[List[ARVisualizationCommand]] = Field(
        None, description="AR可视化指令"
    )
    request_for_distributed_ai_optional: Optional[List[DistributedAIRequest]] = Field(
        None, description="分布式AI处理请求"
    )
    system_notifications: Optional[List[SystemNotification]] = Field(
        None, description="系统通知"
    )
    
    # 分发策略
    softbus_distribution_strategy: Optional[SoftBusDistributionStrategy] = Field(
        None, description="SoftBus分发策略"
    )
    
    class Config:
        use_enum_values = True


# 简化的响应模型用于API
class SimpleResponse(BaseModel):
    """简化的API响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    timestamp: int = Field(default_factory=lambda: int(time.time() * 1000000))


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: bool = Field(default=True, description="是否为错误")
    error_code: str = Field(..., description="错误代码")
    error_message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: int = Field(default_factory=lambda: int(time.time() * 1000000))


class HealthStatus(BaseModel):
    """健康状态响应"""
    status: str = Field(..., description="服务状态")
    sn_core_status: str = Field(..., description="SN-Core状态")
    connected_devices: int = Field(..., description="连接设备数量")
    uptime_seconds: float = Field(..., description="运行时间（秒）")
    memory_usage_mb: Optional[float] = Field(None, description="内存使用（MB）")
    cpu_usage_percent: Optional[float] = Field(None, description="CPU使用率")
    timestamp: int = Field(default_factory=lambda: int(time.time() * 1000000))
