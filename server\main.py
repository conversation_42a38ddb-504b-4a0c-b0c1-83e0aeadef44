"""
灵境笔记 (Sentient Notes) 服务端主入口
基于FastAPI的分布式AI笔记系统服务端
"""

import asyncio
import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from loguru import logger
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.config import settings
from core.sn_core_engine import SNCore
from api.routes import api_router
from api.websocket_manager import WebSocketManager
from models.events import SN_EVENT_STREAM
from models.responses import SN_ACTION_AND_STATE_UPDATE


class SentientNotesServer:
    """灵境笔记服务端主类"""
    
    def __init__(self):
        self.sn_core: SNCore = None
        self.websocket_manager = WebSocketManager()
        
    async def initialize(self):
        """初始化服务端组件"""
        logger.info("正在初始化灵境笔记服务端...")
        
        # 初始化SN-Core引擎
        self.sn_core = SNCore()
        await self.sn_core.initialize()
        
        logger.info("SN-Core引擎初始化完成")
        
    async def shutdown(self):
        """关闭服务端组件"""
        logger.info("正在关闭灵境笔记服务端...")
        
        if self.sn_core:
            await self.sn_core.shutdown()
            
        await self.websocket_manager.disconnect_all()
        logger.info("服务端关闭完成")


# 全局服务端实例
server_instance = SentientNotesServer()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    await server_instance.initialize()
    yield
    # 关闭时清理
    await server_instance.shutdown()


# 创建FastAPI应用
app = FastAPI(
    title="灵境笔记 API",
    description="基于OpenHarmony SoftBus的分布式AI笔记系统",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(api_router, prefix="/api/v1")


@app.websocket("/ws/{device_id}")
async def websocket_endpoint(websocket: WebSocket, device_id: str):
    """WebSocket连接端点，用于实时通信"""
    await server_instance.websocket_manager.connect(websocket, device_id)
    
    try:
        while True:
            # 接收来自客户端的事件流
            data = await websocket.receive_json()
            
            try:
                # 解析事件流
                event_stream = SN_EVENT_STREAM.model_validate(data)
                logger.info(f"收到来自设备 {device_id} 的事件: {event_stream.input_type}")
                
                # 通过SN-Core处理事件
                response = await server_instance.sn_core.process_event_stream(event_stream)
                
                # 发送响应
                if response:
                    await server_instance.websocket_manager.send_to_device(
                        device_id, response.model_dump()
                    )
                    
                    # 如果需要广播给其他设备
                    if response.target_devices:
                        for target_device in response.target_devices:
                            if target_device != device_id:
                                await server_instance.websocket_manager.send_to_device(
                                    target_device, response.model_dump()
                                )
                                
            except Exception as e:
                logger.error(f"处理事件流时发生错误: {e}")
                await websocket.send_json({
                    "error": "事件处理失败",
                    "message": str(e)
                })
                
    except WebSocketDisconnect:
        logger.info(f"设备 {device_id} 断开连接")
        await server_instance.websocket_manager.disconnect(device_id)
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
        await server_instance.websocket_manager.disconnect(device_id)


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "灵境笔记服务端运行正常",
        "version": "1.0.0",
        "status": "healthy"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查SN-Core状态
        core_status = await server_instance.sn_core.get_health_status() if server_instance.sn_core else "not_initialized"
        
        return {
            "status": "healthy",
            "sn_core_status": core_status,
            "connected_devices": len(server_instance.websocket_manager.active_connections),
            "timestamp": asyncio.get_event_loop().time()
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 启动服务器
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
