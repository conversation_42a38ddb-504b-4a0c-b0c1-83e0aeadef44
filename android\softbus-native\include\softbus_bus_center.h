/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * SoftBus Bus Center - 设备发现和管理核心
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#ifndef SOFTBUS_BUS_CENTER_H
#define SOFTBUS_BUS_CENTER_H

#include <stdint.h>
#include <stdbool.h>
#include "softbus_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 设备发现类型 */
typedef enum {
    DISCOVERY_TYPE_WIFI = 0,
    DISCOVERY_TYPE_BLE = 1,
    DISCOVERY_TYPE_BR = 2,
    DISCOVERY_TYPE_P2P = 3,
    DISCOVERY_TYPE_USB = 4,
    DISCOVERY_TYPE_HML = 5,
    DISCOVERY_TYPE_COUNT
} DiscoveryType;

/* 设备发现模式 */
typedef enum {
    DISCOVER_MODE_PASSIVE = 0,
    DISCOVER_MODE_ACTIVE = 1
} DiscoverMode;

/* 设备发现频率 */
typedef enum {
    FREQ_LOW = 0,
    FREQ_MID = 1,
    FREQ_HIGH = 2,
    FREQ_SUPER_HIGH = 3
} ExchangeFreq;

/* 设备发现能力 */
typedef enum {
    CAPABILITY_NONE = 0,
    CAPABILITY_OSDDATA = 1,
    CAPABILITY_SHARE = 2,
    CAPABILITY_CASTPLUS = 4,
    CAPABILITY_VA = 8,
    CAPABILITY_RICHDM = 16,
    CAPABILITY_DDMP = 32,
    CAPABILITY_OSD = 64
} DataBitMap;

/* 设备信息结构 */
typedef struct {
    char deviceId[DEVICE_ID_SIZE_MAX];
    char deviceName[DEVICE_NAME_SIZE_MAX];
    char deviceType[DEVICE_TYPE_SIZE_MAX];
    char networkId[NETWORK_ID_SIZE_MAX];
    uint32_t deviceTypeId;
    int32_t authForm;
    uint32_t addrNum;
    ConnectionAddr addr[CONNECTION_ADDR_MAX];
    uint32_t capabilityBitmap[CAPABILITY_NUM];
    int32_t masterWeight;
    bool isOnline;
    uint64_t timestamp;
} DeviceInfo;

/* 设备发现信息 */
typedef struct {
    char deviceId[DEVICE_ID_SIZE_MAX];
    char deviceName[DEVICE_NAME_SIZE_MAX];
    uint32_t deviceTypeId;
    uint32_t capabilityBitmap[CAPABILITY_NUM];
    uint32_t addrNum;
    ConnectionAddr addr[CONNECTION_ADDR_MAX];
    uint32_t range;
} DeviceFoundInfo;

/* 发布信息 */
typedef struct {
    int32_t publishId;
    DiscoverMode mode;
    ExchangeFreq freq;
    char capability[CAPABILITY_SIZE_MAX];
    uint32_t capabilityData;
    uint32_t dataLen;
    bool ranging;
} PublishInfo;

/* 订阅信息 */
typedef struct {
    int32_t subscribeId;
    DiscoverMode mode;
    ExchangeFreq freq;
    bool isSameAccount;
    bool isWakeRemote;
    char capability[CAPABILITY_SIZE_MAX];
    uint32_t capabilityData;
    uint32_t dataLen;
} SubscribeInfo;

/* 设备发现回调 */
typedef struct {
    void (*OnDeviceFound)(const DeviceFoundInfo *device);
    void (*OnDiscoverFailed)(int32_t subscribeId, DiscoveryFailReason failReason);
    void (*OnDiscoverySuccess)(int32_t subscribeId);
} IDiscoveryCallback;

/* 设备状态回调 */
typedef struct {
    void (*OnDeviceOnline)(DeviceInfo *device);
    void (*OnDeviceOffline)(const char *deviceId);
    void (*OnDeviceInfoChanged)(DeviceInfo *device);
} INodeStateCb;

/* 发布服务回调 */
typedef struct {
    void (*OnPublishResult)(int32_t publishId, PublishResult result);
} IPublishCb;

/**
 * 初始化Bus Center模块
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t LnnInitBusCenter(void);

/**
 * 反初始化Bus Center模块
 */
void LnnDeinitBusCenter(void);

/**
 * 启动设备发现
 * @param pkgName 包名
 * @param subscribeInfo 订阅信息
 * @param cb 发现回调
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t StartDiscovery(const char *pkgName, const SubscribeInfo *subscribeInfo, 
                      const IDiscoveryCallback *cb);

/**
 * 停止设备发现
 * @param pkgName 包名
 * @param subscribeId 订阅ID
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t StopDiscovery(const char *pkgName, int32_t subscribeId);

/**
 * 发布服务
 * @param pkgName 包名
 * @param publishInfo 发布信息
 * @param cb 发布回调
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t PublishLNN(const char *pkgName, const PublishInfo *publishInfo, 
                   const IPublishCb *cb);

/**
 * 停止发布服务
 * @param pkgName 包名
 * @param publishId 发布ID
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t StopPublishLNN(const char *pkgName, int32_t publishId);

/**
 * 注册设备状态监听
 * @param pkgName 包名
 * @param cb 状态回调
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t RegNodeDeviceStateCb(const char *pkgName, INodeStateCb *cb);

/**
 * 注销设备状态监听
 * @param pkgName 包名
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t UnregNodeDeviceStateCb(const char *pkgName);

/**
 * 获取所有在线设备信息
 * @param pkgName 包名
 * @param info 设备信息数组
 * @param infoNum 设备数量
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetAllNodeDeviceInfo(const char *pkgName, DeviceInfo **info, int32_t *infoNum);

/**
 * 获取本地设备信息
 * @param pkgName 包名
 * @param info 本地设备信息
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetLocalNodeDeviceInfo(const char *pkgName, DeviceInfo *info);

/**
 * 根据网络ID获取设备信息
 * @param pkgName 包名
 * @param networkId 网络ID
 * @param info 设备信息
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetNodeKeyInfo(const char *pkgName, const char *networkId, 
                       NodeDeviceInfoKey key, uint8_t *info, int32_t infoLen);

/**
 * 设置节点数据变更标志
 * @param networkId 网络ID
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SetNodeDataChangeFlag(const char *networkId, uint16_t dataChangeFlag);

/**
 * 加入LNN网络
 * @param pkgName 包名
 * @param target 目标设备地址
 * @param cb 连接回调
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t JoinLNN(const char *pkgName, ConnectionAddr *target, OnJoinLNNResult cb);

/**
 * 离开LNN网络
 * @param pkgName 包名
 * @param networkId 网络ID
 * @param cb 离开回调
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t LeaveLNN(const char *pkgName, const char *networkId, OnLeaveLNNResult cb);

/**
 * 启动时间同步
 * @param pkgName 包名
 * @param targetNetworkId 目标网络ID
 * @param accuracy 精度要求
 * @param period 同步周期
 * @param cb 时间同步回调
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t StartTimeSync(const char *pkgName, const char *targetNetworkId, 
                      TimeSyncAccuracy accuracy, TimeSyncPeriod period, ITimeSyncCb *cb);

/**
 * 停止时间同步
 * @param pkgName 包名
 * @param targetNetworkId 目标网络ID
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t StopTimeSync(const char *pkgName, const char *targetNetworkId);

/**
 * 发布服务能力
 * @param capability 能力信息
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t PublishService(const char *pkgName, const PublishInfo *capability);

/**
 * 取消发布服务能力
 * @param capability 能力信息
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t UnPublishService(const char *pkgName, int32_t publishId);

/**
 * 刷新LNN
 * @param pkgName 包名
 * @param subscribeId 订阅ID
 * @param refreshOption 刷新选项
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t RefreshLNN(const char *pkgName, int32_t subscribeId, const SubscribeInfo *refreshOption);

/**
 * 激活元能力
 * @param pkgName 包名
 * @param targetNetworkId 目标网络ID
 * @param metaNodeId 元节点ID
 * @param metaNodeIdLen 元节点ID长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ActiveMetaNode(const char *pkgName, const char *targetNetworkId, 
                       const char *metaNodeId, uint32_t metaNodeIdLen);

/**
 * 去激活元能力
 * @param pkgName 包名
 * @param targetNetworkId 目标网络ID
 * @param metaNodeId 元节点ID
 * @param metaNodeIdLen 元节点ID长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t DeactiveMetaNode(const char *pkgName, const char *targetNetworkId, 
                         const char *metaNodeId, uint32_t metaNodeIdLen);

/**
 * 获取所有元节点信息
 * @param infos 元节点信息数组
 * @param infoNum 元节点数量
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetAllMetaNodeInfo(MetaNodeInfo *infos, int32_t *infoNum);

/**
 * 移位元节点
 * @param pkgName 包名
 * @param targetNetworkId 目标网络ID
 * @param metaNodeId 元节点ID
 * @param metaNodeIdLen 元节点ID长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ShiftLNNGear(const char *pkgName, const char *targetNetworkId, 
                     const GearMode *mode);

#ifdef __cplusplus
}
#endif

#endif // SOFTBUS_BUS_CENTER_H
