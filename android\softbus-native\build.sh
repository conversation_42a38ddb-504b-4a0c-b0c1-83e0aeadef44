#!/bin/bash

# Copyright (c) 2024 SentientNotes Project
# Licensed under the Apache License, Version 2.0 (the "License");
# 
# SoftBus Native Build Script
# 基于OpenHarmony SoftBus架构移植到Android用户态

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 默认配置
BUILD_TYPE="Release"
BUILD_DIR="$PROJECT_ROOT/build"
INSTALL_PREFIX="/usr/local"
ENABLE_TESTING="OFF"
ENABLE_EXAMPLES="ON"
ENABLE_DOCS="OFF"
CROSS_COMPILE=""
TARGET_ARCH=""
ANDROID_NDK=""
ANDROID_API_LEVEL="26"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
SoftBus Native Build Script

Usage: $0 [OPTIONS] [COMMAND]

Commands:
    build       Build the project (default)
    clean       Clean build directory
    install     Install the project
    test        Run tests
    package     Create packages
    format      Format source code
    lint        Run static analysis
    docs        Generate documentation

Options:
    -h, --help              Show this help message
    -t, --type TYPE         Build type: Debug, Release, RelWithDebInfo, MinSizeRel (default: Release)
    -d, --build-dir DIR     Build directory (default: ./build)
    -p, --prefix PREFIX     Install prefix (default: /usr/local)
    -j, --jobs JOBS         Number of parallel jobs (default: auto-detect)
    --enable-testing        Enable testing
    --enable-examples       Enable examples (default: ON)
    --enable-docs           Enable documentation generation
    --cross-compile TRIPLE  Cross-compile target triple
    --android-ndk PATH      Android NDK path for cross-compilation
    --android-api LEVEL     Android API level (default: 26)
    --target-arch ARCH      Target architecture (arm, arm64, x86, x86_64)
    -v, --verbose           Verbose output

Examples:
    $0                                          # Build with default settings
    $0 -t Debug --enable-testing               # Debug build with tests
    $0 --android-ndk /opt/android-ndk          # Android cross-compilation
    $0 --target-arch arm64 --cross-compile     # ARM64 cross-compilation
    $0 clean                                    # Clean build directory
    $0 install                                  # Install to system

EOF
}

# 检测CPU核心数
detect_jobs() {
    if command -v nproc >/dev/null 2>&1; then
        nproc
    elif [ -f /proc/cpuinfo ]; then
        grep -c ^processor /proc/cpuinfo
    elif command -v sysctl >/dev/null 2>&1; then
        sysctl -n hw.ncpu 2>/dev/null || echo "4"
    else
        echo "4"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    local missing_deps=()
    
    # 检查必需的工具
    for cmd in cmake make gcc; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_deps+=("$cmd")
        fi
    done
    
    # 检查OpenSSL
    if ! pkg-config --exists openssl; then
        missing_deps+=("libssl-dev")
    fi
    
    # 检查pthread
    if ! pkg-config --exists threads; then
        if [ ! -f /usr/include/pthread.h ]; then
            missing_deps+=("libpthread-stubs0-dev")
        fi
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Please install them using your package manager:"
        log_info "  Ubuntu/Debian: sudo apt-get install ${missing_deps[*]}"
        log_info "  CentOS/RHEL: sudo yum install ${missing_deps[*]}"
        log_info "  Fedora: sudo dnf install ${missing_deps[*]}"
        exit 1
    fi
    
    log_info "All dependencies satisfied"
}

# 设置Android交叉编译
setup_android_cross_compile() {
    if [ -z "$ANDROID_NDK" ]; then
        log_error "Android NDK path not specified"
        exit 1
    fi
    
    if [ ! -d "$ANDROID_NDK" ]; then
        log_error "Android NDK not found: $ANDROID_NDK"
        exit 1
    fi
    
    log_info "Setting up Android cross-compilation..."
    log_info "  NDK: $ANDROID_NDK"
    log_info "  API Level: $ANDROID_API_LEVEL"
    log_info "  Target Arch: $TARGET_ARCH"
    
    # 设置工具链文件
    TOOLCHAIN_FILE="$ANDROID_NDK/build/cmake/android.toolchain.cmake"
    if [ ! -f "$TOOLCHAIN_FILE" ]; then
        log_error "Android toolchain file not found: $TOOLCHAIN_FILE"
        exit 1
    fi
    
    # 设置Android特定的CMake参数
    CMAKE_ARGS+=(
        "-DCMAKE_TOOLCHAIN_FILE=$TOOLCHAIN_FILE"
        "-DANDROID_ABI=$TARGET_ARCH"
        "-DANDROID_PLATFORM=android-$ANDROID_API_LEVEL"
        "-DANDROID_STL=c++_shared"
    )
}

# 设置通用交叉编译
setup_cross_compile() {
    if [ -n "$ANDROID_NDK" ]; then
        setup_android_cross_compile
        return
    fi
    
    log_info "Setting up cross-compilation for $CROSS_COMPILE..."
    
    # 设置交叉编译工具
    CMAKE_ARGS+=(
        "-DCMAKE_C_COMPILER=${CROSS_COMPILE}-gcc"
        "-DCMAKE_CXX_COMPILER=${CROSS_COMPILE}-g++"
        "-DCMAKE_SYSTEM_NAME=Linux"
    )
    
    if [ -n "$TARGET_ARCH" ]; then
        case "$TARGET_ARCH" in
            arm)
                CMAKE_ARGS+=("-DCMAKE_SYSTEM_PROCESSOR=arm")
                ;;
            arm64|aarch64)
                CMAKE_ARGS+=("-DCMAKE_SYSTEM_PROCESSOR=aarch64")
                ;;
            x86)
                CMAKE_ARGS+=("-DCMAKE_SYSTEM_PROCESSOR=i386")
                ;;
            x86_64)
                CMAKE_ARGS+=("-DCMAKE_SYSTEM_PROCESSOR=x86_64")
                ;;
            *)
                log_warn "Unknown target architecture: $TARGET_ARCH"
                ;;
        esac
    fi
}

# 配置构建
configure_build() {
    log_info "Configuring build..."
    log_info "  Build type: $BUILD_TYPE"
    log_info "  Build directory: $BUILD_DIR"
    log_info "  Install prefix: $INSTALL_PREFIX"
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    # 基本CMake参数
    CMAKE_ARGS=(
        "-DCMAKE_BUILD_TYPE=$BUILD_TYPE"
        "-DCMAKE_INSTALL_PREFIX=$INSTALL_PREFIX"
        "-DBUILD_TESTING=$ENABLE_TESTING"
        "-DBUILD_EXAMPLES=$ENABLE_EXAMPLES"
        "-DBUILD_DOCS=$ENABLE_DOCS"
    )
    
    # 设置交叉编译
    if [ -n "$CROSS_COMPILE" ] || [ -n "$ANDROID_NDK" ]; then
        setup_cross_compile
    fi
    
    # 运行CMake配置
    log_debug "Running: cmake ${CMAKE_ARGS[*]} $PROJECT_ROOT"
    cmake "${CMAKE_ARGS[@]}" "$PROJECT_ROOT"
}

# 构建项目
build_project() {
    log_info "Building project..."
    
    if [ ! -f "$BUILD_DIR/Makefile" ]; then
        configure_build
    fi
    
    cd "$BUILD_DIR"
    
    # 检测并行作业数
    if [ -z "$JOBS" ]; then
        JOBS=$(detect_jobs)
    fi
    
    log_info "Using $JOBS parallel jobs"
    
    # 构建
    make -j"$JOBS"
    
    log_info "Build completed successfully"
}

# 清理构建
clean_build() {
    log_info "Cleaning build directory..."
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        log_info "Build directory cleaned"
    else
        log_info "Build directory does not exist"
    fi
}

# 安装项目
install_project() {
    log_info "Installing project..."
    
    if [ ! -f "$BUILD_DIR/Makefile" ]; then
        log_error "Project not built. Run build first."
        exit 1
    fi
    
    cd "$BUILD_DIR"
    
    # 检查权限
    if [ ! -w "$INSTALL_PREFIX" ]; then
        log_warn "Install prefix requires root privileges: $INSTALL_PREFIX"
        sudo make install
    else
        make install
    fi
    
    log_info "Installation completed"
}

# 运行测试
run_tests() {
    log_info "Running tests..."
    
    if [ "$ENABLE_TESTING" != "ON" ]; then
        log_error "Testing not enabled. Use --enable-testing option."
        exit 1
    fi
    
    if [ ! -f "$BUILD_DIR/Makefile" ]; then
        log_error "Project not built. Run build first."
        exit 1
    fi
    
    cd "$BUILD_DIR"
    ctest --output-on-failure
    
    log_info "Tests completed"
}

# 创建包
create_package() {
    log_info "Creating packages..."
    
    if [ ! -f "$BUILD_DIR/Makefile" ]; then
        log_error "Project not built. Run build first."
        exit 1
    fi
    
    cd "$BUILD_DIR"
    make package
    
    log_info "Packages created in $BUILD_DIR"
}

# 格式化代码
format_code() {
    log_info "Formatting source code..."
    
    if command -v clang-format >/dev/null 2>&1; then
        find "$PROJECT_ROOT" -name "*.c" -o -name "*.h" | xargs clang-format -i
        log_info "Code formatting completed"
    else
        log_warn "clang-format not found, skipping code formatting"
    fi
}

# 静态分析
run_lint() {
    log_info "Running static analysis..."
    
    if command -v cppcheck >/dev/null 2>&1; then
        find "$PROJECT_ROOT" -name "*.c" -o -name "*.h" | xargs cppcheck --enable=all --std=c11
        log_info "Static analysis completed"
    else
        log_warn "cppcheck not found, skipping static analysis"
    fi
}

# 生成文档
generate_docs() {
    log_info "Generating documentation..."
    
    if command -v doxygen >/dev/null 2>&1; then
        cd "$PROJECT_ROOT"
        doxygen Doxyfile
        log_info "Documentation generated in docs/"
    else
        log_warn "doxygen not found, skipping documentation generation"
    fi
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--type)
                BUILD_TYPE="$2"
                shift 2
                ;;
            -d|--build-dir)
                BUILD_DIR="$2"
                shift 2
                ;;
            -p|--prefix)
                INSTALL_PREFIX="$2"
                shift 2
                ;;
            -j|--jobs)
                JOBS="$2"
                shift 2
                ;;
            --enable-testing)
                ENABLE_TESTING="ON"
                shift
                ;;
            --enable-examples)
                ENABLE_EXAMPLES="ON"
                shift
                ;;
            --enable-docs)
                ENABLE_DOCS="ON"
                shift
                ;;
            --cross-compile)
                CROSS_COMPILE="$2"
                shift 2
                ;;
            --android-ndk)
                ANDROID_NDK="$2"
                shift 2
                ;;
            --android-api)
                ANDROID_API_LEVEL="$2"
                shift 2
                ;;
            --target-arch)
                TARGET_ARCH="$2"
                shift 2
                ;;
            -v|--verbose)
                set -x
                shift
                ;;
            build|clean|install|test|package|format|lint|docs)
                COMMAND="$1"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    local COMMAND="build"
    
    # 解析参数
    parse_args "$@"
    
    # 检查依赖（除了clean命令）
    if [ "$COMMAND" != "clean" ]; then
        check_dependencies
    fi
    
    # 执行命令
    case "$COMMAND" in
        build)
            build_project
            ;;
        clean)
            clean_build
            ;;
        install)
            install_project
            ;;
        test)
            run_tests
            ;;
        package)
            create_package
            ;;
        format)
            format_code
            ;;
        lint)
            run_lint
            ;;
        docs)
            generate_docs
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
