package com.sentientnotes.android.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.net.URI
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import org.java_websocket.client.WebSocketClient
import org.java_websocket.handshake.ServerHandshake
import com.google.gson.Gson
import com.sentientnotes.android.R
import com.sentientnotes.android.SentientNotesApplication
import com.sentientnotes.android.data.models.SNEventStream
import com.sentientnotes.android.data.repository.SettingsRepository
import com.sentientnotes.android.utils.NetworkUtils

/**
 * SoftBus服务
 * 负责设备发现、连接管理和数据传输
 * 在实际的OpenHarmony环境中，这里会使用真实的SoftBus API
 * 目前使用WebSocket模拟SoftBus功能
 */
@AndroidEntryPoint
class SoftBusService : Service() {

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "softbus_service_channel"
        private const val CHANNEL_NAME = "SoftBus服务"
        
        // 服务状态
        const val STATE_DISCONNECTED = 0
        const val STATE_CONNECTING = 1
        const val STATE_CONNECTED = 2
        const val STATE_ERROR = 3
    }

    @Inject
    lateinit var settingsRepository: SettingsRepository

    @Inject
    lateinit var gson: Gson

    private val binder = SoftBusBinder()
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // WebSocket客户端（模拟SoftBus连接）
    private var webSocketClient: WebSocketClient? = null
    
    // 连接状态
    private val _connectionState = MutableStateFlow(STATE_DISCONNECTED)
    val connectionState: StateFlow<Int> = _connectionState.asStateFlow()
    
    // 已发现的设备
    private val _discoveredDevices = MutableStateFlow<Map<String, DeviceInfo>>(emptyMap())
    val discoveredDevices: StateFlow<Map<String, DeviceInfo>> = _discoveredDevices.asStateFlow()
    
    // 设备信息缓存
    private val deviceInfoCache = ConcurrentHashMap<String, DeviceInfo>()
    
    // 消息监听器
    private val messageListeners = mutableSetOf<MessageListener>()
    
    // 设备ID
    private val deviceId: String by lazy {
        SentientNotesApplication.getInstance().deviceId
    }

    inner class SoftBusBinder : Binder() {
        fun getService(): SoftBusService = this@SoftBusService
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onCreate() {
        super.onCreate()
        Timber.i("SoftBus服务创建")
        
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 启动设备发现
        startDeviceDiscovery()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.i("SoftBus服务启动")
        return START_STICKY // 服务被杀死后自动重启
    }

    override fun onDestroy() {
        super.onDestroy()
        Timber.i("SoftBus服务销毁")
        
        // 断开连接
        disconnect()
        
        // 取消协程
        serviceScope.cancel()
        
        // 清理资源
        messageListeners.clear()
        deviceInfoCache.clear()
    }

    /**
     * 连接到服务器（模拟SoftBus连接）
     */
    fun connect(serverUrl: String = "ws://192.168.1.100:8000/ws") {
        if (_connectionState.value == STATE_CONNECTING || _connectionState.value == STATE_CONNECTED) {
            Timber.w("已经在连接或已连接状态")
            return
        }

        serviceScope.launch {
            try {
                _connectionState.value = STATE_CONNECTING
                
                val uri = URI("$serverUrl/$deviceId")
                webSocketClient = object : WebSocketClient(uri) {
                    override fun onOpen(handshake: ServerHandshake?) {
                        Timber.i("WebSocket连接已建立")
                        _connectionState.value = STATE_CONNECTED
                        
                        // 发送设备信息
                        sendDeviceInfo()
                    }

                    override fun onMessage(message: String?) {
                        message?.let { handleMessage(it) }
                    }

                    override fun onClose(code: Int, reason: String?, remote: Boolean) {
                        Timber.i("WebSocket连接关闭: $code - $reason")
                        _connectionState.value = STATE_DISCONNECTED
                    }

                    override fun onError(ex: Exception?) {
                        Timber.e(ex, "WebSocket连接错误")
                        _connectionState.value = STATE_ERROR
                    }
                }
                
                webSocketClient?.connect()
                
            } catch (e: Exception) {
                Timber.e(e, "连接失败")
                _connectionState.value = STATE_ERROR
            }
        }
    }

    /**
     * 断开连接
     */
    fun disconnect() {
        serviceScope.launch {
            try {
                webSocketClient?.close()
                webSocketClient = null
                _connectionState.value = STATE_DISCONNECTED
                Timber.i("已断开SoftBus连接")
            } catch (e: Exception) {
                Timber.e(e, "断开连接时发生错误")
            }
        }
    }

    /**
     * 发送事件流
     */
    fun sendEventStream(eventStream: SNEventStream): Boolean {
        return try {
            val message = gson.toJson(eventStream)
            webSocketClient?.send(message)
            Timber.d("发送事件流: ${eventStream.eventId}")
            true
        } catch (e: Exception) {
            Timber.e(e, "发送事件流失败")
            false
        }
    }

    /**
     * 发送原始消息
     */
    fun sendMessage(message: String): Boolean {
        return try {
            webSocketClient?.send(message)
            true
        } catch (e: Exception) {
            Timber.e(e, "发送消息失败")
            false
        }
    }

    /**
     * 添加消息监听器
     */
    fun addMessageListener(listener: MessageListener) {
        messageListeners.add(listener)
    }

    /**
     * 移除消息监听器
     */
    fun removeMessageListener(listener: MessageListener) {
        messageListeners.remove(listener)
    }

    /**
     * 开始设备发现
     */
    private fun startDeviceDiscovery() {
        serviceScope.launch {
            while (isActive) {
                try {
                    // 模拟设备发现过程
                    // 在真实的SoftBus环境中，这里会使用SoftBus的设备发现API
                    discoverDevices()
                    
                    delay(5000) // 每5秒发现一次设备
                } catch (e: Exception) {
                    Timber.e(e, "设备发现过程中发生错误")
                    delay(10000) // 错误时等待更长时间
                }
            }
        }
    }

    /**
     * 发现设备（模拟）
     */
    private suspend fun discoverDevices() {
        // 模拟发现的设备
        val mockDevices = listOf(
            DeviceInfo(
                deviceId = "mock_device_001",
                deviceName = "测试设备1",
                deviceType = "smartphone",
                capabilities = listOf("camera", "microphone", "ar"),
                isOnline = true
            ),
            DeviceInfo(
                deviceId = "mock_device_002", 
                deviceName = "测试平板",
                deviceType = "tablet",
                capabilities = listOf("camera", "stylus", "large_screen"),
                isOnline = true
            )
        )

        val currentDevices = deviceInfoCache.toMap()
        val newDevices = mutableMapOf<String, DeviceInfo>()

        mockDevices.forEach { device ->
            deviceInfoCache[device.deviceId] = device
            newDevices[device.deviceId] = device
        }

        _discoveredDevices.value = deviceInfoCache.toMap()
        
        // 通知监听器有新设备发现
        newDevices.values.forEach { device ->
            if (!currentDevices.containsKey(device.deviceId)) {
                notifyDeviceDiscovered(device)
            }
        }
    }

    /**
     * 发送设备信息
     */
    private fun sendDeviceInfo() {
        val deviceInfo = DeviceInfo(
            deviceId = deviceId,
            deviceName = getDeviceName(),
            deviceType = "smartphone",
            capabilities = getDeviceCapabilities(),
            isOnline = true
        )

        val message = mapOf(
            "type" to "device_info",
            "data" to deviceInfo
        )

        sendMessage(gson.toJson(message))
    }

    /**
     * 处理接收到的消息
     */
    private fun handleMessage(message: String) {
        try {
            serviceScope.launch {
                // 通知所有监听器
                messageListeners.forEach { listener ->
                    try {
                        listener.onMessageReceived(message)
                    } catch (e: Exception) {
                        Timber.e(e, "消息监听器处理消息时发生错误")
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "处理消息时发生错误")
        }
    }

    /**
     * 通知设备发现
     */
    private fun notifyDeviceDiscovered(device: DeviceInfo) {
        messageListeners.forEach { listener ->
            try {
                listener.onDeviceDiscovered(device)
            } catch (e: Exception) {
                Timber.e(e, "通知设备发现时发生错误")
            }
        }
    }

    /**
     * 获取设备名称
     */
    private fun getDeviceName(): String {
        return "${Build.MANUFACTURER} ${Build.MODEL}"
    }

    /**
     * 获取设备能力
     */
    private fun getDeviceCapabilities(): List<String> {
        val capabilities = mutableListOf<String>()
        
        // 检查相机
        if (packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_CAMERA)) {
            capabilities.add("camera")
        }
        
        // 检查麦克风
        if (packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_MICROPHONE)) {
            capabilities.add("microphone")
        }
        
        // 检查触控笔
        if (packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_TOUCHSCREEN)) {
            capabilities.add("touchscreen")
        }
        
        // 检查传感器
        if (packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_SENSOR_ACCELEROMETER)) {
            capabilities.add("accelerometer")
        }
        
        if (packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_SENSOR_GYROSCOPE)) {
            capabilities.add("gyroscope")
        }
        
        // 检查AR支持
        try {
            if (packageManager.hasSystemFeature("android.hardware.camera.ar")) {
                capabilities.add("ar")
            }
        } catch (e: Exception) {
            // AR功能不可用
        }
        
        return capabilities
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "SoftBus后台服务通知"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("灵境笔记")
            .setContentText("SoftBus服务运行中")
            .setSmallIcon(R.drawable.ic_notification)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }

    /**
     * 设备信息数据类
     */
    data class DeviceInfo(
        val deviceId: String,
        val deviceName: String,
        val deviceType: String,
        val capabilities: List<String>,
        val isOnline: Boolean,
        val lastSeen: Long = System.currentTimeMillis()
    )

    /**
     * 消息监听器接口
     */
    interface MessageListener {
        fun onMessageReceived(message: String)
        fun onDeviceDiscovered(device: DeviceInfo)
        fun onDeviceLost(deviceId: String)
        fun onConnectionStateChanged(state: Int)
    }
}
