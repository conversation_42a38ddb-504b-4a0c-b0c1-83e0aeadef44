/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.softbus;

import android.softbus.ISoftBusCallback;
import android.softbus.IPublishCallback;
import android.softbus.ITransmissionCallback;
import android.softbus.IDeviceStateCallback;
import android.softbus.discovery.DiscoveryConfig;
import android.softbus.connection.ConnectionConfig;
import android.softbus.device.DeviceInfo;
import android.softbus.SoftBusStatistics;

/**
 * SoftBus系统服务接口
 * 
 * 定义了SoftBus Framework与System Service之间的AIDL通信接口。
 * 提供设备发现、连接管理、数据传输和设备管理的服务端实现。
 * 
 * @hide
 */
interface ISoftBusService {
    
    // ==================== 设备发现接口 ====================
    
    /**
     * 开始设备发现
     * 
     * @param config 发现配置
     * @param callback 发现回调
     * @return 发现ID
     */
    int startDiscovery(in DiscoveryConfig config, ISoftBusCallback callback);
    
    /**
     * 停止设备发现
     * 
     * @param discoveryId 发现ID
     */
    void stopDiscovery(int discoveryId);
    
    /**
     * 发布服务能力
     * 
     * @param capability 服务能力
     * @param callback 发布回调
     * @return 发布ID
     */
    int publishService(String capability, IPublishCallback callback);
    
    /**
     * 停止发布服务
     * 
     * @param publishId 发布ID
     */
    void stopPublishService(int publishId);
    
    // ==================== 连接管理接口 ====================
    
    /**
     * 连接到指定设备
     * 
     * @param deviceId 设备ID
     * @param config 连接配置
     * @param callback 连接回调
     * @return 会话ID
     */
    String connectDevice(String deviceId, in ConnectionConfig config, ISoftBusCallback callback);
    
    /**
     * 断开设备连接
     * 
     * @param sessionId 会话ID
     */
    void disconnectDevice(String sessionId);
    
    /**
     * 创建会话服务器
     * 
     * @param sessionName 会话名称
     * @param callback 会话回调
     * @return 服务器ID
     */
    int createSessionServer(String sessionName, ISoftBusCallback callback);
    
    /**
     * 移除会话服务器
     * 
     * @param serverId 服务器ID
     */
    void removeSessionServer(int serverId);
    
    // ==================== 数据传输接口 ====================
    
    /**
     * 发送字节数据
     * 
     * @param sessionId 会话ID
     * @param data 数据
     */
    void sendBytes(String sessionId, in byte[] data);
    
    /**
     * 发送消息
     * 
     * @param sessionId 会话ID
     * @param message 消息内容
     */
    void sendMessage(String sessionId, String message);
    
    /**
     * 发送文件
     * 
     * @param sessionId 会话ID
     * @param filePath 文件路径
     * @param callback 传输回调
     */
    void sendFile(String sessionId, String filePath, ITransmissionCallback callback);
    
    /**
     * 发送流数据
     * 
     * @param sessionId 会话ID
     * @param streamData 流数据
     * @param callback 传输回调
     */
    void sendStream(String sessionId, in byte[] streamData, ITransmissionCallback callback);
    
    /**
     * 注册数据接收回调
     * 
     * @param sessionId 会话ID
     * @param callback 接收回调
     */
    void registerDataReceiver(String sessionId, ISoftBusCallback callback);
    
    /**
     * 注销数据接收回调
     * 
     * @param sessionId 会话ID
     */
    void unregisterDataReceiver(String sessionId);
    
    // ==================== 设备管理接口 ====================
    
    /**
     * 获取已连接的设备列表
     * 
     * @return 设备信息列表
     */
    List<DeviceInfo> getConnectedDevices();
    
    /**
     * 获取本地设备信息
     * 
     * @return 本地设备信息
     */
    DeviceInfo getLocalDeviceInfo();
    
    /**
     * 获取指定设备信息
     * 
     * @param deviceId 设备ID
     * @return 设备信息
     */
    DeviceInfo getDeviceInfo(String deviceId);
    
    /**
     * 注册设备状态监听器
     * 
     * @param callback 状态回调
     */
    void registerDeviceStateCallback(IDeviceStateCallback callback);
    
    /**
     * 注销设备状态监听器
     */
    void unregisterDeviceStateCallback();
    
    // ==================== 网络管理接口 ====================
    
    /**
     * 加入LNN网络
     * 
     * @param networkId 网络ID
     * @param callback 加入回调
     */
    void joinLNN(String networkId, ISoftBusCallback callback);
    
    /**
     * 离开LNN网络
     * 
     * @param networkId 网络ID
     * @param callback 离开回调
     */
    void leaveLNN(String networkId, ISoftBusCallback callback);
    
    /**
     * 获取网络信息
     * 
     * @return 网络信息列表
     */
    List<String> getNetworkList();
    
    // ==================== 安全管理接口 ====================
    
    /**
     * 设置设备认证信息
     * 
     * @param deviceId 设备ID
     * @param authInfo 认证信息
     */
    void setDeviceAuthInfo(String deviceId, in byte[] authInfo);
    
    /**
     * 获取设备认证状态
     * 
     * @param deviceId 设备ID
     * @return 认证状态
     */
    int getDeviceAuthState(String deviceId);
    
    /**
     * 清除设备认证信息
     * 
     * @param deviceId 设备ID
     */
    void clearDeviceAuthInfo(String deviceId);
    
    // ==================== 系统管理接口 ====================
    
    /**
     * 获取服务状态
     * 
     * @return 服务状态
     */
    int getServiceState();
    
    /**
     * 设置服务配置
     * 
     * @param key 配置键
     * @param value 配置值
     */
    void setServiceConfig(String key, String value);
    
    /**
     * 获取服务配置
     * 
     * @param key 配置键
     * @return 配置值
     */
    String getServiceConfig(String key);
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    SoftBusStatistics getStatistics();
    
    /**
     * 重置统计信息
     */
    void resetStatistics();
    
    /**
     * 导出调试信息
     * 
     * @return 调试信息
     */
    String dumpDebugInfo();
    
    // ==================== QoS管理接口 ====================
    
    /**
     * 设置QoS策略
     * 
     * @param sessionId 会话ID
     * @param qosType QoS类型
     * @param qosValue QoS值
     */
    void setQosPolicy(String sessionId, int qosType, int qosValue);
    
    /**
     * 获取QoS状态
     * 
     * @param sessionId 会话ID
     * @return QoS状态
     */
    int getQosState(String sessionId);
    
    // ==================== 电源管理接口 ====================
    
    /**
     * 设置电源模式
     * 
     * @param mode 电源模式
     */
    void setPowerMode(int mode);
    
    /**
     * 获取电源模式
     * 
     * @return 电源模式
     */
    int getPowerMode();
}
