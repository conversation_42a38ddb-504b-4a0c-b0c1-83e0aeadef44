--- a/frameworks/base/core/java/android/content/Context.java
+++ b/frameworks/base/core/java/android/content/Context.java
@@ -4500,6 +4500,15 @@ public abstract class Context {
     public static final String CROSS_PROFILE_APPS_SERVICE = "crossprofileapps";
 
     /**
+     * Use with {@link #getSystemService(String)} to retrieve a
+     * {@link android.softbus.SoftBusManager} for managing distributed
+     * device discovery, connection, and data transmission.
+     *
+     * @see #getSystemService(String)
+     * @see android.softbus.SoftBusManager
+     */
+    public static final String SOFTBUS_SERVICE = "softbus";
+
+    /**
      * Determine whether the given permission is allowed for a particular
      * process and user ID running in the system.
      *
