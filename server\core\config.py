"""
灵境笔记服务端配置管理
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "灵境笔记"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # 服务器配置
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # 跨域配置
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="ALLOWED_ORIGINS"
    )
    
    # 数据库配置
    # Neo4j 图数据库
    NEO4J_URI: str = Field(default="bolt://localhost:7687", env="NEO4J_URI")
    NEO4J_USER: str = Field(default="neo4j", env="NEO4J_USER")
    NEO4J_PASSWORD: str = Field(default="password", env="NEO4J_PASSWORD")
    
    # Redis 缓存
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    
    # Milvus 向量数据库
    MILVUS_HOST: str = Field(default="localhost", env="MILVUS_HOST")
    MILVUS_PORT: int = Field(default=19530, env="MILVUS_PORT")
    
    # AI模型配置
    # 多模态模型路径
    MULTIMODAL_MODEL_PATH: str = Field(
        default="./models/chinese-roberta-wwm-ext-large",
        env="MULTIMODAL_MODEL_PATH"
    )
    
    # 图像编码器
    IMAGE_ENCODER_MODEL: str = Field(
        default="openai/clip-vit-large-patch14",
        env="IMAGE_ENCODER_MODEL"
    )
    
    # 语音编码器
    AUDIO_ENCODER_MODEL: str = Field(
        default="facebook/wav2vec2-large-xlsr-53",
        env="AUDIO_ENCODER_MODEL"
    )
    
    # 语义相似度模型
    SENTENCE_TRANSFORMER_MODEL: str = Field(
        default="paraphrase-multilingual-mpnet-base-v2",
        env="SENTENCE_TRANSFORMER_MODEL"
    )
    
    # SoftBus模拟配置 (实际部署时需要替换为真实SoftBus接口)
    SOFTBUS_SIMULATION_MODE: bool = Field(default=True, env="SOFTBUS_SIMULATION_MODE")
    SOFTBUS_DISCOVERY_PORT: int = Field(default=8001, env="SOFTBUS_DISCOVERY_PORT")
    
    # 性能配置
    MAX_CONCURRENT_REQUESTS: int = Field(default=100, env="MAX_CONCURRENT_REQUESTS")
    REQUEST_TIMEOUT_SECONDS: int = Field(default=30, env="REQUEST_TIMEOUT_SECONDS")
    
    # AI处理配置
    AI_PROCESSING_TIMEOUT: int = Field(default=10, env="AI_PROCESSING_TIMEOUT")
    MAX_MULTIMODAL_FUSION_SIZE: int = Field(default=10, env="MAX_MULTIMODAL_FUSION_SIZE")
    
    # 知识图谱配置
    KNOWLEDGE_GRAPH_MAX_NODES: int = Field(default=100000, env="KNOWLEDGE_GRAPH_MAX_NODES")
    SIMILARITY_THRESHOLD: float = Field(default=0.7, env="SIMILARITY_THRESHOLD")
    
    # 协同配置
    MAX_COLLABORATIVE_USERS: int = Field(default=20, env="MAX_COLLABORATIVE_USERS")
    SYNC_INTERVAL_MS: int = Field(default=50, env="SYNC_INTERVAL_MS")
    
    # 安全配置
    SECRET_KEY: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # 文件存储配置
    UPLOAD_DIR: str = Field(default="./uploads", env="UPLOAD_DIR")
    MAX_FILE_SIZE: int = Field(default=50 * 1024 * 1024, env="MAX_FILE_SIZE")  # 50MB
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=8002, env="METRICS_PORT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.UPLOAD_DIR,
        "./models",
        "./logs",
        "./data"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# 初始化时创建目录
ensure_directories()


# AI模型配置类
class AIModelConfig:
    """AI模型相关配置"""
    
    # 多模态融合配置
    MULTIMODAL_FUSION_CONFIG = {
        "text_encoder_dim": 768,
        "image_encoder_dim": 768,
        "audio_encoder_dim": 768,
        "gesture_encoder_dim": 256,
        "fusion_dim": 1024,
        "attention_heads": 16,
        "num_layers": 12
    }
    
    # 知识图谱配置
    KNOWLEDGE_GRAPH_CONFIG = {
        "node_embedding_dim": 256,
        "edge_embedding_dim": 128,
        "gnn_hidden_dim": 256,
        "gnn_num_layers": 3,
        "max_path_length": 5
    }
    
    # 推荐系统配置
    RECOMMENDATION_CONFIG = {
        "embedding_dim": 256,
        "num_negative_samples": 5,
        "learning_rate": 0.001,
        "batch_size": 32
    }
    
    # 性能优化配置
    PERFORMANCE_CONFIG = {
        "enable_model_quantization": True,
        "enable_dynamic_batching": True,
        "max_batch_size": 16,
        "max_sequence_length": 512
    }


# SoftBus配置类
class SoftBusConfig:
    """SoftBus相关配置"""
    
    # 设备发现配置
    DISCOVERY_CONFIG = {
        "discovery_interval_ms": 1000,
        "discovery_timeout_ms": 5000,
        "max_devices": 50
    }
    
    # 传输配置
    TRANSMISSION_CONFIG = {
        "max_packet_size": 1024 * 1024,  # 1MB
        "compression_enabled": True,
        "encryption_enabled": True,
        "qos_level": "HIGH_PRIORITY"
    }
    
    # 协同配置
    COLLABORATION_CONFIG = {
        "max_concurrent_operations": 100,
        "conflict_resolution_timeout_ms": 500,
        "sync_batch_size": 10
    }


# 导出配置
__all__ = [
    "settings",
    "AIModelConfig", 
    "SoftBusConfig",
    "ensure_directories"
]
