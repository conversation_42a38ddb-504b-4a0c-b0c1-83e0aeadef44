package com.sentientnotes.android

import android.app.Application
import android.content.Context
import androidx.room.Room
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber
import com.sentientnotes.android.data.local.SentientNotesDatabase
import com.sentientnotes.android.utils.DeviceIdGenerator
import javax.inject.Inject

/**
 * 灵境笔记Android应用主类
 * 负责应用级别的初始化和依赖注入
 */
@HiltAndroidApp
class SentientNotesApplication : Application() {

    companion object {
        private lateinit var instance: SentientNotesApplication
        
        fun getInstance(): SentientNotesApplication = instance
        
        fun getContext(): Context = instance.applicationContext
    }

    // 应用级别的组件
    lateinit var database: SentientNotesDatabase
        private set
    
    lateinit var deviceId: String
        private set

    override fun onCreate() {
        super.onCreate()
        instance = this
        
        // 初始化日志
        initializeLogging()
        
        // 初始化设备ID
        initializeDeviceId()
        
        // 初始化数据库
        initializeDatabase()
        
        // 初始化其他组件
        initializeComponents()
        
        Timber.i("灵境笔记应用初始化完成")
    }

    private fun initializeLogging() {
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        } else {
            // 生产环境可以使用自定义的日志树
            Timber.plant(ReleaseTree())
        }
        
        Timber.i("日志系统初始化完成")
    }

    private fun initializeDeviceId() {
        deviceId = DeviceIdGenerator.generateDeviceId(this)
        Timber.i("设备ID: $deviceId")
    }

    private fun initializeDatabase() {
        database = Room.databaseBuilder(
            applicationContext,
            SentientNotesDatabase::class.java,
            "sentient_notes_database"
        )
            .fallbackToDestructiveMigration() // 开发阶段使用，生产环境需要实现迁移策略
            .build()
        
        Timber.i("数据库初始化完成")
    }

    private fun initializeComponents() {
        // 初始化其他应用级别的组件
        // 例如：网络监控、崩溃报告、性能监控等
        
        // 注册应用生命周期回调
        registerActivityLifecycleCallbacks(AppLifecycleCallbacks())
        
        Timber.i("应用组件初始化完成")
    }

    /**
     * 生产环境日志树
     * 只记录警告和错误级别的日志
     */
    private class ReleaseTree : Timber.Tree() {
        override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
            if (priority == android.util.Log.WARN || priority == android.util.Log.ERROR) {
                // 在生产环境中，可以将日志发送到远程服务器
                // 这里只是简单的本地日志
                android.util.Log.println(priority, tag, message)
            }
        }
    }

    /**
     * 应用生命周期回调
     * 用于监控应用的前台/后台状态
     */
    private class AppLifecycleCallbacks : ActivityLifecycleCallbacks {
        private var activityCount = 0
        private var isAppInForeground = false

        override fun onActivityStarted(activity: android.app.Activity) {
            activityCount++
            if (!isAppInForeground) {
                isAppInForeground = true
                Timber.i("应用进入前台")
                // 可以在这里处理应用进入前台的逻辑
                // 例如：重新连接WebSocket、刷新数据等
            }
        }

        override fun onActivityStopped(activity: android.app.Activity) {
            activityCount--
            if (activityCount == 0 && isAppInForeground) {
                isAppInForeground = false
                Timber.i("应用进入后台")
                // 可以在这里处理应用进入后台的逻辑
                // 例如：断开非必要连接、保存状态等
            }
        }

        override fun onActivityCreated(activity: android.app.Activity, savedInstanceState: android.os.Bundle?) {}
        override fun onActivityResumed(activity: android.app.Activity) {}
        override fun onActivityPaused(activity: android.app.Activity) {}
        override fun onActivitySaveInstanceState(activity: android.app.Activity, outState: android.os.Bundle) {}
        override fun onActivityDestroyed(activity: android.app.Activity) {}
    }

    /**
     * 获取应用是否在前台
     */
    fun isAppInForeground(): Boolean {
        val callbacks = getSystemService(Context.ACTIVITY_SERVICE) as? android.app.ActivityManager
        return callbacks?.let { am ->
            val processes = am.runningAppProcesses
            processes?.any { 
                it.processName == packageName && 
                it.importance == android.app.ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND 
            } ?: false
        } ?: false
    }

    /**
     * 获取应用版本信息
     */
    fun getVersionInfo(): Pair<String, Int> {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            Pair(packageInfo.versionName ?: "unknown", packageInfo.versionCode)
        } catch (e: Exception) {
            Timber.e(e, "获取版本信息失败")
            Pair("unknown", 0)
        }
    }

    /**
     * 获取设备信息
     */
    fun getDeviceInfo(): Map<String, String> {
        return mapOf(
            "device_id" to deviceId,
            "manufacturer" to android.os.Build.MANUFACTURER,
            "model" to android.os.Build.MODEL,
            "android_version" to android.os.Build.VERSION.RELEASE,
            "api_level" to android.os.Build.VERSION.SDK_INT.toString(),
            "app_version" to getVersionInfo().first,
            "app_version_code" to getVersionInfo().second.toString()
        )
    }

    /**
     * 清理应用数据（用于调试或重置）
     */
    suspend fun clearAppData() {
        try {
            // 清理数据库
            database.clearAllTables()
            
            // 清理SharedPreferences
            val prefs = getSharedPreferences("sentient_notes_prefs", Context.MODE_PRIVATE)
            prefs.edit().clear().apply()
            
            // 清理缓存文件
            cacheDir.deleteRecursively()
            
            Timber.i("应用数据清理完成")
        } catch (e: Exception) {
            Timber.e(e, "清理应用数据失败")
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        Timber.i("应用终止")
    }

    override fun onLowMemory() {
        super.onLowMemory()
        Timber.w("系统内存不足")
        // 可以在这里释放一些非必要的资源
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        Timber.w("系统要求释放内存，级别: $level")
        
        when (level) {
            TRIM_MEMORY_UI_HIDDEN -> {
                // UI不可见，可以释放UI相关资源
            }
            TRIM_MEMORY_RUNNING_MODERATE,
            TRIM_MEMORY_RUNNING_LOW,
            TRIM_MEMORY_RUNNING_CRITICAL -> {
                // 应用在运行但系统内存紧张，释放非必要资源
            }
            TRIM_MEMORY_BACKGROUND,
            TRIM_MEMORY_MODERATE,
            TRIM_MEMORY_COMPLETE -> {
                // 应用在后台且系统内存紧张，释放更多资源
            }
        }
    }
}
