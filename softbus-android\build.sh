#!/bin/bash

# Copyright (C) 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# SoftBus Android Framework & Service Build Script

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 默认配置
BUILD_TYPE="user"
TARGET_PRODUCT="aosp_arm64"
TARGET_BUILD_VARIANT="eng"
JOBS=$(nproc)
VERBOSE=false
CLEAN_BUILD=false
BUILD_TESTS=false
BUILD_DOCS=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
SoftBus Android Framework & Service Build Script

Usage: $0 [OPTIONS] [COMMAND]

Commands:
    build       Build SoftBus framework and service (default)
    clean       Clean build artifacts
    install     Install to Android source tree
    test        Run tests
    docs        Generate documentation
    package     Create distribution package

Options:
    -h, --help              Show this help message
    -t, --type TYPE         Build type: user, userdebug, eng (default: user)
    -p, --product PRODUCT   Target product (default: aosp_arm64)
    -v, --variant VARIANT   Build variant (default: eng)
    -j, --jobs JOBS         Number of parallel jobs (default: auto-detect)
    --clean                 Clean build before building
    --tests                 Build and run tests
    --docs                  Generate documentation
    --verbose               Verbose output
    --android-root PATH     Android source root path

Examples:
    $0                                      # Build with default settings
    $0 --type userdebug --tests            # Debug build with tests
    $0 install --android-root /path/to/aosp # Install to AOSP
    $0 clean                               # Clean build artifacts

EOF
}

# 检查Android构建环境
check_android_env() {
    if [ -z "$ANDROID_BUILD_TOP" ]; then
        if [ -n "$ANDROID_ROOT" ]; then
            export ANDROID_BUILD_TOP="$ANDROID_ROOT"
        else
            log_error "Android build environment not set up"
            log_info "Please run 'source build/envsetup.sh' in Android source tree"
            return 1
        fi
    fi
    
    if [ ! -f "$ANDROID_BUILD_TOP/build/envsetup.sh" ]; then
        log_error "Invalid Android source tree: $ANDROID_BUILD_TOP"
        return 1
    fi
    
    log_info "Android build environment: $ANDROID_BUILD_TOP"
    return 0
}

# 设置构建环境
setup_build_env() {
    log_info "Setting up build environment..."
    
    # 设置Android构建环境
    if check_android_env; then
        cd "$ANDROID_BUILD_TOP"
        source build/envsetup.sh
        lunch "${TARGET_PRODUCT}-${TARGET_BUILD_VARIANT}"
    else
        log_warn "Building without Android environment"
    fi
    
    # 设置编译标志
    export USE_CCACHE=1
    export CCACHE_DIR="$HOME/.ccache"
    
    log_info "Build environment set up successfully"
}

# 清理构建产物
clean_build() {
    log_info "Cleaning build artifacts..."
    
    if [ -n "$ANDROID_BUILD_TOP" ]; then
        cd "$ANDROID_BUILD_TOP"
        
        # 清理SoftBus相关模块
        m clean-framework-softbus
        m clean-services.softbus
        m clean-libsoftbus_core
        m clean-libsoftbus_service
        
        # 清理输出目录
        rm -rf "$ANDROID_BUILD_TOP/out/target/product/*/system/framework/framework-softbus.jar"
        rm -rf "$ANDROID_BUILD_TOP/out/target/product/*/system/lib*/libsoftbus_*.so"
    fi
    
    # 清理本地构建产物
    rm -rf "$PROJECT_ROOT/out"
    rm -rf "$PROJECT_ROOT/build"
    
    log_info "Build artifacts cleaned"
}

# 构建Framework
build_framework() {
    log_info "Building SoftBus Framework..."
    
    if [ -n "$ANDROID_BUILD_TOP" ]; then
        cd "$ANDROID_BUILD_TOP"
        
        # 构建Framework库
        m framework-softbus -j"$JOBS"
        
        # 构建权限和常量
        m softbus-permissions -j"$JOBS"
        m softbus-constants -j"$JOBS"
        m softbus-types -j"$JOBS"
        m softbus-callbacks -j"$JOBS"
        
        log_info "SoftBus Framework built successfully"
    else
        log_error "Android build environment required for Framework build"
        return 1
    fi
}

# 构建Service
build_service() {
    log_info "Building SoftBus Service..."
    
    if [ -n "$ANDROID_BUILD_TOP" ]; then
        cd "$ANDROID_BUILD_TOP"
        
        # 构建Native库
        m libsoftbus_core -j"$JOBS"
        m libsoftbus_service -j"$JOBS"
        
        # 构建Java服务
        m services.softbus -j"$JOBS"
        m softbus-native-interface -j"$JOBS"
        m softbus-discovery-manager -j"$JOBS"
        m softbus-connection-manager -j"$JOBS"
        m softbus-transmission-manager -j"$JOBS"
        m softbus-device-manager -j"$JOBS"
        m softbus-security-manager -j"$JOBS"
        
        # 构建工具和守护进程
        m softbus_tool -j"$JOBS"
        m softbusd -j"$JOBS"
        
        log_info "SoftBus Service built successfully"
    else
        log_error "Android build environment required for Service build"
        return 1
    fi
}

# 构建测试
build_tests() {
    if [ "$BUILD_TESTS" != true ]; then
        return 0
    fi
    
    log_info "Building SoftBus Tests..."
    
    if [ -n "$ANDROID_BUILD_TOP" ]; then
        cd "$ANDROID_BUILD_TOP"
        
        # 构建Native测试
        m softbus_test -j"$JOBS"
        m softbus_perf_test -j"$JOBS"
        
        # 构建Framework测试
        m SoftBusFrameworkTests -j"$JOBS"
        m SoftBusIntegrationTests -j"$JOBS"
        m SoftBusPerformanceTests -j"$JOBS"
        
        # 构建Service测试
        m SoftBusServiceTests -j"$JOBS"
        m SoftBusServiceIntegrationTests -j"$JOBS"
        m SoftBusServicePerformanceTests -j"$JOBS"
        
        log_info "SoftBus Tests built successfully"
    else
        log_error "Android build environment required for Tests build"
        return 1
    fi
}

# 运行测试
run_tests() {
    log_info "Running SoftBus Tests..."
    
    if [ -n "$ANDROID_BUILD_TOP" ]; then
        cd "$ANDROID_BUILD_TOP"
        
        # 运行Native测试
        adb push "$ANDROID_BUILD_TOP/out/target/product/*/system/bin/softbus_test" /data/local/tmp/
        adb shell chmod 755 /data/local/tmp/softbus_test
        adb shell /data/local/tmp/softbus_test
        
        # 运行Framework测试
        adb install -r "$ANDROID_BUILD_TOP/out/target/product/*/system/app/SoftBusFrameworkTests/SoftBusFrameworkTests.apk"
        adb shell am instrument -w com.android.softbus.tests/androidx.test.runner.AndroidJUnitRunner
        
        # 运行Service测试
        adb install -r "$ANDROID_BUILD_TOP/out/target/product/*/system/app/SoftBusServiceTests/SoftBusServiceTests.apk"
        adb shell am instrument -w com.android.server.softbus.tests/androidx.test.runner.AndroidJUnitRunner
        
        log_info "SoftBus Tests completed"
    else
        log_error "Android build environment required for running tests"
        return 1
    fi
}

# 生成文档
generate_docs() {
    if [ "$BUILD_DOCS" != true ]; then
        return 0
    fi
    
    log_info "Generating SoftBus Documentation..."
    
    if [ -n "$ANDROID_BUILD_TOP" ]; then
        cd "$ANDROID_BUILD_TOP"
        
        # 生成API文档
        m softbus-doc -j"$JOBS"
        
        log_info "SoftBus Documentation generated"
    else
        log_warn "Generating documentation without Android environment"
        
        # 使用Javadoc生成文档
        mkdir -p "$PROJECT_ROOT/docs"
        find "$PROJECT_ROOT/framework/java" -name "*.java" -exec javadoc -d "$PROJECT_ROOT/docs" {} +
    fi
}

# 安装到Android源码树
install_to_android() {
    if [ -z "$ANDROID_ROOT" ]; then
        log_error "Android source root not specified"
        log_info "Use --android-root option to specify Android source path"
        return 1
    fi
    
    if [ ! -d "$ANDROID_ROOT" ]; then
        log_error "Android source root does not exist: $ANDROID_ROOT"
        return 1
    fi
    
    log_info "Installing SoftBus to Android source tree: $ANDROID_ROOT"
    
    # 复制Framework文件
    mkdir -p "$ANDROID_ROOT/frameworks/base/core/java/android/softbus"
    cp -r "$PROJECT_ROOT/framework/java/android/softbus/"* "$ANDROID_ROOT/frameworks/base/core/java/android/softbus/"
    
    mkdir -p "$ANDROID_ROOT/frameworks/base/core/java/android/softbus"
    cp -r "$PROJECT_ROOT/framework/aidl/android/softbus/"* "$ANDROID_ROOT/frameworks/base/core/java/android/softbus/"
    
    # 复制Service文件
    mkdir -p "$ANDROID_ROOT/frameworks/base/services/core/java/com/android/server/softbus"
    cp -r "$PROJECT_ROOT/service/java/com/android/server/softbus/"* "$ANDROID_ROOT/frameworks/base/services/core/java/com/android/server/softbus/"
    
    mkdir -p "$ANDROID_ROOT/frameworks/base/services/core/jni"
    cp -r "$PROJECT_ROOT/service/native/"* "$ANDROID_ROOT/frameworks/base/services/core/jni/softbus/"
    
    # 复制构建文件
    cp "$PROJECT_ROOT/framework/Android.bp" "$ANDROID_ROOT/frameworks/base/core/java/android/softbus/"
    cp "$PROJECT_ROOT/service/java/Android.bp" "$ANDROID_ROOT/frameworks/base/services/core/java/com/android/server/softbus/"
    cp "$PROJECT_ROOT/service/native/Android.bp" "$ANDROID_ROOT/frameworks/base/services/core/jni/softbus/"
    
    # 应用系统补丁
    log_info "Applying system patches..."
    cd "$ANDROID_ROOT"
    
    if [ -f "$PROJECT_ROOT/system_integration/SystemServer.java.patch" ]; then
        patch -p1 < "$PROJECT_ROOT/system_integration/SystemServer.java.patch"
    fi
    
    if [ -f "$PROJECT_ROOT/system_integration/AndroidManifest.xml.patch" ]; then
        patch -p1 < "$PROJECT_ROOT/system_integration/AndroidManifest.xml.patch"
    fi
    
    if [ -f "$PROJECT_ROOT/system_integration/Context.java.patch" ]; then
        patch -p1 < "$PROJECT_ROOT/system_integration/Context.java.patch"
    fi
    
    log_info "SoftBus installed to Android source tree successfully"
}

# 创建分发包
create_package() {
    log_info "Creating SoftBus distribution package..."
    
    local package_dir="$PROJECT_ROOT/dist"
    local package_name="softbus-android-$(date +%Y%m%d)"
    
    mkdir -p "$package_dir/$package_name"
    
    # 复制源码
    cp -r "$PROJECT_ROOT/framework" "$package_dir/$package_name/"
    cp -r "$PROJECT_ROOT/service" "$package_dir/$package_name/"
    cp -r "$PROJECT_ROOT/sdk" "$package_dir/$package_name/"
    cp -r "$PROJECT_ROOT/tools" "$package_dir/$package_name/"
    cp -r "$PROJECT_ROOT/tests" "$package_dir/$package_name/"
    cp -r "$PROJECT_ROOT/docs" "$package_dir/$package_name/"
    cp -r "$PROJECT_ROOT/system_integration" "$package_dir/$package_name/"
    
    # 复制构建脚本和文档
    cp "$PROJECT_ROOT/build.sh" "$package_dir/$package_name/"
    cp "$PROJECT_ROOT/README.md" "$package_dir/$package_name/"
    
    # 创建压缩包
    cd "$package_dir"
    tar -czf "${package_name}.tar.gz" "$package_name"
    
    log_info "Distribution package created: $package_dir/${package_name}.tar.gz"
}

# 主构建函数
build_softbus() {
    log_info "Building SoftBus Android Framework & Service..."
    
    if [ "$CLEAN_BUILD" = true ]; then
        clean_build
    fi
    
    setup_build_env
    
    # 构建Framework
    build_framework
    
    # 构建Service
    build_service
    
    # 构建测试
    build_tests
    
    # 生成文档
    generate_docs
    
    log_info "SoftBus build completed successfully"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--type)
                BUILD_TYPE="$2"
                shift 2
                ;;
            -p|--product)
                TARGET_PRODUCT="$2"
                shift 2
                ;;
            -v|--variant)
                TARGET_BUILD_VARIANT="$2"
                shift 2
                ;;
            -j|--jobs)
                JOBS="$2"
                shift 2
                ;;
            --clean)
                CLEAN_BUILD=true
                shift
                ;;
            --tests)
                BUILD_TESTS=true
                shift
                ;;
            --docs)
                BUILD_DOCS=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --android-root)
                ANDROID_ROOT="$2"
                shift 2
                ;;
            build|clean|install|test|docs|package)
                COMMAND="$1"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    local COMMAND="build"
    
    # 解析参数
    parse_args "$@"
    
    # 执行命令
    case "$COMMAND" in
        build)
            build_softbus
            ;;
        clean)
            clean_build
            ;;
        install)
            install_to_android
            ;;
        test)
            BUILD_TESTS=true
            build_softbus
            run_tests
            ;;
        docs)
            BUILD_DOCS=true
            generate_docs
            ;;
        package)
            create_package
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
