// Copyright (C) 2024 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

// SoftBus核心静态库
cc_library_static {
    name: "libsoftbus_core",
    
    srcs: [
        "core/softbus_core.cpp",
        "core/discovery/discovery_manager.cpp",
        "core/discovery/wifi_discovery.cpp",
        "core/discovery/bluetooth_discovery.cpp",
        "core/discovery/ble_discovery.cpp",
        "core/discovery/usb_discovery.cpp",
        "core/connection/connection_manager.cpp",
        "core/connection/wifi_connection.cpp",
        "core/connection/bluetooth_connection.cpp",
        "core/connection/ble_connection.cpp",
        "core/connection/usb_connection.cpp",
        "core/transmission/transmission_manager.cpp",
        "core/transmission/session_manager.cpp",
        "core/transmission/channel_manager.cpp",
        "core/transmission/stream_manager.cpp",
        "core/transmission/file_manager.cpp",
        "core/authentication/auth_manager.cpp",
        "core/authentication/device_auth.cpp",
        "core/authentication/crypto_utils.cpp",
        "core/bus_center/bus_center_manager.cpp",
        "core/bus_center/lnn_manager.cpp",
        "core/bus_center/node_manager.cpp",
        "core/bus_center/time_sync.cpp",
        "adapter/android_adapter.cpp",
        "adapter/wifi_adapter.cpp",
        "adapter/bluetooth_adapter.cpp",
        "adapter/ble_adapter.cpp",
        "adapter/usb_adapter.cpp",
    ],
    
    local_include_dirs: [
        "core",
        "core/discovery",
        "core/connection",
        "core/transmission",
        "core/authentication",
        "core/bus_center",
        "adapter",
        "interfaces",
    ],
    
    export_include_dirs: [
        "core",
        "interfaces",
    ],
    
    cflags: [
        "-Wall",
        "-Wextra",
        "-Werror",
        "-DANDROID",
        "-DSOFTBUS_ANDROID_USER_SPACE",
        "-DSOFTBUS_VERSION=\"1.0.0\"",
    ],
    
    cppflags: [
        "-std=c++17",
        "-fexceptions",
        "-frtti",
    ],
    
    shared_libs: [
        "liblog",
        "libutils",
        "libbinder",
        "libcutils",
        "libbase",
        "libssl",
        "libcrypto",
    ],
    
    static_libs: [
        "libprotobuf-cpp-lite",
    ],
    
    header_libs: [
        "libnativehelper_header_only",
    ],
}

// SoftBus JNI共享库
cc_library_shared {
    name: "libsoftbus_service",
    
    srcs: [
        "interfaces/softbus_jni.cpp",
        "interfaces/softbus_native_interface.cpp",
    ],
    
    local_include_dirs: [
        "interfaces",
    ],
    
    cflags: [
        "-Wall",
        "-Wextra",
        "-Werror",
        "-DANDROID",
        "-DSOFTBUS_ANDROID_USER_SPACE",
    ],
    
    cppflags: [
        "-std=c++17",
        "-fexceptions",
        "-frtti",
    ],
    
    shared_libs: [
        "liblog",
        "libutils",
        "libbinder",
        "libcutils",
        "libbase",
        "libnativehelper",
        "libandroid_runtime",
    ],
    
    static_libs: [
        "libsoftbus_core",
    ],
    
    header_libs: [
        "jni_headers",
    ],
}

// SoftBus测试可执行文件
cc_test {
    name: "softbus_test",
    
    srcs: [
        "tests/softbus_core_test.cpp",
        "tests/discovery_manager_test.cpp",
        "tests/connection_manager_test.cpp",
        "tests/transmission_manager_test.cpp",
        "tests/auth_manager_test.cpp",
        "tests/bus_center_test.cpp",
    ],
    
    local_include_dirs: [
        "tests",
    ],
    
    cflags: [
        "-Wall",
        "-Wextra",
        "-Werror",
        "-DANDROID",
        "-DSOFTBUS_TEST",
    ],
    
    cppflags: [
        "-std=c++17",
        "-fexceptions",
        "-frtti",
    ],
    
    shared_libs: [
        "liblog",
        "libutils",
        "libbase",
    ],
    
    static_libs: [
        "libsoftbus_core",
        "libgtest",
        "libgmock",
    ],
    
    test_suites: ["device-tests"],
}

// SoftBus性能测试
cc_test {
    name: "softbus_perf_test",
    
    srcs: [
        "tests/performance/discovery_perf_test.cpp",
        "tests/performance/connection_perf_test.cpp",
        "tests/performance/transmission_perf_test.cpp",
    ],
    
    local_include_dirs: [
        "tests/performance",
    ],
    
    cflags: [
        "-Wall",
        "-Wextra",
        "-Werror",
        "-DANDROID",
        "-DSOFTBUS_PERF_TEST",
        "-O2",
    ],
    
    cppflags: [
        "-std=c++17",
        "-fexceptions",
        "-frtti",
    ],
    
    shared_libs: [
        "liblog",
        "libutils",
        "libbase",
    ],
    
    static_libs: [
        "libsoftbus_core",
        "libgtest",
        "libbenchmark",
    ],
    
    test_suites: ["device-tests"],
}

// SoftBus工具
cc_binary {
    name: "softbus_tool",
    
    srcs: [
        "tools/softbus_tool.cpp",
        "tools/discovery_tool.cpp",
        "tools/connection_tool.cpp",
        "tools/transmission_tool.cpp",
    ],
    
    local_include_dirs: [
        "tools",
    ],
    
    cflags: [
        "-Wall",
        "-Wextra",
        "-Werror",
        "-DANDROID",
        "-DSOFTBUS_TOOL",
    ],
    
    cppflags: [
        "-std=c++17",
        "-fexceptions",
        "-frtti",
    ],
    
    shared_libs: [
        "liblog",
        "libutils",
        "libbase",
    ],
    
    static_libs: [
        "libsoftbus_core",
    ],
}

// SoftBus守护进程
cc_binary {
    name: "softbusd",
    
    srcs: [
        "daemon/softbus_daemon.cpp",
        "daemon/daemon_manager.cpp",
        "daemon/service_monitor.cpp",
    ],
    
    local_include_dirs: [
        "daemon",
    ],
    
    cflags: [
        "-Wall",
        "-Wextra",
        "-Werror",
        "-DANDROID",
        "-DSOFTBUS_DAEMON",
    ],
    
    cppflags: [
        "-std=c++17",
        "-fexceptions",
        "-frtti",
    ],
    
    shared_libs: [
        "liblog",
        "libutils",
        "libbase",
        "libcutils",
    ],
    
    static_libs: [
        "libsoftbus_core",
    ],
    
    init_rc: ["softbusd.rc"],
}

// SoftBus配置文件
prebuilt_etc {
    name: "softbus.conf",
    src: "config/softbus.conf",
    sub_dir: "softbus",
}

// SoftBus权限配置
prebuilt_etc {
    name: "softbus_permissions.xml",
    src: "config/softbus_permissions.xml",
    sub_dir: "permissions",
}

// SoftBus SELinux策略
prebuilt_etc {
    name: "softbus.te",
    src: "sepolicy/softbus.te",
    sub_dir: "sepolicy",
}
