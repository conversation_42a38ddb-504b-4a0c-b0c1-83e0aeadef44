--- a/frameworks/base/services/java/com/android/server/SystemServer.java
+++ b/frameworks/base/services/java/com/android/server/SystemServer.java
@@ -100,6 +100,7 @@ import com.android.server.rollback.RollbackManagerService;
 import com.android.server.search.SearchManagerService;
 import com.android.server.security.KeyAttestationApplicationIdProviderService;
 import com.android.server.security.KeyChainSystemService;
+import com.android.server.softbus.SoftBusService;
 import com.android.server.soundtrigger.SoundTriggerService;
 import com.android.server.stats.StatsCompanionService;
 import com.android.server.stats.StatsManagerService;
@@ -1450,6 +1451,15 @@ public final class SystemServer {
             }
             traceEnd();
 
+            // SoftBus Service
+            if (mPackageManager.hasSystemFeature(PackageManager.FEATURE_SOFTBUS)) {
+                traceBeginAndSlog("StartSoftBusService");
+                try {
+                    ServiceManager.addService(Context.SOFTBUS_SERVICE, new SoftBusService(context));
+                } catch (Throwable e) {
+                    reportWtf("starting SoftBus Service", e);
+                }
+                traceEnd();
+            }
+
             traceBeginAndSlog("StartNetworkTimeUpdateService");
             try {
                 networkTimeUpdater = new NetworkTimeUpdateService(context);
