/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.softbus;

import android.annotation.NonNull;
import android.annotation.Nullable;
import android.annotation.RequiresPermission;
import android.annotation.SystemApi;
import android.annotation.SystemService;
import android.content.Context;
import android.os.Binder;
import android.os.RemoteException;
import android.softbus.discovery.DiscoveryCallback;
import android.softbus.discovery.DiscoveryConfig;
import android.softbus.connection.ConnectionCallback;
import android.softbus.connection.ConnectionConfig;
import android.softbus.transmission.TransmissionCallback;
import android.softbus.transmission.TransmissionConfig;
import android.softbus.device.DeviceInfo;
import android.softbus.device.DeviceStateCallback;
import android.util.Log;

import java.util.List;
import java.util.concurrent.Executor;

/**
 * SoftBus Manager - Android分布式软总线管理器
 * 
 * 提供设备发现、连接管理、数据传输和设备管理的统一API接口。
 * 基于OpenHarmony DSoftBus架构，为Android应用提供分布式能力。
 * 
 * @hide
 */
@SystemApi
@SystemService(Context.SOFTBUS_SERVICE)
public final class SoftBusManager {
    
    private static final String TAG = "SoftBusManager";
    
    /** SoftBus服务名称 */
    public static final String SOFTBUS_SERVICE = "softbus";
    
    /** 权限：访问SoftBus服务 */
    public static final String PERMISSION_ACCESS_SOFTBUS = "android.permission.ACCESS_SOFTBUS";
    
    /** 权限：SoftBus设备发现 */
    public static final String PERMISSION_SOFTBUS_DISCOVERY = "android.permission.SOFTBUS_DISCOVERY";
    
    /** 权限：SoftBus数据传输 */
    public static final String PERMISSION_SOFTBUS_TRANSMISSION = "android.permission.SOFTBUS_TRANSMISSION";
    
    private final Context mContext;
    private final ISoftBusService mService;
    
    /**
     * 构造函数
     * 
     * @param context Android上下文
     * @param service SoftBus服务接口
     * @hide
     */
    public SoftBusManager(@NonNull Context context, @NonNull ISoftBusService service) {
        mContext = context;
        mService = service;
    }
    
    // ==================== 设备发现 API ====================
    
    /**
     * 开始设备发现
     * 
     * @param config 发现配置
     * @param executor 回调执行器
     * @param callback 发现回调
     * @return 发现ID，用于停止发现
     * @throws SecurityException 如果没有相应权限
     */
    @RequiresPermission(PERMISSION_SOFTBUS_DISCOVERY)
    public int startDiscovery(@NonNull DiscoveryConfig config, 
                             @NonNull Executor executor,
                             @NonNull DiscoveryCallback callback) {
        try {
            ISoftBusCallback serviceCallback = new ISoftBusCallback.Stub() {
                @Override
                public void onDeviceFound(DeviceInfo device) {
                    executor.execute(() -> callback.onDeviceFound(device));
                }
                
                @Override
                public void onDiscoveryStateChanged(int discoveryId, int state, int reason) {
                    executor.execute(() -> {
                        switch (state) {
                            case SoftBusConstants.DISCOVERY_STATE_STARTED:
                                callback.onDiscoveryStarted(discoveryId);
                                break;
                            case SoftBusConstants.DISCOVERY_STATE_STOPPED:
                                callback.onDiscoveryStopped(discoveryId);
                                break;
                            case SoftBusConstants.DISCOVERY_STATE_FAILED:
                                callback.onDiscoveryFailed(discoveryId, reason);
                                break;
                        }
                    });
                }
                
                @Override
                public void onConnectionStateChanged(String sessionId, int state, int reason) {
                    // 设备发现不处理连接状态
                }
                
                @Override
                public void onDataReceived(String sessionId, byte[] data, int dataType) {
                    // 设备发现不处理数据接收
                }
            };
            
            return mService.startDiscovery(config, serviceCallback);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to start discovery", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 停止设备发现
     * 
     * @param discoveryId 发现ID
     * @throws SecurityException 如果没有相应权限
     */
    @RequiresPermission(PERMISSION_SOFTBUS_DISCOVERY)
    public void stopDiscovery(int discoveryId) {
        try {
            mService.stopDiscovery(discoveryId);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to stop discovery", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 发布服务能力
     * 
     * @param capability 服务能力
     * @param executor 回调执行器
     * @param callback 发布回调
     * @return 发布ID
     */
    @RequiresPermission(PERMISSION_SOFTBUS_DISCOVERY)
    public int publishService(@NonNull String capability,
                             @NonNull Executor executor,
                             @NonNull PublishCallback callback) {
        try {
            return mService.publishService(capability, new IPublishCallback.Stub() {
                @Override
                public void onPublishResult(int publishId, int result) {
                    executor.execute(() -> callback.onPublishResult(publishId, result));
                }
            });
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to publish service", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 停止发布服务
     * 
     * @param publishId 发布ID
     */
    @RequiresPermission(PERMISSION_SOFTBUS_DISCOVERY)
    public void stopPublishService(int publishId) {
        try {
            mService.stopPublishService(publishId);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to stop publish service", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    // ==================== 连接管理 API ====================
    
    /**
     * 连接到指定设备
     * 
     * @param deviceId 设备ID
     * @param config 连接配置
     * @param executor 回调执行器
     * @param callback 连接回调
     * @return 会话ID
     */
    @RequiresPermission(PERMISSION_ACCESS_SOFTBUS)
    public String connectDevice(@NonNull String deviceId,
                               @NonNull ConnectionConfig config,
                               @NonNull Executor executor,
                               @NonNull ConnectionCallback callback) {
        try {
            ISoftBusCallback serviceCallback = new ISoftBusCallback.Stub() {
                @Override
                public void onDeviceFound(DeviceInfo device) {
                    // 连接管理不处理设备发现
                }
                
                @Override
                public void onDiscoveryStateChanged(int discoveryId, int state, int reason) {
                    // 连接管理不处理发现状态
                }
                
                @Override
                public void onConnectionStateChanged(String sessionId, int state, int reason) {
                    executor.execute(() -> {
                        switch (state) {
                            case SoftBusConstants.CONNECTION_STATE_CONNECTING:
                                callback.onConnecting(sessionId);
                                break;
                            case SoftBusConstants.CONNECTION_STATE_CONNECTED:
                                callback.onConnected(sessionId);
                                break;
                            case SoftBusConstants.CONNECTION_STATE_DISCONNECTED:
                                callback.onDisconnected(sessionId, reason);
                                break;
                            case SoftBusConstants.CONNECTION_STATE_FAILED:
                                callback.onConnectionFailed(sessionId, reason);
                                break;
                        }
                    });
                }
                
                @Override
                public void onDataReceived(String sessionId, byte[] data, int dataType) {
                    // 连接管理不处理数据接收
                }
            };
            
            return mService.connectDevice(deviceId, config, serviceCallback);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to connect device", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 断开设备连接
     * 
     * @param sessionId 会话ID
     */
    @RequiresPermission(PERMISSION_ACCESS_SOFTBUS)
    public void disconnectDevice(@NonNull String sessionId) {
        try {
            mService.disconnectDevice(sessionId);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to disconnect device", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    // ==================== 数据传输 API ====================
    
    /**
     * 发送字节数据
     * 
     * @param sessionId 会话ID
     * @param data 数据
     */
    @RequiresPermission(PERMISSION_SOFTBUS_TRANSMISSION)
    public void sendBytes(@NonNull String sessionId, @NonNull byte[] data) {
        try {
            mService.sendBytes(sessionId, data);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to send bytes", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 发送消息
     * 
     * @param sessionId 会话ID
     * @param message 消息内容
     */
    @RequiresPermission(PERMISSION_SOFTBUS_TRANSMISSION)
    public void sendMessage(@NonNull String sessionId, @NonNull String message) {
        try {
            mService.sendMessage(sessionId, message);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to send message", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 发送文件
     * 
     * @param sessionId 会话ID
     * @param filePath 文件路径
     * @param executor 回调执行器
     * @param callback 传输回调
     */
    @RequiresPermission(PERMISSION_SOFTBUS_TRANSMISSION)
    public void sendFile(@NonNull String sessionId, 
                        @NonNull String filePath,
                        @NonNull Executor executor,
                        @NonNull TransmissionCallback callback) {
        try {
            ITransmissionCallback serviceCallback = new ITransmissionCallback.Stub() {
                @Override
                public void onTransmissionProgress(String sessionId, long bytesTransferred, long totalBytes) {
                    executor.execute(() -> callback.onProgress(sessionId, bytesTransferred, totalBytes));
                }
                
                @Override
                public void onTransmissionResult(String sessionId, int result) {
                    executor.execute(() -> callback.onResult(sessionId, result));
                }
            };
            
            mService.sendFile(sessionId, filePath, serviceCallback);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to send file", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 注册数据接收回调
     * 
     * @param sessionId 会话ID
     * @param executor 回调执行器
     * @param callback 接收回调
     */
    @RequiresPermission(PERMISSION_SOFTBUS_TRANSMISSION)
    public void registerDataReceiver(@NonNull String sessionId,
                                   @NonNull Executor executor,
                                   @NonNull DataReceiveCallback callback) {
        try {
            ISoftBusCallback serviceCallback = new ISoftBusCallback.Stub() {
                @Override
                public void onDeviceFound(DeviceInfo device) {
                    // 数据接收不处理设备发现
                }
                
                @Override
                public void onDiscoveryStateChanged(int discoveryId, int state, int reason) {
                    // 数据接收不处理发现状态
                }
                
                @Override
                public void onConnectionStateChanged(String sessionId, int state, int reason) {
                    // 数据接收不处理连接状态
                }
                
                @Override
                public void onDataReceived(String sessionId, byte[] data, int dataType) {
                    executor.execute(() -> {
                        switch (dataType) {
                            case SoftBusConstants.DATA_TYPE_BYTES:
                                callback.onBytesReceived(sessionId, data);
                                break;
                            case SoftBusConstants.DATA_TYPE_MESSAGE:
                                callback.onMessageReceived(sessionId, new String(data));
                                break;
                            case SoftBusConstants.DATA_TYPE_FILE:
                                callback.onFileReceived(sessionId, new String(data));
                                break;
                        }
                    });
                }
            };
            
            mService.registerDataReceiver(sessionId, serviceCallback);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to register data receiver", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    // ==================== 设备管理 API ====================
    
    /**
     * 获取已连接的设备列表
     * 
     * @return 设备信息列表
     */
    @RequiresPermission(PERMISSION_ACCESS_SOFTBUS)
    @NonNull
    public List<DeviceInfo> getConnectedDevices() {
        try {
            return mService.getConnectedDevices();
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to get connected devices", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 获取本地设备信息
     * 
     * @return 本地设备信息
     */
    @RequiresPermission(PERMISSION_ACCESS_SOFTBUS)
    @NonNull
    public DeviceInfo getLocalDeviceInfo() {
        try {
            return mService.getLocalDeviceInfo();
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to get local device info", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 注册设备状态监听器
     * 
     * @param executor 回调执行器
     * @param callback 状态回调
     */
    @RequiresPermission(PERMISSION_ACCESS_SOFTBUS)
    public void registerDeviceStateCallback(@NonNull Executor executor,
                                          @NonNull DeviceStateCallback callback) {
        try {
            IDeviceStateCallback serviceCallback = new IDeviceStateCallback.Stub() {
                @Override
                public void onDeviceOnline(DeviceInfo device) {
                    executor.execute(() -> callback.onDeviceOnline(device));
                }
                
                @Override
                public void onDeviceOffline(String deviceId) {
                    executor.execute(() -> callback.onDeviceOffline(deviceId));
                }
                
                @Override
                public void onDeviceInfoChanged(DeviceInfo device) {
                    executor.execute(() -> callback.onDeviceInfoChanged(device));
                }
            };
            
            mService.registerDeviceStateCallback(serviceCallback);
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to register device state callback", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 注销设备状态监听器
     * 
     * @param callback 状态回调
     */
    @RequiresPermission(PERMISSION_ACCESS_SOFTBUS)
    public void unregisterDeviceStateCallback(@NonNull DeviceStateCallback callback) {
        try {
            // 实现注销逻辑
            mService.unregisterDeviceStateCallback();
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to unregister device state callback", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    // ==================== 系统管理 API ====================
    
    /**
     * 获取SoftBus服务状态
     * 
     * @return 服务状态
     */
    @RequiresPermission(PERMISSION_ACCESS_SOFTBUS)
    public int getServiceState() {
        try {
            return mService.getServiceState();
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to get service state", e);
            throw e.rethrowFromSystemServer();
        }
    }
    
    /**
     * 获取SoftBus统计信息
     * 
     * @return 统计信息
     */
    @RequiresPermission(PERMISSION_ACCESS_SOFTBUS)
    @Nullable
    public SoftBusStatistics getStatistics() {
        try {
            return mService.getStatistics();
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to get statistics", e);
            throw e.rethrowFromSystemServer();
        }
    }
}
