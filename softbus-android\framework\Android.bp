// Copyright (C) 2024 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["Android-Apache-2.0"],
}

// SoftBus Framework Java库
java_library {
    name: "framework-softbus",
    
    srcs: [
        "java/**/*.java",
        ":framework-softbus-aidl",
    ],
    
    aidl: {
        local_include_dirs: ["aidl"],
        include_dirs: [
            "frameworks/base/core/java",
        ],
    },
    
    libs: [
        "framework-annotations-lib",
        "unsupportedappusage",
    ],
    
    static_libs: [
        "modules-utils-build",
    ],
    
    installable: true,
    
    plugins: ["java_api_finder"],
    
    lint: {
        strict_updatability_linting: true,
    },
    
    visibility: [
        "//frameworks/base",
        "//frameworks/base/services",
    ],
}

// SoftBus AIDL文件
filegroup {
    name: "framework-softbus-aidl",
    srcs: [
        "aidl/android/softbus/*.aidl",
        "aidl/android/softbus/discovery/*.aidl",
        "aidl/android/softbus/connection/*.aidl",
        "aidl/android/softbus/transmission/*.aidl",
        "aidl/android/softbus/device/*.aidl",
    ],
    path: "aidl",
}

// SoftBus API文档
droiddoc_host {
    name: "softbus-doc",
    srcs: [
        ":framework-softbus-aidl",
        "java/**/*.java",
    ],
    libs: [
        "framework-annotations-lib",
    ],
    custom_template: "droiddoc-templates-sdk",
    hdf: [
        "android.hdf",
    ],
    knowntags: [
        "knowntags.txt",
    ],
    proofread_file: "softbus-proofread.txt",
    todo_file: "softbus-docs-todo.html",
    args: "-offlinemode -title \"SoftBus Framework API\"",
}

// SoftBus权限定义
java_library {
    name: "softbus-permissions",
    srcs: [
        "java/android/softbus/SoftBusPermissions.java",
    ],
    libs: [
        "framework-annotations-lib",
    ],
    installable: true,
}

// SoftBus常量定义
java_library {
    name: "softbus-constants",
    srcs: [
        "java/android/softbus/SoftBusConstants.java",
    ],
    libs: [
        "framework-annotations-lib",
    ],
    installable: true,
}

// SoftBus数据类型
java_library {
    name: "softbus-types",
    srcs: [
        "java/android/softbus/device/*.java",
        "java/android/softbus/discovery/*.java",
        "java/android/softbus/connection/*.java",
        "java/android/softbus/transmission/*.java",
    ],
    libs: [
        "framework-annotations-lib",
    ],
    static_libs: [
        "softbus-constants",
    ],
    installable: true,
}

// SoftBus回调接口
java_library {
    name: "softbus-callbacks",
    srcs: [
        "java/android/softbus/*Callback.java",
    ],
    libs: [
        "framework-annotations-lib",
    ],
    static_libs: [
        "softbus-types",
    ],
    installable: true,
}

// SoftBus测试工具
java_library {
    name: "softbus-test-utils",
    srcs: [
        "tests/java/**/*.java",
    ],
    libs: [
        "framework-softbus",
        "junit",
        "mockito-target",
    ],
    installable: false,
}

// SoftBus单元测试
android_test {
    name: "SoftBusFrameworkTests",
    srcs: [
        "tests/java/**/*Test.java",
    ],
    libs: [
        "android.test.runner",
        "android.test.base",
    ],
    static_libs: [
        "framework-softbus",
        "softbus-test-utils",
        "androidx.test.rules",
        "mockito-target-minus-junit4",
        "truth-prebuilt",
    ],
    test_suites: ["device-tests"],
    platform_apis: true,
}

// SoftBus集成测试
android_test {
    name: "SoftBusIntegrationTests",
    srcs: [
        "tests/integration/**/*.java",
    ],
    libs: [
        "android.test.runner",
        "android.test.base",
    ],
    static_libs: [
        "framework-softbus",
        "softbus-test-utils",
        "androidx.test.rules",
        "androidx.test.uiautomator_uiautomator",
    ],
    test_suites: ["device-tests"],
    platform_apis: true,
}

// SoftBus性能测试
android_test {
    name: "SoftBusPerformanceTests",
    srcs: [
        "tests/performance/**/*.java",
    ],
    libs: [
        "android.test.runner",
        "android.test.base",
    ],
    static_libs: [
        "framework-softbus",
        "androidx.test.rules",
        "androidx.benchmark_benchmark-junit4",
    ],
    test_suites: ["device-tests"],
    platform_apis: true,
}
