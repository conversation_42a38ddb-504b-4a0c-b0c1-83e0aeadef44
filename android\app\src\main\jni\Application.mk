# Copyright (c) 2024 SentientNotes Project
# Licensed under the Apache License, Version 2.0 (the "License");
# 
# SoftBus Native Application.mk Build Configuration
# 基于OpenHarmony SoftBus架构移植到Android用户态

# 应用程序ABI
APP_ABI := arm64-v8a armeabi-v7a x86 x86_64

# 平台版本
APP_PLATFORM := android-26

# STL库
APP_STL := c++_shared

# 优化级别
APP_OPTIM := release

# C++标准
APP_CPPFLAGS := -std=c++17

# 启用异常处理
APP_CPPFLAGS += -fexceptions

# 启用RTTI
APP_CPPFLAGS += -frtti

# 调试信息
APP_CPPFLAGS += -g

# 警告设置
APP_CFLAGS := -Wall -Wextra -Werror

# 定义宏
APP_CFLAGS += -DANDROID_NDK
APP_CFLAGS += -DSOFTBUS_ANDROID_USER_SPACE
APP_CFLAGS += -DSOFTBUS_VERSION=\"1.0.0\"

# 链接器标志
APP_LDFLAGS := -Wl,--gc-sections

# 模块列表
APP_MODULES := softbus_client

# 构建配置
ifeq ($(NDK_DEBUG),1)
    APP_OPTIM := debug
    APP_CFLAGS += -DDEBUG -O0
    APP_CPPFLAGS += -DDEBUG -O0
else
    APP_OPTIM := release
    APP_CFLAGS += -DNDEBUG -O2
    APP_CPPFLAGS += -DNDEBUG -O2
endif

# 启用测试构建（可选）
ifeq ($(BUILD_TESTS),true)
    APP_MODULES += softbus_test
    APP_CFLAGS += -DBUILD_SOFTBUS_TESTS=true
endif

# 短命令
APP_SHORT_COMMANDS := true

# 并行构建
APP_JOBS := $(shell nproc)

# 构建脚本
APP_BUILD_SCRIPT := Android.mk
