package com.sentientnotes.android.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.sentientnotes.android.SentientNotesApplication
import com.sentientnotes.android.data.repository.NotesRepository
import com.sentientnotes.android.services.SoftBusService
import javax.inject.Inject

/**
 * 主界面ViewModel
 * 管理主界面的状态和业务逻辑
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val notesRepository: NotesRepository,
    private val gson: Gson
) : ViewModel() {

    // 设备ID
    val deviceId: String = SentientNotesApplication.getInstance().deviceId

    // UI状态
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()

    init {
        Timber.i("MainViewModel初始化，设备ID: $deviceId")
        
        // 初始化时加载数据
        loadInitialData()
    }

    /**
     * 加载初始数据
     */
    private fun loadInitialData() {
        viewModelScope.launch {
            try {
                // 加载笔记数据等
                Timber.i("加载初始数据完成")
            } catch (e: Exception) {
                Timber.e(e, "加载初始数据失败")
                updateErrorState("加载数据失败: ${e.message}")
            }
        }
    }

    /**
     * 处理SoftBus消息
     */
    fun handleSoftBusMessage(message: String) {
        viewModelScope.launch {
            try {
                Timber.d("收到SoftBus消息: $message")
                
                // 解析消息
                val messageData = gson.fromJson(message, Map::class.java)
                val messageType = messageData["type"] as? String
                
                when (messageType) {
                    "connection_established" -> {
                        handleConnectionEstablished(messageData)
                    }
                    "device_info" -> {
                        handleDeviceInfo(messageData)
                    }
                    "note_update" -> {
                        handleNoteUpdate(messageData)
                    }
                    "ai_insight" -> {
                        handleAIInsight(messageData)
                    }
                    "error" -> {
                        handleError(messageData)
                    }
                    else -> {
                        Timber.w("未知消息类型: $messageType")
                    }
                }
                
            } catch (e: JsonSyntaxException) {
                Timber.e(e, "解析SoftBus消息失败")
            } catch (e: Exception) {
                Timber.e(e, "处理SoftBus消息时发生错误")
            }
        }
    }

    /**
     * 处理连接建立消息
     */
    private fun handleConnectionEstablished(messageData: Map<*, *>) {
        Timber.i("连接已建立")
        _uiState.value = _uiState.value.copy(
            connectionState = SoftBusService.STATE_CONNECTED,
            lastMessage = "连接已建立",
            errorMessage = null
        )
    }

    /**
     * 处理设备信息消息
     */
    private fun handleDeviceInfo(messageData: Map<*, *>) {
        try {
            val data = messageData["data"] as? Map<*, *>
            if (data != null) {
                val deviceInfo = SoftBusService.DeviceInfo(
                    deviceId = data["deviceId"] as? String ?: "",
                    deviceName = data["deviceName"] as? String ?: "",
                    deviceType = data["deviceType"] as? String ?: "",
                    capabilities = (data["capabilities"] as? List<*>)?.mapNotNull { it as? String } ?: emptyList(),
                    isOnline = data["isOnline"] as? Boolean ?: false
                )
                
                onDeviceDiscovered(deviceInfo)
            }
        } catch (e: Exception) {
            Timber.e(e, "处理设备信息时发生错误")
        }
    }

    /**
     * 处理笔记更新消息
     */
    private fun handleNoteUpdate(messageData: Map<*, *>) {
        Timber.i("收到笔记更新")
        // 这里可以处理来自其他设备的笔记更新
        _uiState.value = _uiState.value.copy(
            lastMessage = "收到笔记更新"
        )
    }

    /**
     * 处理AI洞察消息
     */
    private fun handleAIInsight(messageData: Map<*, *>) {
        Timber.i("收到AI洞察")
        // 这里可以处理AI生成的洞察信息
        _uiState.value = _uiState.value.copy(
            lastMessage = "收到AI洞察"
        )
    }

    /**
     * 处理错误消息
     */
    private fun handleError(messageData: Map<*, *>) {
        val errorMessage = messageData["message"] as? String ?: "未知错误"
        Timber.e("收到错误消息: $errorMessage")
        updateErrorState(errorMessage)
    }

    /**
     * 设备发现回调
     */
    fun onDeviceDiscovered(device: SoftBusService.DeviceInfo) {
        Timber.i("发现设备: ${device.deviceName} (${device.deviceId})")
        
        val currentDevices = _uiState.value.discoveredDevices.toMutableMap()
        currentDevices[device.deviceId] = device
        
        _uiState.value = _uiState.value.copy(
            discoveredDevices = currentDevices,
            lastMessage = "发现设备: ${device.deviceName}"
        )
    }

    /**
     * 设备丢失回调
     */
    fun onDeviceLost(deviceId: String) {
        Timber.i("设备丢失: $deviceId")
        
        val currentDevices = _uiState.value.discoveredDevices.toMutableMap()
        val lostDevice = currentDevices.remove(deviceId)
        
        _uiState.value = _uiState.value.copy(
            discoveredDevices = currentDevices,
            lastMessage = "设备离线: ${lostDevice?.deviceName ?: deviceId}"
        )
    }

    /**
     * 连接状态变化回调
     */
    fun onConnectionStateChanged(state: Int) {
        Timber.i("连接状态变化: $state")
        
        val stateMessage = when (state) {
            SoftBusService.STATE_CONNECTED -> "已连接"
            SoftBusService.STATE_CONNECTING -> "连接中"
            SoftBusService.STATE_DISCONNECTED -> "已断开"
            SoftBusService.STATE_ERROR -> "连接错误"
            else -> "未知状态"
        }
        
        _uiState.value = _uiState.value.copy(
            connectionState = state,
            lastMessage = "连接状态: $stateMessage",
            errorMessage = if (state == SoftBusService.STATE_ERROR) "连接发生错误" else null
        )
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * 更新错误状态
     */
    private fun updateErrorState(message: String) {
        _uiState.value = _uiState.value.copy(
            errorMessage = message,
            isLoading = false
        )
    }

    /**
     * 刷新设备列表
     */
    fun refreshDevices() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // 这里可以触发重新发现设备
                Timber.i("刷新设备列表")
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    lastMessage = "设备列表已刷新"
                )
                
            } catch (e: Exception) {
                Timber.e(e, "刷新设备列表失败")
                updateErrorState("刷新失败: ${e.message}")
            }
        }
    }

    /**
     * 获取应用统计信息
     */
    fun getAppStats(): Map<String, Any> {
        return mapOf(
            "device_id" to deviceId,
            "discovered_devices_count" to _uiState.value.discoveredDevices.size,
            "connection_state" to _uiState.value.connectionState,
            "last_message" to (_uiState.value.lastMessage ?: "无"),
            "has_error" to (_uiState.value.errorMessage != null)
        )
    }

    override fun onCleared() {
        super.onCleared()
        Timber.i("MainViewModel清理")
    }
}

/**
 * 主界面UI状态
 */
data class MainUiState(
    val isLoading: Boolean = false,
    val connectionState: Int = SoftBusService.STATE_DISCONNECTED,
    val discoveredDevices: Map<String, SoftBusService.DeviceInfo> = emptyMap(),
    val lastMessage: String? = null,
    val errorMessage: String? = null
)
