package com.sentientnotes.android.softbus.core

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject
import javax.inject.Singleton

import com.sentientnotes.android.softbus.discovery.DiscoveryManager
import com.sentientnotes.android.softbus.connection.ConnectionManager
import com.sentientnotes.android.softbus.transmission.TransmissionManager
import com.sentientnotes.android.softbus.security.SecurityManager
import com.sentientnotes.android.softbus.protocol.ProtocolManager
import com.sentientnotes.android.softbus.common.*

/**
 * SoftBus核心管理器
 * 基于OpenEuler SoftBus架构实现的Android用户态移植
 * 
 * 主要功能：
 * 1. 设备发现和管理
 * 2. 连接建立和维护
 * 3. 数据传输和路由
 * 4. 安全认证和加密
 * 5. 协议适配和转换
 */
@Singleton
class SoftBusManager @Inject constructor(
    private val context: Context,
    private val discoveryManager: DiscoveryManager,
    private val connectionManager: ConnectionManager,
    private val transmissionManager: TransmissionManager,
    private val securityManager: SecurityManager,
    private val protocolManager: ProtocolManager
) {
    
    companion object {
        private const val TAG = "SoftBusManager"
        private const val SOFTBUS_VERSION = "1.0.0"
        private const val MAX_CONCURRENT_CONNECTIONS = 32
        private const val HEARTBEAT_INTERVAL_MS = 30000L
        private const val DISCOVERY_INTERVAL_MS = 5000L
    }

    // 管理器状态
    private val _isInitialized = AtomicBoolean(false)
    private val _isStarted = AtomicBoolean(false)
    
    // 协程作用域
    private val managerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 状态流
    private val _busState = MutableStateFlow(SoftBusState.STOPPED)
    val busState: StateFlow<SoftBusState> = _busState.asStateFlow()
    
    // 设备管理
    private val _discoveredDevices = MutableStateFlow<Map<String, DeviceInfo>>(emptyMap())
    val discoveredDevices: StateFlow<Map<String, DeviceInfo>> = _discoveredDevices.asStateFlow()
    
    private val _connectedDevices = MutableStateFlow<Map<String, ConnectionInfo>>(emptyMap())
    val connectedDevices: StateFlow<Map<String, ConnectionInfo>> = _connectedDevices.asStateFlow()
    
    // 内部状态
    private val deviceCache = ConcurrentHashMap<String, DeviceInfo>()
    private val connectionCache = ConcurrentHashMap<String, ConnectionInfo>()
    private val sessionCounter = AtomicInteger(0)
    
    // 事件监听器
    private val eventListeners = mutableSetOf<SoftBusEventListener>()
    
    // 本地设备信息
    private lateinit var localDeviceInfo: DeviceInfo
    
    /**
     * 初始化SoftBus
     */
    suspend fun initialize(): Result<Unit> {
        if (_isInitialized.get()) {
            return Result.success(Unit)
        }
        
        return try {
            Timber.i("$TAG: 开始初始化SoftBus...")
            
            // 1. 初始化本地设备信息
            initializeLocalDevice()
            
            // 2. 初始化各个子模块
            securityManager.initialize(localDeviceInfo)
            discoveryManager.initialize(localDeviceInfo)
            connectionManager.initialize(localDeviceInfo)
            transmissionManager.initialize(localDeviceInfo)
            protocolManager.initialize(localDeviceInfo)
            
            // 3. 设置模块间的回调
            setupModuleCallbacks()
            
            _isInitialized.set(true)
            _busState.value = SoftBusState.INITIALIZED
            
            Timber.i("$TAG: SoftBus初始化完成")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: SoftBus初始化失败")
            Result.failure(e)
        }
    }
    
    /**
     * 启动SoftBus服务
     */
    suspend fun start(): Result<Unit> {
        if (!_isInitialized.get()) {
            return Result.failure(IllegalStateException("SoftBus未初始化"))
        }
        
        if (_isStarted.get()) {
            return Result.success(Unit)
        }
        
        return try {
            Timber.i("$TAG: 启动SoftBus服务...")
            
            // 1. 启动各个子模块
            discoveryManager.start()
            connectionManager.start()
            transmissionManager.start()
            
            // 2. 启动后台任务
            startBackgroundTasks()
            
            _isStarted.set(true)
            _busState.value = SoftBusState.RUNNING
            
            // 3. 开始设备发现
            startDeviceDiscovery()
            
            Timber.i("$TAG: SoftBus服务启动完成")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: SoftBus服务启动失败")
            Result.failure(e)
        }
    }
    
    /**
     * 停止SoftBus服务
     */
    suspend fun stop(): Result<Unit> {
        if (!_isStarted.get()) {
            return Result.success(Unit)
        }
        
        return try {
            Timber.i("$TAG: 停止SoftBus服务...")
            
            _busState.value = SoftBusState.STOPPING
            
            // 1. 断开所有连接
            disconnectAllDevices()
            
            // 2. 停止各个子模块
            discoveryManager.stop()
            connectionManager.stop()
            transmissionManager.stop()
            
            // 3. 取消后台任务
            managerScope.cancel()
            
            _isStarted.set(false)
            _busState.value = SoftBusState.STOPPED
            
            Timber.i("$TAG: SoftBus服务停止完成")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: SoftBus服务停止失败")
            Result.failure(e)
        }
    }
    
    /**
     * 连接到指定设备
     */
    suspend fun connectToDevice(
        deviceId: String,
        connectionType: ConnectionType = ConnectionType.BR,
        timeout: Long = 30000L
    ): Result<String> {
        if (!_isStarted.get()) {
            return Result.failure(IllegalStateException("SoftBus服务未启动"))
        }
        
        val device = deviceCache[deviceId]
            ?: return Result.failure(IllegalArgumentException("设备不存在: $deviceId"))
        
        return try {
            Timber.i("$TAG: 连接设备 $deviceId, 类型: $connectionType")
            
            val sessionId = generateSessionId()
            val connectionInfo = connectionManager.connectToDevice(
                device, connectionType, sessionId, timeout
            )
            
            connectionCache[deviceId] = connectionInfo
            updateConnectedDevices()
            
            // 通知监听器
            notifyDeviceConnected(device, connectionInfo)
            
            Result.success(sessionId)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 连接设备失败: $deviceId")
            Result.failure(e)
        }
    }
    
    /**
     * 断开设备连接
     */
    suspend fun disconnectDevice(deviceId: String): Result<Unit> {
        val connectionInfo = connectionCache[deviceId]
            ?: return Result.failure(IllegalArgumentException("设备未连接: $deviceId"))
        
        return try {
            Timber.i("$TAG: 断开设备连接: $deviceId")
            
            connectionManager.disconnectDevice(connectionInfo.sessionId)
            connectionCache.remove(deviceId)
            updateConnectedDevices()
            
            // 通知监听器
            notifyDeviceDisconnected(deviceId)
            
            Result.success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 断开设备连接失败: $deviceId")
            Result.failure(e)
        }
    }
    
    /**
     * 发送数据到指定设备
     */
    suspend fun sendData(
        deviceId: String,
        data: ByteArray,
        dataType: DataType = DataType.BYTES,
        qos: QosRequirement = QosRequirement.DEFAULT
    ): Result<Unit> {
        val connectionInfo = connectionCache[deviceId]
            ?: return Result.failure(IllegalArgumentException("设备未连接: $deviceId"))
        
        return try {
            val packet = DataPacket(
                sessionId = connectionInfo.sessionId,
                data = data,
                dataType = dataType,
                qos = qos,
                timestamp = System.currentTimeMillis()
            )
            
            transmissionManager.sendData(packet)
            Result.success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 发送数据失败: $deviceId")
            Result.failure(e)
        }
    }
    
    /**
     * 广播数据到所有连接的设备
     */
    suspend fun broadcastData(
        data: ByteArray,
        dataType: DataType = DataType.BYTES,
        qos: QosRequirement = QosRequirement.DEFAULT
    ): Result<Int> {
        if (connectionCache.isEmpty()) {
            return Result.success(0)
        }
        
        return try {
            var successCount = 0
            
            connectionCache.values.forEach { connectionInfo ->
                val packet = DataPacket(
                    sessionId = connectionInfo.sessionId,
                    data = data,
                    dataType = dataType,
                    qos = qos,
                    timestamp = System.currentTimeMillis()
                )
                
                try {
                    transmissionManager.sendData(packet)
                    successCount++
                } catch (e: Exception) {
                    Timber.w(e, "$TAG: 广播到设备失败: ${connectionInfo.deviceId}")
                }
            }
            
            Result.success(successCount)
            
        } catch (e: Exception) {
            Timber.e(e, "$TAG: 广播数据失败")
            Result.failure(e)
        }
    }
    
    /**
     * 添加事件监听器
     */
    fun addEventListener(listener: SoftBusEventListener) {
        eventListeners.add(listener)
    }
    
    /**
     * 移除事件监听器
     */
    fun removeEventListener(listener: SoftBusEventListener) {
        eventListeners.remove(listener)
    }
    
    /**
     * 获取本地设备信息
     */
    fun getLocalDeviceInfo(): DeviceInfo = localDeviceInfo
    
    /**
     * 获取SoftBus统计信息
     */
    fun getStatistics(): SoftBusStatistics {
        return SoftBusStatistics(
            discoveredDeviceCount = deviceCache.size,
            connectedDeviceCount = connectionCache.size,
            totalDataSent = transmissionManager.getTotalDataSent(),
            totalDataReceived = transmissionManager.getTotalDataReceived(),
            uptime = System.currentTimeMillis() - (localDeviceInfo.createTime ?: 0L)
        )
    }
    
    // ==================== 私有方法 ====================
    
    private fun initializeLocalDevice() {
        localDeviceInfo = DeviceInfoBuilder(context)
            .setDeviceId(generateDeviceId())
            .setDeviceName(getDeviceName())
            .setDeviceType(getDeviceType())
            .setCapabilities(getDeviceCapabilities())
            .setNetworkCapabilities(getNetworkCapabilities())
            .build()
        
        Timber.i("$TAG: 本地设备信息: $localDeviceInfo")
    }
    
    private fun setupModuleCallbacks() {
        // 设备发现回调
        discoveryManager.setDeviceDiscoveryCallback { device ->
            managerScope.launch {
                handleDeviceDiscovered(device)
            }
        }
        
        // 连接状态回调
        connectionManager.setConnectionStateCallback { sessionId, state ->
            managerScope.launch {
                handleConnectionStateChanged(sessionId, state)
            }
        }
        
        // 数据接收回调
        transmissionManager.setDataReceiveCallback { packet ->
            managerScope.launch {
                handleDataReceived(packet)
            }
        }
    }
    
    private fun startBackgroundTasks() {
        // 心跳任务
        managerScope.launch {
            while (isActive && _isStarted.get()) {
                try {
                    sendHeartbeat()
                    delay(HEARTBEAT_INTERVAL_MS)
                } catch (e: Exception) {
                    Timber.e(e, "$TAG: 心跳任务异常")
                }
            }
        }
        
        // 连接维护任务
        managerScope.launch {
            while (isActive && _isStarted.get()) {
                try {
                    maintainConnections()
                    delay(10000L) // 每10秒检查一次
                } catch (e: Exception) {
                    Timber.e(e, "$TAG: 连接维护任务异常")
                }
            }
        }
    }
    
    private suspend fun startDeviceDiscovery() {
        discoveryManager.startDiscovery(
            discoveryType = DiscoveryType.ACTIVE,
            medium = DiscoveryMedium.AUTO,
            freq = DiscoveryFreq.HIGH
        )
    }
    
    private suspend fun handleDeviceDiscovered(device: DeviceInfo) {
        deviceCache[device.deviceId] = device
        updateDiscoveredDevices()
        
        // 通知监听器
        notifyDeviceDiscovered(device)
        
        Timber.d("$TAG: 发现设备: ${device.deviceName} (${device.deviceId})")
    }
    
    private suspend fun handleConnectionStateChanged(sessionId: String, state: ConnectionState) {
        val connectionInfo = connectionCache.values.find { it.sessionId == sessionId }
        if (connectionInfo != null) {
            connectionInfo.state = state
            updateConnectedDevices()
            
            when (state) {
                ConnectionState.CONNECTED -> {
                    notifyDeviceConnected(deviceCache[connectionInfo.deviceId]!!, connectionInfo)
                }
                ConnectionState.DISCONNECTED -> {
                    connectionCache.remove(connectionInfo.deviceId)
                    updateConnectedDevices()
                    notifyDeviceDisconnected(connectionInfo.deviceId)
                }
                else -> {
                    // 其他状态变化
                }
            }
        }
    }
    
    private suspend fun handleDataReceived(packet: DataPacket) {
        val connectionInfo = connectionCache.values.find { it.sessionId == packet.sessionId }
        if (connectionInfo != null) {
            notifyDataReceived(connectionInfo.deviceId, packet.data, packet.dataType)
        }
    }
    
    private fun updateDiscoveredDevices() {
        _discoveredDevices.value = deviceCache.toMap()
    }
    
    private fun updateConnectedDevices() {
        _connectedDevices.value = connectionCache.toMap()
    }
    
    private suspend fun disconnectAllDevices() {
        connectionCache.keys.toList().forEach { deviceId ->
            try {
                disconnectDevice(deviceId)
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 断开设备连接失败: $deviceId")
            }
        }
    }
    
    private suspend fun sendHeartbeat() {
        if (connectionCache.isNotEmpty()) {
            val heartbeatData = createHeartbeatData()
            broadcastData(heartbeatData, DataType.HEARTBEAT, QosRequirement.LOW_LATENCY)
        }
    }
    
    private suspend fun maintainConnections() {
        val currentTime = System.currentTimeMillis()
        val timeoutConnections = connectionCache.values.filter { 
            currentTime - it.lastActiveTime > 60000L // 60秒超时
        }
        
        timeoutConnections.forEach { connectionInfo ->
            Timber.w("$TAG: 连接超时，断开设备: ${connectionInfo.deviceId}")
            disconnectDevice(connectionInfo.deviceId)
        }
    }
    
    private fun generateSessionId(): String {
        return "${localDeviceInfo.deviceId}_${sessionCounter.incrementAndGet()}_${System.currentTimeMillis()}"
    }
    
    private fun generateDeviceId(): String {
        return DeviceIdGenerator.generateDeviceId(context)
    }
    
    private fun getDeviceName(): String {
        return "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}"
    }
    
    private fun getDeviceType(): DeviceType {
        return when {
            isTablet() -> DeviceType.TABLET
            isTV() -> DeviceType.TV
            isWearable() -> DeviceType.WEARABLE
            else -> DeviceType.PHONE
        }
    }
    
    private fun getDeviceCapabilities(): List<DeviceCapability> {
        val capabilities = mutableListOf<DeviceCapability>()
        
        if (context.packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_CAMERA)) {
            capabilities.add(DeviceCapability.CAMERA)
        }
        if (context.packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_MICROPHONE)) {
            capabilities.add(DeviceCapability.MICROPHONE)
        }
        if (context.packageManager.hasSystemFeature(android.content.pm.PackageManager.FEATURE_TOUCHSCREEN)) {
            capabilities.add(DeviceCapability.TOUCHSCREEN)
        }
        
        return capabilities
    }
    
    private fun getNetworkCapabilities(): List<NetworkCapability> {
        return listOf(
            NetworkCapability.WIFI,
            NetworkCapability.BLUETOOTH,
            NetworkCapability.CELLULAR
        )
    }
    
    private fun isTablet(): Boolean = false // 简化实现
    private fun isTV(): Boolean = false
    private fun isWearable(): Boolean = false
    
    private fun createHeartbeatData(): ByteArray {
        return "HEARTBEAT:${localDeviceInfo.deviceId}:${System.currentTimeMillis()}".toByteArray()
    }
    
    // 事件通知方法
    private fun notifyDeviceDiscovered(device: DeviceInfo) {
        eventListeners.forEach { listener ->
            try {
                listener.onDeviceDiscovered(device)
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 通知设备发现事件失败")
            }
        }
    }
    
    private fun notifyDeviceConnected(device: DeviceInfo, connectionInfo: ConnectionInfo) {
        eventListeners.forEach { listener ->
            try {
                listener.onDeviceConnected(device, connectionInfo)
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 通知设备连接事件失败")
            }
        }
    }
    
    private fun notifyDeviceDisconnected(deviceId: String) {
        eventListeners.forEach { listener ->
            try {
                listener.onDeviceDisconnected(deviceId)
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 通知设备断开事件失败")
            }
        }
    }
    
    private fun notifyDataReceived(deviceId: String, data: ByteArray, dataType: DataType) {
        eventListeners.forEach { listener ->
            try {
                listener.onDataReceived(deviceId, data, dataType)
            } catch (e: Exception) {
                Timber.e(e, "$TAG: 通知数据接收事件失败")
            }
        }
    }
}
