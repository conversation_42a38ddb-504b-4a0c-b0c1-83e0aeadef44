/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.softbus;

import android.annotation.NonNull;
import android.annotation.Nullable;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Binder;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.RemoteException;
import android.os.SystemClock;
import android.softbus.ISoftBusService;
import android.softbus.ISoftBusCallback;
import android.softbus.IPublishCallback;
import android.softbus.ITransmissionCallback;
import android.softbus.IDeviceStateCallback;
import android.softbus.SoftBusManager;
import android.softbus.SoftBusConstants;
import android.softbus.SoftBusStatistics;
import android.softbus.discovery.DiscoveryConfig;
import android.softbus.connection.ConnectionConfig;
import android.softbus.device.DeviceInfo;
import android.util.Log;
import android.util.SparseArray;

import com.android.internal.annotations.GuardedBy;
import com.android.server.SystemService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * SoftBus系统服务实现
 * 
 * 提供分布式软总线的系统级服务，包括设备发现、连接管理、
 * 数据传输和设备管理等核心功能。
 */
public class SoftBusService extends SystemService {
    
    private static final String TAG = "SoftBusService";
    private static final boolean DEBUG = Log.isLoggable(TAG, Log.DEBUG);
    
    /** 服务启动超时时间 */
    private static final long SERVICE_START_TIMEOUT_MS = 10000;
    
    /** Native库名称 */
    private static final String NATIVE_LIBRARY_NAME = "softbus_service";
    
    private final Context mContext;
    private final Object mLock = new Object();
    
    /** 服务状态 */
    @GuardedBy("mLock")
    private int mServiceState = SoftBusConstants.SERVICE_STATE_STOPPED;
    
    /** 工作线程 */
    private HandlerThread mHandlerThread;
    private Handler mHandler;
    
    /** Native接口 */
    private SoftBusNativeInterface mNativeInterface;
    
    /** 发现管理器 */
    private DiscoveryManager mDiscoveryManager;
    
    /** 连接管理器 */
    private ConnectionManager mConnectionManager;
    
    /** 传输管理器 */
    private TransmissionManager mTransmissionManager;
    
    /** 设备管理器 */
    private DeviceManager mDeviceManager;
    
    /** 安全管理器 */
    private SecurityManager mSecurityManager;
    
    /** 统计信息 */
    private final SoftBusStatistics mStatistics = new SoftBusStatistics();
    
    /** 回调管理 */
    private final Map<IBinder, ISoftBusCallback> mCallbacks = new ConcurrentHashMap<>();
    private final AtomicInteger mNextCallbackId = new AtomicInteger(1);
    
    /** 会话管理 */
    private final Map<String, SessionInfo> mSessions = new ConcurrentHashMap<>();
    private final AtomicInteger mNextSessionId = new AtomicInteger(1);
    
    static {
        try {
            System.loadLibrary(NATIVE_LIBRARY_NAME);
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native library: " + NATIVE_LIBRARY_NAME, e);
        }
    }
    
    public SoftBusService(Context context) {
        super(context);
        mContext = context;
    }
    
    @Override
    public void onStart() {
        Log.i(TAG, "Starting SoftBus service");
        
        // 创建工作线程
        mHandlerThread = new HandlerThread("SoftBusService");
        mHandlerThread.start();
        mHandler = new Handler(mHandlerThread.getLooper());
        
        // 初始化Native接口
        mNativeInterface = new SoftBusNativeInterface();
        
        // 初始化各个管理器
        mDiscoveryManager = new DiscoveryManager(mContext, mNativeInterface);
        mConnectionManager = new ConnectionManager(mContext, mNativeInterface);
        mTransmissionManager = new TransmissionManager(mContext, mNativeInterface);
        mDeviceManager = new DeviceManager(mContext, mNativeInterface);
        mSecurityManager = new SecurityManager(mContext, mNativeInterface);
        
        // 发布服务
        publishBinderService(Context.SOFTBUS_SERVICE, new SoftBusServiceImpl());
        
        // 启动服务
        mHandler.post(this::startServiceInternal);
        
        Log.i(TAG, "SoftBus service started");
    }
    
    @Override
    public void onBootPhase(int phase) {
        if (phase == SystemService.PHASE_SYSTEM_SERVICES_READY) {
            Log.i(TAG, "SoftBus service boot phase: SYSTEM_SERVICES_READY");
            // 系统服务就绪后的初始化
            mHandler.post(this::onSystemReady);
        }
    }
    
    private void startServiceInternal() {
        synchronized (mLock) {
            if (mServiceState != SoftBusConstants.SERVICE_STATE_STOPPED) {
                Log.w(TAG, "Service already started, state: " + mServiceState);
                return;
            }
            
            mServiceState = SoftBusConstants.SERVICE_STATE_STARTING;
        }
        
        try {
            // 初始化Native层
            if (!mNativeInterface.initialize()) {
                throw new RuntimeException("Failed to initialize native interface");
            }
            
            // 启动各个管理器
            mDiscoveryManager.start();
            mConnectionManager.start();
            mTransmissionManager.start();
            mDeviceManager.start();
            mSecurityManager.start();
            
            synchronized (mLock) {
                mServiceState = SoftBusConstants.SERVICE_STATE_RUNNING;
            }
            
            Log.i(TAG, "SoftBus service started successfully");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to start SoftBus service", e);
            synchronized (mLock) {
                mServiceState = SoftBusConstants.SERVICE_STATE_ERROR;
            }
        }
    }
    
    private void onSystemReady() {
        Log.i(TAG, "System ready, performing additional initialization");
        
        // 系统就绪后的额外初始化
        mDeviceManager.onSystemReady();
        mDiscoveryManager.onSystemReady();
    }
    
    @Override
    public void onUserStarting(@NonNull TargetUser user) {
        Log.i(TAG, "User starting: " + user.getUserIdentifier());
        // 用户启动时的处理
    }
    
    @Override
    public void onUserStopping(@NonNull TargetUser user) {
        Log.i(TAG, "User stopping: " + user.getUserIdentifier());
        // 用户停止时的处理
    }
    
    /**
     * SoftBus服务Binder实现
     */
    private final class SoftBusServiceImpl extends ISoftBusService.Stub {
        
        @Override
        public int startDiscovery(DiscoveryConfig config, ISoftBusCallback callback) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_DISCOVERY);
            
            if (config == null || callback == null) {
                throw new IllegalArgumentException("Config and callback cannot be null");
            }
            
            synchronized (mLock) {
                if (mServiceState != SoftBusConstants.SERVICE_STATE_RUNNING) {
                    throw new IllegalStateException("Service not running");
                }
            }
            
            try {
                // 注册回调
                IBinder binder = callback.asBinder();
                mCallbacks.put(binder, callback);
                
                // 启动发现
                int discoveryId = mDiscoveryManager.startDiscovery(config, callback);
                
                // 更新统计信息
                mStatistics.incrementDiscoveryCount();
                
                Log.i(TAG, "Discovery started: " + discoveryId);
                return discoveryId;
                
            } catch (Exception e) {
                Log.e(TAG, "Failed to start discovery", e);
                throw new RuntimeException("Failed to start discovery", e);
            }
        }
        
        @Override
        public void stopDiscovery(int discoveryId) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_DISCOVERY);
            
            try {
                mDiscoveryManager.stopDiscovery(discoveryId);
                Log.i(TAG, "Discovery stopped: " + discoveryId);
            } catch (Exception e) {
                Log.e(TAG, "Failed to stop discovery", e);
                throw new RuntimeException("Failed to stop discovery", e);
            }
        }
        
        @Override
        public int publishService(String capability, IPublishCallback callback) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_DISCOVERY);
            
            if (capability == null || callback == null) {
                throw new IllegalArgumentException("Capability and callback cannot be null");
            }
            
            try {
                int publishId = mDiscoveryManager.publishService(capability, callback);
                Log.i(TAG, "Service published: " + publishId);
                return publishId;
            } catch (Exception e) {
                Log.e(TAG, "Failed to publish service", e);
                throw new RuntimeException("Failed to publish service", e);
            }
        }
        
        @Override
        public void stopPublishService(int publishId) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_DISCOVERY);
            
            try {
                mDiscoveryManager.stopPublishService(publishId);
                Log.i(TAG, "Service unpublished: " + publishId);
            } catch (Exception e) {
                Log.e(TAG, "Failed to stop publish service", e);
                throw new RuntimeException("Failed to stop publish service", e);
            }
        }
        
        @Override
        public String connectDevice(String deviceId, ConnectionConfig config, ISoftBusCallback callback) {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            if (deviceId == null || config == null || callback == null) {
                throw new IllegalArgumentException("Parameters cannot be null");
            }
            
            try {
                // 注册回调
                IBinder binder = callback.asBinder();
                mCallbacks.put(binder, callback);
                
                // 建立连接
                String sessionId = mConnectionManager.connectDevice(deviceId, config, callback);
                
                // 创建会话信息
                SessionInfo sessionInfo = new SessionInfo(sessionId, deviceId, 
                    SystemClock.elapsedRealtime());
                mSessions.put(sessionId, sessionInfo);
                
                // 更新统计信息
                mStatistics.incrementConnectionCount();
                
                Log.i(TAG, "Device connection initiated: " + deviceId + " -> " + sessionId);
                return sessionId;
                
            } catch (Exception e) {
                Log.e(TAG, "Failed to connect device", e);
                throw new RuntimeException("Failed to connect device", e);
            }
        }
        
        @Override
        public void disconnectDevice(String sessionId) {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            if (sessionId == null) {
                throw new IllegalArgumentException("Session ID cannot be null");
            }
            
            try {
                mConnectionManager.disconnectDevice(sessionId);
                mSessions.remove(sessionId);
                
                Log.i(TAG, "Device disconnected: " + sessionId);
            } catch (Exception e) {
                Log.e(TAG, "Failed to disconnect device", e);
                throw new RuntimeException("Failed to disconnect device", e);
            }
        }
        
        @Override
        public int createSessionServer(String sessionName, ISoftBusCallback callback) {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            if (sessionName == null || callback == null) {
                throw new IllegalArgumentException("Parameters cannot be null");
            }
            
            try {
                int serverId = mConnectionManager.createSessionServer(sessionName, callback);
                Log.i(TAG, "Session server created: " + sessionName + " -> " + serverId);
                return serverId;
            } catch (Exception e) {
                Log.e(TAG, "Failed to create session server", e);
                throw new RuntimeException("Failed to create session server", e);
            }
        }
        
        @Override
        public void removeSessionServer(int serverId) {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            try {
                mConnectionManager.removeSessionServer(serverId);
                Log.i(TAG, "Session server removed: " + serverId);
            } catch (Exception e) {
                Log.e(TAG, "Failed to remove session server", e);
                throw new RuntimeException("Failed to remove session server", e);
            }
        }
        
        @Override
        public void sendBytes(String sessionId, byte[] data) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_TRANSMISSION);
            
            if (sessionId == null || data == null) {
                throw new IllegalArgumentException("Parameters cannot be null");
            }
            
            try {
                mTransmissionManager.sendBytes(sessionId, data);
                
                // 更新统计信息
                mStatistics.addBytesSent(data.length);
                
                if (DEBUG) {
                    Log.d(TAG, "Bytes sent: " + sessionId + ", length: " + data.length);
                }
            } catch (Exception e) {
                Log.e(TAG, "Failed to send bytes", e);
                throw new RuntimeException("Failed to send bytes", e);
            }
        }
        
        @Override
        public void sendMessage(String sessionId, String message) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_TRANSMISSION);
            
            if (sessionId == null || message == null) {
                throw new IllegalArgumentException("Parameters cannot be null");
            }
            
            try {
                mTransmissionManager.sendMessage(sessionId, message);
                
                // 更新统计信息
                mStatistics.addBytesSent(message.getBytes().length);
                
                if (DEBUG) {
                    Log.d(TAG, "Message sent: " + sessionId + ", length: " + message.length());
                }
            } catch (Exception e) {
                Log.e(TAG, "Failed to send message", e);
                throw new RuntimeException("Failed to send message", e);
            }
        }
        
        @Override
        public void sendFile(String sessionId, String filePath, ITransmissionCallback callback) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_TRANSMISSION);
            
            if (sessionId == null || filePath == null || callback == null) {
                throw new IllegalArgumentException("Parameters cannot be null");
            }
            
            try {
                mTransmissionManager.sendFile(sessionId, filePath, callback);
                Log.i(TAG, "File transmission started: " + sessionId + " -> " + filePath);
            } catch (Exception e) {
                Log.e(TAG, "Failed to send file", e);
                throw new RuntimeException("Failed to send file", e);
            }
        }
        
        @Override
        public void sendStream(String sessionId, byte[] streamData, ITransmissionCallback callback) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_TRANSMISSION);
            
            if (sessionId == null || streamData == null || callback == null) {
                throw new IllegalArgumentException("Parameters cannot be null");
            }
            
            try {
                mTransmissionManager.sendStream(sessionId, streamData, callback);
                
                // 更新统计信息
                mStatistics.addBytesSent(streamData.length);
                
                if (DEBUG) {
                    Log.d(TAG, "Stream sent: " + sessionId + ", length: " + streamData.length);
                }
            } catch (Exception e) {
                Log.e(TAG, "Failed to send stream", e);
                throw new RuntimeException("Failed to send stream", e);
            }
        }
        
        @Override
        public void registerDataReceiver(String sessionId, ISoftBusCallback callback) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_TRANSMISSION);
            
            if (sessionId == null || callback == null) {
                throw new IllegalArgumentException("Parameters cannot be null");
            }
            
            try {
                IBinder binder = callback.asBinder();
                mCallbacks.put(binder, callback);
                
                mTransmissionManager.registerDataReceiver(sessionId, callback);
                Log.i(TAG, "Data receiver registered: " + sessionId);
            } catch (Exception e) {
                Log.e(TAG, "Failed to register data receiver", e);
                throw new RuntimeException("Failed to register data receiver", e);
            }
        }
        
        @Override
        public void unregisterDataReceiver(String sessionId) {
            enforcePermission(SoftBusManager.PERMISSION_SOFTBUS_TRANSMISSION);
            
            if (sessionId == null) {
                throw new IllegalArgumentException("Session ID cannot be null");
            }
            
            try {
                mTransmissionManager.unregisterDataReceiver(sessionId);
                Log.i(TAG, "Data receiver unregistered: " + sessionId);
            } catch (Exception e) {
                Log.e(TAG, "Failed to unregister data receiver", e);
                throw new RuntimeException("Failed to unregister data receiver", e);
            }
        }
        
        @Override
        public List<DeviceInfo> getConnectedDevices() {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            try {
                return mDeviceManager.getConnectedDevices();
            } catch (Exception e) {
                Log.e(TAG, "Failed to get connected devices", e);
                return new ArrayList<>();
            }
        }
        
        @Override
        public DeviceInfo getLocalDeviceInfo() {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            try {
                return mDeviceManager.getLocalDeviceInfo();
            } catch (Exception e) {
                Log.e(TAG, "Failed to get local device info", e);
                return null;
            }
        }
        
        @Override
        public DeviceInfo getDeviceInfo(String deviceId) {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            if (deviceId == null) {
                throw new IllegalArgumentException("Device ID cannot be null");
            }
            
            try {
                return mDeviceManager.getDeviceInfo(deviceId);
            } catch (Exception e) {
                Log.e(TAG, "Failed to get device info", e);
                return null;
            }
        }
        
        @Override
        public void registerDeviceStateCallback(IDeviceStateCallback callback) {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            if (callback == null) {
                throw new IllegalArgumentException("Callback cannot be null");
            }
            
            try {
                mDeviceManager.registerDeviceStateCallback(callback);
                Log.i(TAG, "Device state callback registered");
            } catch (Exception e) {
                Log.e(TAG, "Failed to register device state callback", e);
                throw new RuntimeException("Failed to register device state callback", e);
            }
        }
        
        @Override
        public void unregisterDeviceStateCallback() {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            try {
                mDeviceManager.unregisterDeviceStateCallback();
                Log.i(TAG, "Device state callback unregistered");
            } catch (Exception e) {
                Log.e(TAG, "Failed to unregister device state callback", e);
                throw new RuntimeException("Failed to unregister device state callback", e);
            }
        }
        
        // 其他接口实现...
        
        @Override
        public int getServiceState() {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            synchronized (mLock) {
                return mServiceState;
            }
        }
        
        @Override
        public SoftBusStatistics getStatistics() {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            return new SoftBusStatistics(mStatistics);
        }
        
        @Override
        public void resetStatistics() {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            mStatistics.reset();
            Log.i(TAG, "Statistics reset");
        }
        
        @Override
        public String dumpDebugInfo() {
            enforcePermission(SoftBusManager.PERMISSION_ACCESS_SOFTBUS);
            
            StringBuilder sb = new StringBuilder();
            sb.append("SoftBus Service Debug Info:\n");
            sb.append("Service State: ").append(mServiceState).append("\n");
            sb.append("Active Sessions: ").append(mSessions.size()).append("\n");
            sb.append("Active Callbacks: ").append(mCallbacks.size()).append("\n");
            sb.append("Statistics: ").append(mStatistics.toString()).append("\n");
            
            return sb.toString();
        }
        
        // 其他接口的简化实现...
        @Override public void joinLNN(String networkId, ISoftBusCallback callback) { }
        @Override public void leaveLNN(String networkId, ISoftBusCallback callback) { }
        @Override public List<String> getNetworkList() { return new ArrayList<>(); }
        @Override public void setDeviceAuthInfo(String deviceId, byte[] authInfo) { }
        @Override public int getDeviceAuthState(String deviceId) { return 0; }
        @Override public void clearDeviceAuthInfo(String deviceId) { }
        @Override public void setServiceConfig(String key, String value) { }
        @Override public String getServiceConfig(String key) { return null; }
        @Override public void setQosPolicy(String sessionId, int qosType, int qosValue) { }
        @Override public int getQosState(String sessionId) { return 0; }
        @Override public void setPowerMode(int mode) { }
        @Override public int getPowerMode() { return 0; }
    }
    
    private void enforcePermission(String permission) {
        mContext.enforceCallingOrSelfPermission(permission, 
            "Permission required: " + permission);
    }
    
    /**
     * 会话信息
     */
    private static class SessionInfo {
        final String sessionId;
        final String deviceId;
        final long createTime;
        
        SessionInfo(String sessionId, String deviceId, long createTime) {
            this.sessionId = sessionId;
            this.deviceId = deviceId;
            this.createTime = createTime;
        }
    }
}
