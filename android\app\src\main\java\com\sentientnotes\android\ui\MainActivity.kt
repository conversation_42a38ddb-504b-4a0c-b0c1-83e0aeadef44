package com.sentientnotes.android.ui

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.IBinder
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import com.sentientnotes.android.services.SoftBusService
import com.sentientnotes.android.ui.theme.SentientNotesTheme
import com.sentientnotes.android.ui.ar.ARActivity
import com.sentientnotes.android.ui.camera.CameraActivity
import com.sentientnotes.android.ui.notes.NotesListScreen
import com.sentientnotes.android.viewmodels.MainViewModel

/**
 * 主Activity
 * 应用的入口点，提供主要功能的导航和SoftBus服务管理
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private val viewModel: MainViewModel by viewModels()
    
    // SoftBus服务连接
    private var softBusService: SoftBusService? = null
    private var isBound = false
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as SoftBusService.SoftBusBinder
            softBusService = binder.getService()
            isBound = true
            
            // 连接到服务器
            softBusService?.connect()
            
            // 设置消息监听器
            softBusService?.addMessageListener(object : SoftBusService.MessageListener {
                override fun onMessageReceived(message: String) {
                    viewModel.handleSoftBusMessage(message)
                }
                
                override fun onDeviceDiscovered(device: SoftBusService.DeviceInfo) {
                    viewModel.onDeviceDiscovered(device)
                }
                
                override fun onDeviceLost(deviceId: String) {
                    viewModel.onDeviceLost(deviceId)
                }
                
                override fun onConnectionStateChanged(state: Int) {
                    viewModel.onConnectionStateChanged(state)
                }
            })
            
            Timber.i("SoftBus服务已连接")
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            softBusService = null
            isBound = false
            Timber.i("SoftBus服务已断开")
        }
    }

    // 权限请求
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            Timber.i("所有权限已授予")
            startSoftBusService()
        } else {
            Timber.w("部分权限被拒绝")
            // 可以显示权限说明对话框
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Timber.i("MainActivity创建")
        
        // 检查和请求权限
        checkAndRequestPermissions()
        
        setContent {
            SentientNotesTheme {
                MainScreen(
                    viewModel = viewModel,
                    onNavigateToAR = { startARActivity() },
                    onNavigateToCamera = { startCameraActivity() },
                    onSendTestMessage = { sendTestMessage() }
                )
            }
        }
    }

    override fun onStart() {
        super.onStart()
        // 绑定SoftBus服务
        if (!isBound) {
            val intent = Intent(this, SoftBusService::class.java)
            bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
        }
    }

    override fun onStop() {
        super.onStop()
        // 解绑服务
        if (isBound) {
            unbindService(serviceConnection)
            isBound = false
        }
    }

    /**
     * 检查和请求权限
     */
    private fun checkAndRequestPermissions() {
        val requiredPermissions = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_SCAN
        )

        val missingPermissions = requiredPermissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (missingPermissions.isNotEmpty()) {
            permissionLauncher.launch(missingPermissions.toTypedArray())
        } else {
            startSoftBusService()
        }
    }

    /**
     * 启动SoftBus服务
     */
    private fun startSoftBusService() {
        val intent = Intent(this, SoftBusService::class.java)
        startForegroundService(intent)
    }

    /**
     * 启动AR Activity
     */
    private fun startARActivity() {
        val intent = Intent(this, ARActivity::class.java)
        startActivity(intent)
    }

    /**
     * 启动相机Activity
     */
    private fun startCameraActivity() {
        val intent = Intent(this, CameraActivity::class.java)
        startActivity(intent)
    }

    /**
     * 发送测试消息
     */
    private fun sendTestMessage() {
        softBusService?.let { service ->
            val testMessage = """
                {
                    "type": "test_message",
                    "timestamp": ${System.currentTimeMillis()},
                    "data": {
                        "message": "Hello from Android client",
                        "device_id": "${viewModel.deviceId}"
                    }
                }
            """.trimIndent()
            
            val success = service.sendMessage(testMessage)
            if (success) {
                Timber.i("测试消息发送成功")
            } else {
                Timber.e("测试消息发送失败")
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    onNavigateToAR: () -> Unit,
    onNavigateToCamera: () -> Unit,
    onSendTestMessage: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("灵境笔记") },
                actions = {
                    // 连接状态指示器
                    ConnectionStatusIndicator(
                        connectionState = uiState.connectionState
                    )
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = onNavigateToAR
            ) {
                Icon(Icons.Default.CameraAlt, contentDescription = "启动AR")
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 功能按钮区域
            FunctionButtons(
                onNavigateToAR = onNavigateToAR,
                onNavigateToCamera = onNavigateToCamera,
                onSendTestMessage = onSendTestMessage
            )
            
            // 设备列表
            DeviceList(
                devices = uiState.discoveredDevices
            )
            
            // 笔记列表
            NotesListScreen(
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
fun ConnectionStatusIndicator(
    connectionState: Int
) {
    val (color, icon, text) = when (connectionState) {
        SoftBusService.STATE_CONNECTED -> Triple(
            MaterialTheme.colorScheme.primary,
            Icons.Default.CloudDone,
            "已连接"
        )
        SoftBusService.STATE_CONNECTING -> Triple(
            MaterialTheme.colorScheme.secondary,
            Icons.Default.CloudSync,
            "连接中"
        )
        SoftBusService.STATE_ERROR -> Triple(
            MaterialTheme.colorScheme.error,
            Icons.Default.CloudOff,
            "连接错误"
        )
        else -> Triple(
            MaterialTheme.colorScheme.outline,
            Icons.Default.CloudOff,
            "未连接"
        )
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = text,
            tint = color,
            modifier = Modifier.size(16.dp)
        )
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = color
        )
    }
}

@Composable
fun FunctionButtons(
    onNavigateToAR: () -> Unit,
    onNavigateToCamera: () -> Unit,
    onSendTestMessage: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "功能",
                style = MaterialTheme.typography.titleMedium
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onNavigateToAR,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.CameraAlt, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("AR模式")
                }
                
                Button(
                    onClick = onNavigateToCamera,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.PhotoCamera, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("拍照")
                }
            }
            
            OutlinedButton(
                onClick = onSendTestMessage,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(Icons.Default.Send, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("发送测试消息")
            }
        }
    }
}

@Composable
fun DeviceList(
    devices: Map<String, SoftBusService.DeviceInfo>
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "发现的设备 (${devices.size})",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            if (devices.isEmpty()) {
                Text(
                    text = "暂无发现的设备",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(4.dp),
                    modifier = Modifier.heightIn(max = 200.dp)
                ) {
                    items(devices.values.toList()) { device ->
                        DeviceItem(device = device)
                    }
                }
            }
        }
    }
}

@Composable
fun DeviceItem(
    device: SoftBusService.DeviceInfo
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = when (device.deviceType) {
                    "smartphone" -> Icons.Default.PhoneAndroid
                    "tablet" -> Icons.Default.Tablet
                    else -> Icons.Default.Devices
                },
                contentDescription = device.deviceType,
                tint = if (device.isOnline) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.outline
                }
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = device.deviceName,
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = device.capabilities.joinToString(", "),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            if (device.isOnline) {
                Icon(
                    imageVector = Icons.Default.Circle,
                    contentDescription = "在线",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(8.dp)
                )
            }
        }
    }
}
