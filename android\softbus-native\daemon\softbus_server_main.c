/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * SoftBus Server Main - SoftBus守护进程主入口
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <errno.h>
#include <pthread.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <fcntl.h>

#include "softbus_server.h"
#include "softbus_common.h"
#include "softbus_bus_center.h"
#include "session.h"

#define SOFTBUS_SERVER_NAME "softbus_server"
#define SOFTBUS_SOCKET_PATH "/data/local/tmp/softbus_socket"
#define SOFTBUS_PID_FILE "/data/local/tmp/softbus.pid"
#define SOFTBUS_LOG_FILE "/data/local/tmp/softbus.log"

static bool g_serverRunning = false;
static pthread_t g_mainThread;
static int32_t g_serverSocket = -1;

/* 信号处理函数 */
static void SignalHandler(int sig) {
    SOFTBUS_LOG_INFO("Received signal %d, shutting down server", sig);
    g_serverRunning = false;
}

/* 创建PID文件 */
static int32_t CreatePidFile(void) {
    FILE *pidFile = fopen(SOFTBUS_PID_FILE, "w");
    if (pidFile == NULL) {
        SOFTBUS_LOG_ERROR("Failed to create PID file: %s", strerror(errno));
        return SOFTBUS_ERR;
    }
    
    fprintf(pidFile, "%d\n", getpid());
    fclose(pidFile);
    
    SOFTBUS_LOG_INFO("Created PID file: %s", SOFTBUS_PID_FILE);
    return SOFTBUS_OK;
}

/* 删除PID文件 */
static void RemovePidFile(void) {
    if (unlink(SOFTBUS_PID_FILE) != 0) {
        SOFTBUS_LOG_WARN("Failed to remove PID file: %s", strerror(errno));
    }
}

/* 检查是否已有实例运行 */
static bool IsServerRunning(void) {
    FILE *pidFile = fopen(SOFTBUS_PID_FILE, "r");
    if (pidFile == NULL) {
        return false;
    }
    
    int32_t pid;
    if (fscanf(pidFile, "%d", &pid) != 1) {
        fclose(pidFile);
        return false;
    }
    fclose(pidFile);
    
    /* 检查进程是否存在 */
    if (kill(pid, 0) == 0) {
        SOFTBUS_LOG_ERROR("SoftBus server is already running (PID: %d)", pid);
        return true;
    }
    
    /* 进程不存在，删除过期的PID文件 */
    RemovePidFile();
    return false;
}

/* 创建Unix域套接字服务器 */
static int32_t CreateServerSocket(void) {
    int32_t sockfd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (sockfd < 0) {
        SOFTBUS_LOG_ERROR("Failed to create socket: %s", strerror(errno));
        return -1;
    }
    
    /* 删除可能存在的旧套接字文件 */
    unlink(SOFTBUS_SOCKET_PATH);
    
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, SOFTBUS_SOCKET_PATH, sizeof(addr.sun_path) - 1);
    
    if (bind(sockfd, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        SOFTBUS_LOG_ERROR("Failed to bind socket: %s", strerror(errno));
        close(sockfd);
        return -1;
    }
    
    if (listen(sockfd, 10) < 0) {
        SOFTBUS_LOG_ERROR("Failed to listen on socket: %s", strerror(errno));
        close(sockfd);
        unlink(SOFTBUS_SOCKET_PATH);
        return -1;
    }
    
    /* 设置套接字权限 */
    chmod(SOFTBUS_SOCKET_PATH, 0666);
    
    SOFTBUS_LOG_INFO("Server socket created: %s", SOFTBUS_SOCKET_PATH);
    return sockfd;
}

/* 处理客户端连接 */
static void* HandleClientConnection(void* arg) {
    int32_t clientSocket = *(int32_t*)arg;
    free(arg);
    
    SOFTBUS_LOG_INFO("New client connected: socket %d", clientSocket);
    
    char buffer[4096];
    ssize_t bytesRead;
    
    while (g_serverRunning) {
        bytesRead = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);
        if (bytesRead <= 0) {
            if (bytesRead < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
                SOFTBUS_LOG_ERROR("Failed to receive data: %s", strerror(errno));
            }
            break;
        }
        
        buffer[bytesRead] = '\0';
        SOFTBUS_LOG_DEBUG("Received data from client: %s", buffer);
        
        /* 处理客户端请求 */
        int32_t result = ProcessClientRequest(clientSocket, buffer, bytesRead);
        if (result != SOFTBUS_OK) {
            SOFTBUS_LOG_ERROR("Failed to process client request");
            break;
        }
    }
    
    close(clientSocket);
    SOFTBUS_LOG_INFO("Client disconnected: socket %d", clientSocket);
    return NULL;
}

/* 服务器主循环 */
static void* ServerMainLoop(void* arg) {
    (void)arg;
    
    g_serverSocket = CreateServerSocket();
    if (g_serverSocket < 0) {
        SOFTBUS_LOG_ERROR("Failed to create server socket");
        return NULL;
    }
    
    SOFTBUS_LOG_INFO("SoftBus server started, waiting for connections...");
    
    while (g_serverRunning) {
        struct sockaddr_un clientAddr;
        socklen_t clientAddrLen = sizeof(clientAddr);
        
        int32_t clientSocket = accept(g_serverSocket, (struct sockaddr*)&clientAddr, &clientAddrLen);
        if (clientSocket < 0) {
            if (errno == EINTR) {
                continue; /* 被信号中断，继续 */
            }
            SOFTBUS_LOG_ERROR("Failed to accept connection: %s", strerror(errno));
            break;
        }
        
        /* 为每个客户端创建处理线程 */
        pthread_t clientThread;
        int32_t* socketPtr = malloc(sizeof(int32_t));
        if (socketPtr == NULL) {
            SOFTBUS_LOG_ERROR("Failed to allocate memory for client socket");
            close(clientSocket);
            continue;
        }
        
        *socketPtr = clientSocket;
        if (pthread_create(&clientThread, NULL, HandleClientConnection, socketPtr) != 0) {
            SOFTBUS_LOG_ERROR("Failed to create client thread: %s", strerror(errno));
            close(clientSocket);
            free(socketPtr);
            continue;
        }
        
        /* 分离线程，让其自动清理 */
        pthread_detach(clientThread);
    }
    
    return NULL;
}

/* 初始化SoftBus服务器 */
static int32_t InitSoftBusServer(void) {
    SOFTBUS_LOG_INFO("Initializing SoftBus server...");
    
    /* 初始化Bus Center */
    if (LnnInitBusCenter() != SOFTBUS_OK) {
        SOFTBUS_LOG_ERROR("Failed to initialize Bus Center");
        return SOFTBUS_ERR;
    }
    
    /* 初始化传输层 */
    if (InitTransport() != SOFTBUS_OK) {
        SOFTBUS_LOG_ERROR("Failed to initialize Transport");
        LnnDeinitBusCenter();
        return SOFTBUS_ERR;
    }
    
    /* 初始化认证模块 */
    if (InitAuth() != SOFTBUS_OK) {
        SOFTBUS_LOG_ERROR("Failed to initialize Auth");
        DeinitTransport();
        LnnDeinitBusCenter();
        return SOFTBUS_ERR;
    }
    
    /* 初始化连接管理 */
    if (InitConnection() != SOFTBUS_OK) {
        SOFTBUS_LOG_ERROR("Failed to initialize Connection");
        DeinitAuth();
        DeinitTransport();
        LnnDeinitBusCenter();
        return SOFTBUS_ERR;
    }
    
    SOFTBUS_LOG_INFO("SoftBus server initialized successfully");
    return SOFTBUS_OK;
}

/* 反初始化SoftBus服务器 */
static void DeinitSoftBusServer(void) {
    SOFTBUS_LOG_INFO("Deinitializing SoftBus server...");
    
    DeinitConnection();
    DeinitAuth();
    DeinitTransport();
    LnnDeinitBusCenter();
    
    SOFTBUS_LOG_INFO("SoftBus server deinitialized");
}

/* 启动守护进程 */
static int32_t StartDaemon(void) {
    pid_t pid = fork();
    if (pid < 0) {
        SOFTBUS_LOG_ERROR("Failed to fork daemon process: %s", strerror(errno));
        return SOFTBUS_ERR;
    }
    
    if (pid > 0) {
        /* 父进程退出 */
        exit(0);
    }
    
    /* 子进程继续 */
    if (setsid() < 0) {
        SOFTBUS_LOG_ERROR("Failed to create new session: %s", strerror(errno));
        return SOFTBUS_ERR;
    }
    
    /* 改变工作目录 */
    if (chdir("/") < 0) {
        SOFTBUS_LOG_ERROR("Failed to change directory: %s", strerror(errno));
        return SOFTBUS_ERR;
    }
    
    /* 重定向标准输入输出 */
    freopen("/dev/null", "r", stdin);
    freopen(SOFTBUS_LOG_FILE, "w", stdout);
    freopen(SOFTBUS_LOG_FILE, "w", stderr);
    
    return SOFTBUS_OK;
}

/* 显示帮助信息 */
static void ShowUsage(const char* progName) {
    printf("Usage: %s [options]\n", progName);
    printf("Options:\n");
    printf("  -d, --daemon    Run as daemon\n");
    printf("  -h, --help      Show this help message\n");
    printf("  -v, --version   Show version information\n");
    printf("  -s, --stop      Stop running server\n");
}

/* 停止运行的服务器 */
static int32_t StopServer(void) {
    FILE *pidFile = fopen(SOFTBUS_PID_FILE, "r");
    if (pidFile == NULL) {
        printf("SoftBus server is not running\n");
        return SOFTBUS_OK;
    }
    
    int32_t pid;
    if (fscanf(pidFile, "%d", &pid) != 1) {
        fclose(pidFile);
        printf("Invalid PID file\n");
        return SOFTBUS_ERR;
    }
    fclose(pidFile);
    
    if (kill(pid, SIGTERM) != 0) {
        printf("Failed to stop server (PID: %d): %s\n", pid, strerror(errno));
        return SOFTBUS_ERR;
    }
    
    printf("SoftBus server stopped (PID: %d)\n", pid);
    return SOFTBUS_OK;
}

/* 主函数 */
int main(int argc, char* argv[]) {
    bool daemonMode = false;
    
    /* 解析命令行参数 */
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-d") == 0 || strcmp(argv[i], "--daemon") == 0) {
            daemonMode = true;
        } else if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            ShowUsage(argv[0]);
            return 0;
        } else if (strcmp(argv[i], "-v") == 0 || strcmp(argv[i], "--version") == 0) {
            printf("SoftBus Server Version 1.0.0\n");
            return 0;
        } else if (strcmp(argv[i], "-s") == 0 || strcmp(argv[i], "--stop") == 0) {
            return StopServer();
        } else {
            printf("Unknown option: %s\n", argv[i]);
            ShowUsage(argv[0]);
            return 1;
        }
    }
    
    /* 检查是否已有实例运行 */
    if (IsServerRunning()) {
        return 1;
    }
    
    /* 启动守护进程 */
    if (daemonMode && StartDaemon() != SOFTBUS_OK) {
        return 1;
    }
    
    /* 创建PID文件 */
    if (CreatePidFile() != SOFTBUS_OK) {
        return 1;
    }
    
    /* 设置信号处理 */
    signal(SIGTERM, SignalHandler);
    signal(SIGINT, SignalHandler);
    signal(SIGPIPE, SIG_IGN);
    
    /* 初始化SoftBus服务器 */
    if (InitSoftBusServer() != SOFTBUS_OK) {
        RemovePidFile();
        return 1;
    }
    
    g_serverRunning = true;
    
    /* 启动服务器主循环 */
    if (pthread_create(&g_mainThread, NULL, ServerMainLoop, NULL) != 0) {
        SOFTBUS_LOG_ERROR("Failed to create main thread: %s", strerror(errno));
        DeinitSoftBusServer();
        RemovePidFile();
        return 1;
    }
    
    /* 等待服务器线程结束 */
    pthread_join(g_mainThread, NULL);
    
    /* 清理资源 */
    if (g_serverSocket >= 0) {
        close(g_serverSocket);
        unlink(SOFTBUS_SOCKET_PATH);
    }
    
    DeinitSoftBusServer();
    RemovePidFile();
    
    SOFTBUS_LOG_INFO("SoftBus server shutdown complete");
    return 0;
}
