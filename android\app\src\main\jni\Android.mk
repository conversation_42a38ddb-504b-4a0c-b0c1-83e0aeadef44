# Copyright (c) 2024 SentientNotes Project
# Licensed under the Apache License, Version 2.0 (the "License");
# 
# SoftBus Native Android.mk Build Configuration
# 基于OpenHarmony SoftBus架构移植到Android用户态

LOCAL_PATH := $(call my-dir)

# 清理变量
include $(CLEAR_VARS)

# 设置模块名称
LOCAL_MODULE := softbus_client

# 设置源文件
LOCAL_SRC_FILES := \
    ../../../softbus-native/jni/softbus_jni.c \
    ../../../softbus-native/sdk/softbus_client.c \
    ../../../softbus-native/sdk/softbus_client_stub.c \
    ../../../softbus-native/core/common/softbus_utils.c \
    ../../../softbus-native/core/common/softbus_log.c \
    ../../../softbus-native/core/common/softbus_thread.c \
    ../../../softbus-native/core/common/softbus_queue.c \
    ../../../softbus-native/core/discovery/disc_manager.c \
    ../../../softbus-native/core/discovery/disc_wifi.c \
    ../../../softbus-native/core/discovery/disc_ble.c \
    ../../../softbus-native/core/discovery/disc_br.c \
    ../../../softbus-native/core/connection/conn_manager.c \
    ../../../softbus-native/core/connection/conn_wifi.c \
    ../../../softbus-native/core/connection/conn_ble.c \
    ../../../softbus-native/core/connection/conn_br.c \
    ../../../softbus-native/core/transmission/trans_session.c \
    ../../../softbus-native/core/transmission/trans_channel.c \
    ../../../softbus-native/core/transmission/trans_stream.c \
    ../../../softbus-native/core/transmission/trans_file.c \
    ../../../softbus-native/core/authentication/auth_manager.c \
    ../../../softbus-native/core/authentication/auth_crypto.c \
    ../../../softbus-native/core/authentication/auth_device.c \
    ../../../softbus-native/core/bus_center/lnn_bus_center.c \
    ../../../softbus-native/core/bus_center/lnn_node_manager.c \
    ../../../softbus-native/core/bus_center/lnn_time_sync.c \
    ../../../softbus-native/core/bus_center/lnn_meta_node.c

# 设置包含目录
LOCAL_C_INCLUDES := \
    $(LOCAL_PATH)/../../../softbus-native/include \
    $(LOCAL_PATH)/../../../softbus-native/core/common \
    $(LOCAL_PATH)/../../../softbus-native/core/discovery \
    $(LOCAL_PATH)/../../../softbus-native/core/connection \
    $(LOCAL_PATH)/../../../softbus-native/core/transmission \
    $(LOCAL_PATH)/../../../softbus-native/core/authentication \
    $(LOCAL_PATH)/../../../softbus-native/core/bus_center \
    $(LOCAL_PATH)/../../../softbus-native/daemon \
    $(LOCAL_PATH)/../../../softbus-native/sdk \
    $(LOCAL_PATH)/../../../softbus-native/jni

# 设置编译标志
LOCAL_CFLAGS := \
    -Wall \
    -Wextra \
    -Werror \
    -fPIC \
    -DANDROID \
    -DSOFTBUS_ANDROID_USER_SPACE \
    -DSOFTBUS_VERSION=\"1.0.0\"

# Debug模式编译标志
ifeq ($(APP_OPTIM),debug)
LOCAL_CFLAGS += -g -O0 -DDEBUG
else
LOCAL_CFLAGS += -O2 -DNDEBUG
endif

# 设置链接库
LOCAL_LDLIBS := \
    -llog \
    -landroid \
    -lm \
    -lz

# 静态链接库
LOCAL_STATIC_LIBRARIES := \
    crypto_static \
    ssl_static

# 共享链接库
LOCAL_SHARED_LIBRARIES := \
    liblog \
    libandroid

# 设置模块类型为共享库
include $(BUILD_SHARED_LIBRARY)

# 导入OpenSSL预编译库
$(call import-add-path,$(LOCAL_PATH)/prebuilt)
$(call import-module,openssl)

# 创建静态库版本（可选）
include $(CLEAR_VARS)
LOCAL_MODULE := softbus_core_static
LOCAL_SRC_FILES := $(filter-out %/softbus_jni.c, $(LOCAL_SRC_FILES))
LOCAL_C_INCLUDES := $(LOCAL_C_INCLUDES)
LOCAL_CFLAGS := $(LOCAL_CFLAGS)
LOCAL_STATIC_LIBRARIES := crypto_static ssl_static
include $(BUILD_STATIC_LIBRARY)

# 创建测试可执行文件（可选）
ifeq ($(BUILD_SOFTBUS_TESTS),true)
include $(CLEAR_VARS)
LOCAL_MODULE := softbus_test
LOCAL_SRC_FILES := \
    ../../../softbus-native/tests/test_discovery.c \
    ../../../softbus-native/tests/test_connection.c \
    ../../../softbus-native/tests/test_session.c \
    ../../../softbus-native/tests/test_auth.c
LOCAL_C_INCLUDES := $(LOCAL_C_INCLUDES)
LOCAL_CFLAGS := $(LOCAL_CFLAGS) -DSOFTBUS_TEST
LOCAL_STATIC_LIBRARIES := softbus_core_static crypto_static ssl_static
LOCAL_LDLIBS := -llog -lm
include $(BUILD_EXECUTABLE)
endif
