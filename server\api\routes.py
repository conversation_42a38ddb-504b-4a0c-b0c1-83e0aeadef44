"""
灵境笔记API路由定义
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from typing import List, Optional, Dict, Any
import uuid
import time

from models.events import SN_EVENT_STREAM
from models.responses import (
    SN_ACTION_AND_STATE_UPDATE, 
    SimpleResponse, 
    ErrorResponse, 
    HealthStatus,
    NoteAtom
)
from core.sn_core_engine import SNCore

# 创建API路由器
api_router = APIRouter()

# 依赖注入：获取SN-Core实例
async def get_sn_core() -> SNCore:
    """获取SN-Core实例的依赖注入函数"""
    from main import server_instance
    if not server_instance.sn_core or not server_instance.sn_core.initialized:
        raise HTTPException(status_code=503, detail="SN-Core引擎未初始化")
    return server_instance.sn_core


@api_router.get("/health", response_model=HealthStatus)
async def get_health_status(sn_core: SNCore = Depends(get_sn_core)):
    """获取系统健康状态"""
    try:
        health_data = await sn_core.get_health_status()
        
        return HealthStatus(
            status=health_data.get("status", "unknown"),
            sn_core_status=health_data.get("status", "unknown"),
            connected_devices=len(health_data.get("modules", {})),
            uptime_seconds=health_data.get("uptime_seconds", 0),
            memory_usage_mb=health_data.get("memory_usage_mb"),
            cpu_usage_percent=health_data.get("cpu_usage_percent")
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取健康状态失败: {str(e)}")


@api_router.post("/events/process", response_model=SN_ACTION_AND_STATE_UPDATE)
async def process_event_stream(
    event_stream: SN_EVENT_STREAM,
    sn_core: SNCore = Depends(get_sn_core)
):
    """处理事件流的HTTP接口（用于测试和调试）"""
    try:
        response = await sn_core.process_event_stream(event_stream)
        if response:
            return response
        else:
            raise HTTPException(status_code=400, detail="事件处理失败，未生成响应")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理事件流失败: {str(e)}")


@api_router.get("/stats/processing", response_model=Dict[str, Any])
async def get_processing_stats(sn_core: SNCore = Depends(get_sn_core)):
    """获取处理统计信息"""
    try:
        stats = await sn_core.get_processing_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@api_router.post("/stats/reset", response_model=SimpleResponse)
async def reset_processing_stats(sn_core: SNCore = Depends(get_sn_core)):
    """重置处理统计信息"""
    try:
        await sn_core.reset_stats()
        return SimpleResponse(
            success=True,
            message="统计信息已重置"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置统计信息失败: {str(e)}")


@api_router.get("/knowledge-graph/atoms", response_model=List[NoteAtom])
async def get_note_atoms(
    limit: int = 50,
    offset: int = 0,
    user_id: Optional[str] = None,
    sn_core: SNCore = Depends(get_sn_core)
):
    """获取笔记原子列表"""
    try:
        if not sn_core.skg_engine:
            raise HTTPException(status_code=503, detail="知识图谱引擎未初始化")
        
        atoms = await sn_core.skg_engine.get_note_atoms(
            limit=limit, 
            offset=offset, 
            user_id=user_id
        )
        return atoms
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取笔记原子失败: {str(e)}")


@api_router.get("/knowledge-graph/atoms/{atom_id}", response_model=NoteAtom)
async def get_note_atom(
    atom_id: str,
    sn_core: SNCore = Depends(get_sn_core)
):
    """获取特定笔记原子"""
    try:
        if not sn_core.skg_engine:
            raise HTTPException(status_code=503, detail="知识图谱引擎未初始化")
        
        atom = await sn_core.skg_engine.get_note_atom(atom_id)
        if not atom:
            raise HTTPException(status_code=404, detail="笔记原子不存在")
        
        return atom
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取笔记原子失败: {str(e)}")


@api_router.delete("/knowledge-graph/atoms/{atom_id}", response_model=SimpleResponse)
async def delete_note_atom(
    atom_id: str,
    sn_core: SNCore = Depends(get_sn_core)
):
    """删除笔记原子"""
    try:
        if not sn_core.skg_engine:
            raise HTTPException(status_code=503, detail="知识图谱引擎未初始化")
        
        success = await sn_core.skg_engine.delete_note_atom(atom_id)
        if not success:
            raise HTTPException(status_code=404, detail="笔记原子不存在")
        
        return SimpleResponse(
            success=True,
            message=f"笔记原子 {atom_id} 已删除"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除笔记原子失败: {str(e)}")


@api_router.get("/knowledge-graph/search", response_model=List[NoteAtom])
async def search_note_atoms(
    query: str,
    limit: int = 20,
    similarity_threshold: float = 0.7,
    sn_core: SNCore = Depends(get_sn_core)
):
    """搜索笔记原子"""
    try:
        if not sn_core.skg_engine:
            raise HTTPException(status_code=503, detail="知识图谱引擎未初始化")
        
        results = await sn_core.skg_engine.search_atoms(
            query=query,
            limit=limit,
            similarity_threshold=similarity_threshold
        )
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@api_router.get("/knowledge-graph/recommendations/{atom_id}", response_model=List[NoteAtom])
async def get_recommendations(
    atom_id: str,
    limit: int = 10,
    sn_core: SNCore = Depends(get_sn_core)
):
    """获取基于特定原子的推荐"""
    try:
        if not sn_core.aie2:
            raise HTTPException(status_code=503, detail="AI洞察引擎未初始化")
        
        recommendations = await sn_core.aie2.get_recommendations(
            atom_id=atom_id,
            limit=limit
        )
        return recommendations
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取推荐失败: {str(e)}")


@api_router.post("/ai/insights", response_model=Dict[str, Any])
async def generate_insights(
    atom_ids: List[str],
    insight_types: Optional[List[str]] = None,
    sn_core: SNCore = Depends(get_sn_core)
):
    """生成AI洞察"""
    try:
        if not sn_core.aie2:
            raise HTTPException(status_code=503, detail="AI洞察引擎未初始化")
        
        insights = await sn_core.aie2.generate_insights(
            atom_ids=atom_ids,
            insight_types=insight_types or ["summary", "connections", "gaps"]
        )
        return insights
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成洞察失败: {str(e)}")


@api_router.post("/upload/file", response_model=SimpleResponse)
async def upload_file(
    file: UploadFile = File(...),
    device_id: str = Form(...),
    user_id: Optional[str] = Form(None),
    sn_core: SNCore = Depends(get_sn_core)
):
    """上传文件并处理"""
    try:
        # 检查文件大小
        if file.size and file.size > 50 * 1024 * 1024:  # 50MB
            raise HTTPException(status_code=413, detail="文件过大")
        
        # 保存文件
        import os
        from core.config import settings
        
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
        file_path = os.path.join(settings.UPLOAD_DIR, f"{file_id}{file_extension}")
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 创建处理事件
        from models.events import SN_EVENT_STREAM, InputType, ImageROIForAIProcessing, ImageSource, ROICoordinates
        
        # 根据文件类型创建相应的事件流
        if file.content_type and file.content_type.startswith("image/"):
            # 图像文件处理
            image_data = ImageROIForAIProcessing(
                image_source=ImageSource(
                    image_uri=file_path,
                    image_format=file.content_type.split("/")[1].upper(),
                    width=0,  # 需要实际读取图像获取
                    height=0,
                    capture_timestamp=int(time.time() * 1000000)
                ),
                roi_coordinates=ROICoordinates(
                    coordinate_system="2D_PIXEL",
                    top_left=[0, 0],
                    bottom_right=[100, 100]  # 占位符
                ),
                requested_ai_processing=["OCR", "OBJECT_DETECTION"]
            )
            
            event_stream = SN_EVENT_STREAM(
                event_id=str(uuid.uuid4()),
                device_id=device_id,
                user_id=user_id,
                input_type=InputType.IMAGE_ROI,
                input_data=image_data
            )
        else:
            # 其他文件类型暂时不支持
            raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        # 处理事件流
        response = await sn_core.process_event_stream(event_stream)
        
        return SimpleResponse(
            success=True,
            message=f"文件 {file.filename} 上传并处理成功",
            data={
                "file_id": file_id,
                "file_path": file_path,
                "processing_response": response.model_dump() if response else None
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@api_router.get("/devices/active", response_model=List[str])
async def get_active_devices(sn_core: SNCore = Depends(get_sn_core)):
    """获取活跃设备列表"""
    try:
        if not sn_core.dcs_engine:
            return []
        
        devices = await sn_core.dcs_engine.get_active_devices()
        return devices
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取活跃设备失败: {str(e)}")


@api_router.post("/collaboration/join", response_model=SimpleResponse)
async def join_collaboration(
    device_id: str,
    user_id: str,
    session_id: Optional[str] = None,
    sn_core: SNCore = Depends(get_sn_core)
):
    """加入协同会话"""
    try:
        if not sn_core.dcs_engine:
            raise HTTPException(status_code=503, detail="协同引擎未初始化")
        
        success = await sn_core.dcs_engine.join_collaboration(
            device_id=device_id,
            user_id=user_id,
            session_id=session_id
        )
        
        if success:
            return SimpleResponse(
                success=True,
                message="成功加入协同会话"
            )
        else:
            raise HTTPException(status_code=400, detail="加入协同会话失败")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"加入协同会话失败: {str(e)}")


@api_router.post("/collaboration/leave", response_model=SimpleResponse)
async def leave_collaboration(
    device_id: str,
    sn_core: SNCore = Depends(get_sn_core)
):
    """离开协同会话"""
    try:
        if not sn_core.dcs_engine:
            raise HTTPException(status_code=503, detail="协同引擎未初始化")
        
        success = await sn_core.dcs_engine.leave_collaboration(device_id)
        
        if success:
            return SimpleResponse(
                success=True,
                message="成功离开协同会话"
            )
        else:
            raise HTTPException(status_code=400, detail="离开协同会话失败")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"离开协同会话失败: {str(e)}")


# 错误处理
@api_router.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return ErrorResponse(
        error_code=str(exc.status_code),
        error_message=exc.detail,
        details={"status_code": exc.status_code}
    )
