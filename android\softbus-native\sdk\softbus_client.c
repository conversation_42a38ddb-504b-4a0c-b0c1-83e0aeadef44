/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * SoftBus Client Library - 客户端库实现
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#include "softbus_client.h"
#include "softbus_common.h"
#include "softbus_bus_center.h"
#include "session.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <errno.h>

#define SOFTBUS_SOCKET_PATH "/data/local/tmp/softbus_socket"
#define CLIENT_RECV_BUFFER_SIZE 4096
#define CLIENT_CONNECT_TIMEOUT_MS 5000

/* 客户端上下文 */
typedef struct {
    bool isInitialized;
    bool isConnected;
    int32_t serverSocket;
    pthread_t recvThread;
    bool threadRunning;
    pthread_mutex_t mutex;
    char pkgName[PKG_NAME_SIZE_MAX];
    
    /* 回调函数 */
    IDiscoveryCallback discoveryCallback;
    IPublishCb publishCallback;
    ISessionListener sessionListener;
    INodeStateCb nodeStateCallback;
    
    /* 统计信息 */
    uint32_t totalRequests;
    uint32_t totalResponses;
    uint64_t totalBytesSent;
    uint64_t totalBytesReceived;
} SoftBusClient;

static SoftBusClient g_client = {0};

/* 发送请求到服务器 */
static int32_t SendRequestToServer(RequestType type, const void* data, uint32_t dataLen) {
    if (!g_client.isConnected) {
        return SOFTBUS_ERR;
    }
    
    /* 构造请求消息 */
    uint32_t totalLen = sizeof(RequestMessage) + dataLen;
    RequestMessage* request = (RequestMessage*)malloc(totalLen);
    if (request == NULL) {
        return SOFTBUS_ERR;
    }
    
    request->magic = SOFTBUS_MESSAGE_MAGIC;
    request->version = SOFTBUS_PROTOCOL_VERSION;
    request->type = type;
    request->dataLen = dataLen;
    request->checksum = 0; // 简化实现，不计算校验和
    
    if (data != NULL && dataLen > 0) {
        memcpy(request->data, data, dataLen);
    }
    
    /* 发送请求 */
    ssize_t bytesSent = send(g_client.serverSocket, request, totalLen, MSG_NOSIGNAL);
    free(request);
    
    if (bytesSent != (ssize_t)totalLen) {
        SOFTBUS_LOG_ERROR("Failed to send request: %s", strerror(errno));
        return SOFTBUS_ERR;
    }
    
    g_client.totalRequests++;
    g_client.totalBytesSent += bytesSent;
    
    return SOFTBUS_OK;
}

/* 处理服务器响应 */
static void ProcessServerResponse(const ResponseMessage* response) {
    if (response == NULL) {
        return;
    }
    
    g_client.totalResponses++;
    
    switch (response->type) {
        case RESPONSE_TYPE_SUCCESS:
            SOFTBUS_LOG_DEBUG("Received success response: result=%d", response->result);
            break;
            
        case RESPONSE_TYPE_ERROR:
            SOFTBUS_LOG_ERROR("Received error response: result=%d", response->result);
            break;
            
        case RESPONSE_TYPE_EVENT:
            /* 处理事件消息 */
            if (response->dataLen > 0) {
                const EventMessage* event = (const EventMessage*)response->data;
                ProcessServerEvent(event);
            }
            break;
            
        case RESPONSE_TYPE_DATA:
            /* 处理数据消息 */
            SOFTBUS_LOG_DEBUG("Received data response: dataLen=%u", response->dataLen);
            break;
            
        default:
            SOFTBUS_LOG_WARN("Unknown response type: %d", response->type);
            break;
    }
}

/* 处理服务器事件 */
static void ProcessServerEvent(const EventMessage* event) {
    if (event == NULL) {
        return;
    }
    
    switch (event->eventType) {
        case EVENT_TYPE_DEVICE_FOUND:
            if (g_client.discoveryCallback.OnDeviceFound != NULL) {
                const DeviceFoundInfo* device = (const DeviceFoundInfo*)event->data;
                g_client.discoveryCallback.OnDeviceFound(device);
            }
            break;
            
        case EVENT_TYPE_DEVICE_ONLINE:
            if (g_client.nodeStateCallback.OnDeviceOnline != NULL) {
                DeviceInfo* device = (DeviceInfo*)event->data;
                g_client.nodeStateCallback.OnDeviceOnline(device);
            }
            break;
            
        case EVENT_TYPE_DEVICE_OFFLINE:
            if (g_client.nodeStateCallback.OnDeviceOffline != NULL) {
                const char* deviceId = (const char*)event->data;
                g_client.nodeStateCallback.OnDeviceOffline(deviceId);
            }
            break;
            
        case EVENT_TYPE_SESSION_OPENED:
            if (g_client.sessionListener.OnSessionOpened != NULL) {
                const SessionOpenedEvent* sessionEvent = (const SessionOpenedEvent*)event->data;
                g_client.sessionListener.OnSessionOpened(sessionEvent->sessionId, sessionEvent->result);
            }
            break;
            
        case EVENT_TYPE_SESSION_CLOSED:
            if (g_client.sessionListener.OnSessionClosed != NULL) {
                const SessionClosedEvent* sessionEvent = (const SessionClosedEvent*)event->data;
                g_client.sessionListener.OnSessionClosed(sessionEvent->sessionId);
            }
            break;
            
        case EVENT_TYPE_BYTES_RECEIVED:
            if (g_client.sessionListener.OnBytesReceived != NULL) {
                const BytesReceivedEvent* bytesEvent = (const BytesReceivedEvent*)event->data;
                g_client.sessionListener.OnBytesReceived(bytesEvent->sessionId, 
                                                        bytesEvent->data, bytesEvent->dataLen);
            }
            break;
            
        default:
            SOFTBUS_LOG_WARN("Unknown event type: %u", event->eventType);
            break;
    }
}

/* 接收线程函数 */
static void* RecvThreadFunc(void* arg) {
    (void)arg;
    
    char buffer[CLIENT_RECV_BUFFER_SIZE];
    ssize_t bytesReceived;
    
    SOFTBUS_LOG_INFO("Client receive thread started");
    
    while (g_client.threadRunning && g_client.isConnected) {
        bytesReceived = recv(g_client.serverSocket, buffer, sizeof(buffer), 0);
        if (bytesReceived > 0) {
            g_client.totalBytesReceived += bytesReceived;
            
            /* 解析响应消息 */
            if (bytesReceived >= (ssize_t)sizeof(ResponseMessage)) {
                const ResponseMessage* response = (const ResponseMessage*)buffer;
                if (response->magic == SOFTBUS_MESSAGE_MAGIC) {
                    ProcessServerResponse(response);
                } else {
                    SOFTBUS_LOG_WARN("Invalid response magic: 0x%x", response->magic);
                }
            }
        } else if (bytesReceived == 0) {
            SOFTBUS_LOG_INFO("Server connection closed");
            break;
        } else {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                usleep(10000); // 10ms
                continue;
            }
            SOFTBUS_LOG_ERROR("Receive error: %s", strerror(errno));
            break;
        }
    }
    
    pthread_mutex_lock(&g_client.mutex);
    g_client.isConnected = false;
    pthread_mutex_unlock(&g_client.mutex);
    
    SOFTBUS_LOG_INFO("Client receive thread stopped");
    return NULL;
}

/* 连接到SoftBus服务器 */
static int32_t ConnectToServer(void) {
    int32_t sockfd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (sockfd < 0) {
        SOFTBUS_LOG_ERROR("Failed to create client socket: %s", strerror(errno));
        return SOFTBUS_ERR;
    }
    
    struct sockaddr_un serverAddr;
    memset(&serverAddr, 0, sizeof(serverAddr));
    serverAddr.sun_family = AF_UNIX;
    strncpy(serverAddr.sun_path, SOFTBUS_SOCKET_PATH, sizeof(serverAddr.sun_path) - 1);
    
    if (connect(sockfd, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) < 0) {
        SOFTBUS_LOG_ERROR("Failed to connect to server: %s", strerror(errno));
        close(sockfd);
        return SOFTBUS_ERR;
    }
    
    g_client.serverSocket = sockfd;
    g_client.isConnected = true;
    
    /* 启动接收线程 */
    g_client.threadRunning = true;
    if (pthread_create(&g_client.recvThread, NULL, RecvThreadFunc, NULL) != 0) {
        SOFTBUS_LOG_ERROR("Failed to create receive thread: %s", strerror(errno));
        close(sockfd);
        g_client.isConnected = false;
        return SOFTBUS_ERR;
    }
    
    SOFTBUS_LOG_INFO("Connected to SoftBus server");
    return SOFTBUS_OK;
}

/* 初始化SoftBus客户端 */
int32_t SoftBusClientInit(const char* pkgName) {
    if (g_client.isInitialized) {
        return SOFTBUS_OK;
    }
    
    if (pkgName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    memset(&g_client, 0, sizeof(g_client));
    
    if (pthread_mutex_init(&g_client.mutex, NULL) != 0) {
        SOFTBUS_LOG_ERROR("Failed to initialize client mutex");
        return SOFTBUS_ERR;
    }
    
    strncpy(g_client.pkgName, pkgName, sizeof(g_client.pkgName) - 1);
    
    /* 连接到服务器 */
    if (ConnectToServer() != SOFTBUS_OK) {
        pthread_mutex_destroy(&g_client.mutex);
        return SOFTBUS_ERR;
    }
    
    g_client.isInitialized = true;
    
    SOFTBUS_LOG_INFO("SoftBus client initialized: %s", pkgName);
    return SOFTBUS_OK;
}

/* 反初始化SoftBus客户端 */
void SoftBusClientDeinit(void) {
    if (!g_client.isInitialized) {
        return;
    }
    
    /* 停止接收线程 */
    g_client.threadRunning = false;
    if (g_client.isConnected) {
        shutdown(g_client.serverSocket, SHUT_RDWR);
        pthread_join(g_client.recvThread, NULL);
        close(g_client.serverSocket);
    }
    
    pthread_mutex_destroy(&g_client.mutex);
    
    SOFTBUS_LOG_INFO("SoftBus client deinitialized");
    memset(&g_client, 0, sizeof(g_client));
}

/* 开始设备发现 */
int32_t StartDiscovery(const char* pkgName, const SubscribeInfo* subscribeInfo, 
                      const IDiscoveryCallback* cb) {
    if (!g_client.isInitialized || !g_client.isConnected) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL || subscribeInfo == NULL || cb == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 保存回调函数 */
    memcpy(&g_client.discoveryCallback, cb, sizeof(IDiscoveryCallback));
    
    /* 构造请求数据 */
    DiscoveryRequest request;
    memset(&request, 0, sizeof(request));
    strncpy(request.pkgName, pkgName, sizeof(request.pkgName) - 1);
    memcpy(&request.subscribeInfo, subscribeInfo, sizeof(SubscribeInfo));
    
    /* 发送请求 */
    return SendRequestToServer(REQUEST_TYPE_DISCOVERY_START, &request, sizeof(request));
}

/* 停止设备发现 */
int32_t StopDiscovery(const char* pkgName, int32_t subscribeId) {
    if (!g_client.isInitialized || !g_client.isConnected) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 构造请求数据 */
    StopDiscoveryRequest request;
    memset(&request, 0, sizeof(request));
    strncpy(request.pkgName, pkgName, sizeof(request.pkgName) - 1);
    request.subscribeId = subscribeId;
    
    /* 发送请求 */
    return SendRequestToServer(REQUEST_TYPE_DISCOVERY_STOP, &request, sizeof(request));
}

/* 发布服务 */
int32_t PublishLNN(const char* pkgName, const PublishInfo* publishInfo, 
                   const IPublishCb* cb) {
    if (!g_client.isInitialized || !g_client.isConnected) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL || publishInfo == NULL || cb == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 保存回调函数 */
    memcpy(&g_client.publishCallback, cb, sizeof(IPublishCb));
    
    /* 构造请求数据 */
    PublishRequest request;
    memset(&request, 0, sizeof(request));
    strncpy(request.pkgName, pkgName, sizeof(request.pkgName) - 1);
    memcpy(&request.publishInfo, publishInfo, sizeof(PublishInfo));
    
    /* 发送请求 */
    return SendRequestToServer(REQUEST_TYPE_PUBLISH_START, &request, sizeof(request));
}

/* 停止发布服务 */
int32_t StopPublishLNN(const char* pkgName, int32_t publishId) {
    if (!g_client.isInitialized || !g_client.isConnected) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 构造请求数据 */
    StopPublishRequest request;
    memset(&request, 0, sizeof(request));
    strncpy(request.pkgName, pkgName, sizeof(request.pkgName) - 1);
    request.publishId = publishId;
    
    /* 发送请求 */
    return SendRequestToServer(REQUEST_TYPE_PUBLISH_STOP, &request, sizeof(request));
}

/* 创建会话服务器 */
int32_t CreateSessionServer(const char* pkgName, const char* sessionName, 
                           const ISessionListener* listener) {
    if (!g_client.isInitialized || !g_client.isConnected) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL || sessionName == NULL || listener == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 保存回调函数 */
    memcpy(&g_client.sessionListener, listener, sizeof(ISessionListener));
    
    /* 构造请求数据 */
    CreateSessionRequest request;
    memset(&request, 0, sizeof(request));
    strncpy(request.pkgName, pkgName, sizeof(request.pkgName) - 1);
    strncpy(request.sessionName, sessionName, sizeof(request.sessionName) - 1);
    
    /* 发送请求 */
    return SendRequestToServer(REQUEST_TYPE_SESSION_CREATE, &request, sizeof(request));
}

/* 移除会话服务器 */
int32_t RemoveSessionServer(const char* pkgName, const char* sessionName) {
    if (!g_client.isInitialized || !g_client.isConnected) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL || sessionName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 构造请求数据 */
    RemoveSessionRequest request;
    memset(&request, 0, sizeof(request));
    strncpy(request.pkgName, pkgName, sizeof(request.pkgName) - 1);
    strncpy(request.sessionName, sessionName, sizeof(request.sessionName) - 1);
    
    /* 发送请求 */
    return SendRequestToServer(REQUEST_TYPE_SESSION_REMOVE, &request, sizeof(request));
}

/* 打开会话 */
int32_t OpenSession(const char* mySessionName, const char* peerSessionName, 
                   const char* peerNetworkId, const char* groupId, 
                   const SessionAttribute* attr) {
    if (!g_client.isInitialized || !g_client.isConnected) {
        return SOFTBUS_NO_INIT;
    }
    
    if (mySessionName == NULL || peerSessionName == NULL || peerNetworkId == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 构造请求数据 */
    OpenSessionRequest request;
    memset(&request, 0, sizeof(request));
    strncpy(request.mySessionName, mySessionName, sizeof(request.mySessionName) - 1);
    strncpy(request.peerSessionName, peerSessionName, sizeof(request.peerSessionName) - 1);
    strncpy(request.peerNetworkId, peerNetworkId, sizeof(request.peerNetworkId) - 1);
    
    if (groupId != NULL) {
        strncpy(request.groupId, groupId, sizeof(request.groupId) - 1);
    }
    
    if (attr != NULL) {
        memcpy(&request.attr, attr, sizeof(SessionAttribute));
    }
    
    /* 发送请求 */
    int32_t result = SendRequestToServer(REQUEST_TYPE_SESSION_OPEN, &request, sizeof(request));
    if (result == SOFTBUS_OK) {
        /* 返回一个模拟的会话ID */
        static int32_t nextSessionId = 1000;
        return nextSessionId++;
    }
    
    return result;
}

/* 关闭会话 */
void CloseSession(int32_t sessionId) {
    if (!g_client.isInitialized || !g_client.isConnected) {
        return;
    }
    
    /* 构造请求数据 */
    CloseSessionRequest request;
    memset(&request, 0, sizeof(request));
    request.sessionId = sessionId;
    
    /* 发送请求 */
    SendRequestToServer(REQUEST_TYPE_SESSION_CLOSE, &request, sizeof(request));
}

/* 发送字节数据 */
int32_t SendBytes(int32_t sessionId, const void* data, uint32_t len) {
    if (!g_client.isInitialized || !g_client.isConnected) {
        return SOFTBUS_NO_INIT;
    }
    
    if (data == NULL || len == 0) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 构造请求数据 */
    uint32_t requestSize = sizeof(SendDataRequest) + len;
    SendDataRequest* request = (SendDataRequest*)malloc(requestSize);
    if (request == NULL) {
        return SOFTBUS_ERR;
    }
    
    memset(request, 0, sizeof(SendDataRequest));
    request->sessionId = sessionId;
    request->dataLen = len;
    memcpy(request->data, data, len);
    
    /* 发送请求 */
    int32_t result = SendRequestToServer(REQUEST_TYPE_SESSION_SEND, request, requestSize);
    free(request);
    
    return result;
}

/* 注册设备状态监听 */
int32_t RegNodeDeviceStateCb(const char* pkgName, INodeStateCb* cb) {
    if (!g_client.isInitialized || pkgName == NULL || cb == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    /* 保存回调函数 */
    memcpy(&g_client.nodeStateCallback, cb, sizeof(INodeStateCb));
    
    /* 构造请求数据 */
    RegNodeStateRequest request;
    memset(&request, 0, sizeof(request));
    strncpy(request.pkgName, pkgName, sizeof(request.pkgName) - 1);
    
    /* 发送请求 */
    return SendRequestToServer(REQUEST_TYPE_REG_NODE_STATE, &request, sizeof(request));
}

/* 获取客户端统计信息 */
int32_t GetClientStatistics(ClientStatistics* stats) {
    if (!g_client.isInitialized || stats == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_client.mutex);
    
    memset(stats, 0, sizeof(ClientStatistics));
    stats->isConnected = g_client.isConnected;
    stats->totalRequests = g_client.totalRequests;
    stats->totalResponses = g_client.totalResponses;
    stats->totalBytesSent = g_client.totalBytesSent;
    stats->totalBytesReceived = g_client.totalBytesReceived;
    strncpy(stats->pkgName, g_client.pkgName, sizeof(stats->pkgName) - 1);
    
    pthread_mutex_unlock(&g_client.mutex);
    
    return SOFTBUS_OK;
}
