"""
WebSocket连接管理器
用于管理客户端WebSocket连接和实时通信
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger
import time


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接字典：device_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        
        # 连接元数据：device_id -> metadata
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
        # 消息队列：device_id -> List[message]
        self.message_queues: Dict[str, List[Dict[str, Any]]] = {}
        
        # 统计信息
        self.stats = {
            "total_connections": 0,
            "current_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "connection_errors": 0
        }
    
    async def connect(self, websocket: WebSocket, device_id: str):
        """接受新的WebSocket连接"""
        try:
            await websocket.accept()
            
            # 如果设备已连接，先断开旧连接
            if device_id in self.active_connections:
                await self._disconnect_device(device_id, "new_connection_replacing")
            
            # 添加新连接
            self.active_connections[device_id] = websocket
            self.connection_metadata[device_id] = {
                "connected_at": time.time(),
                "last_activity": time.time(),
                "message_count": 0
            }
            
            # 初始化消息队列
            if device_id not in self.message_queues:
                self.message_queues[device_id] = []
            
            # 更新统计
            self.stats["total_connections"] += 1
            self.stats["current_connections"] = len(self.active_connections)
            
            logger.info(f"设备 {device_id} 已连接，当前连接数: {self.stats['current_connections']}")
            
            # 发送欢迎消息
            await self.send_to_device(device_id, {
                "type": "connection_established",
                "device_id": device_id,
                "timestamp": int(time.time() * 1000000),
                "message": "WebSocket连接已建立"
            })
            
            # 发送队列中的待发消息
            await self._send_queued_messages(device_id)
            
        except Exception as e:
            logger.error(f"设备 {device_id} 连接失败: {e}")
            self.stats["connection_errors"] += 1
            raise
    
    async def disconnect(self, device_id: str, reason: str = "client_disconnect"):
        """断开设备连接"""
        await self._disconnect_device(device_id, reason)
    
    async def _disconnect_device(self, device_id: str, reason: str):
        """内部断开设备连接方法"""
        if device_id in self.active_connections:
            try:
                websocket = self.active_connections[device_id]
                
                # 发送断开连接消息
                try:
                    await websocket.send_json({
                        "type": "connection_closing",
                        "reason": reason,
                        "timestamp": int(time.time() * 1000000)
                    })
                except:
                    pass  # 忽略发送失败
                
                # 关闭连接
                try:
                    await websocket.close()
                except:
                    pass  # 忽略关闭失败
                
                # 清理连接信息
                del self.active_connections[device_id]
                
                if device_id in self.connection_metadata:
                    connection_time = time.time() - self.connection_metadata[device_id]["connected_at"]
                    logger.info(f"设备 {device_id} 断开连接，连接时长: {connection_time:.2f}秒，原因: {reason}")
                    del self.connection_metadata[device_id]
                
                # 更新统计
                self.stats["current_connections"] = len(self.active_connections)
                
            except Exception as e:
                logger.error(f"断开设备 {device_id} 连接时发生错误: {e}")
    
    async def send_to_device(self, device_id: str, message: Dict[str, Any]) -> bool:
        """向特定设备发送消息"""
        if device_id not in self.active_connections:
            # 设备未连接，将消息加入队列
            if device_id not in self.message_queues:
                self.message_queues[device_id] = []
            
            self.message_queues[device_id].append(message)
            logger.warning(f"设备 {device_id} 未连接，消息已加入队列")
            return False
        
        try:
            websocket = self.active_connections[device_id]
            await websocket.send_json(message)
            
            # 更新统计和元数据
            self.stats["messages_sent"] += 1
            if device_id in self.connection_metadata:
                self.connection_metadata[device_id]["last_activity"] = time.time()
                self.connection_metadata[device_id]["message_count"] += 1
            
            logger.debug(f"消息已发送到设备 {device_id}")
            return True
            
        except WebSocketDisconnect:
            logger.warning(f"设备 {device_id} 连接已断开，移除连接")
            await self._disconnect_device(device_id, "websocket_disconnect")
            return False
        except Exception as e:
            logger.error(f"向设备 {device_id} 发送消息失败: {e}")
            return False
    
    async def broadcast_to_all(self, message: Dict[str, Any], exclude_devices: Optional[List[str]] = None):
        """向所有连接的设备广播消息"""
        exclude_devices = exclude_devices or []
        
        tasks = []
        for device_id in self.active_connections:
            if device_id not in exclude_devices:
                tasks.append(self.send_to_device(device_id, message))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for result in results if result is True)
            logger.info(f"广播消息完成，成功发送到 {success_count}/{len(tasks)} 个设备")
    
    async def broadcast_to_devices(self, device_ids: List[str], message: Dict[str, Any]):
        """向指定设备列表广播消息"""
        tasks = []
        for device_id in device_ids:
            tasks.append(self.send_to_device(device_id, message))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for result in results if result is True)
            logger.info(f"向指定设备广播消息完成，成功发送到 {success_count}/{len(tasks)} 个设备")
    
    async def _send_queued_messages(self, device_id: str):
        """发送队列中的消息"""
        if device_id not in self.message_queues:
            return
        
        queued_messages = self.message_queues[device_id]
        if not queued_messages:
            return
        
        logger.info(f"向设备 {device_id} 发送 {len(queued_messages)} 条队列消息")
        
        for message in queued_messages:
            success = await self.send_to_device(device_id, message)
            if not success:
                break  # 如果发送失败，停止发送剩余消息
        
        # 清空队列
        self.message_queues[device_id] = []
    
    async def disconnect_all(self):
        """断开所有连接"""
        logger.info("断开所有WebSocket连接...")
        
        disconnect_tasks = []
        for device_id in list(self.active_connections.keys()):
            disconnect_tasks.append(self._disconnect_device(device_id, "server_shutdown"))
        
        if disconnect_tasks:
            await asyncio.gather(*disconnect_tasks, return_exceptions=True)
        
        # 清理所有数据
        self.active_connections.clear()
        self.connection_metadata.clear()
        self.message_queues.clear()
        
        logger.info("所有WebSocket连接已断开")
    
    def get_connected_devices(self) -> List[str]:
        """获取已连接设备列表"""
        return list(self.active_connections.keys())
    
    def is_device_connected(self, device_id: str) -> bool:
        """检查设备是否已连接"""
        return device_id in self.active_connections
    
    def get_connection_info(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取设备连接信息"""
        if device_id not in self.connection_metadata:
            return None
        
        metadata = self.connection_metadata[device_id].copy()
        metadata["is_connected"] = device_id in self.active_connections
        metadata["queued_messages"] = len(self.message_queues.get(device_id, []))
        
        return metadata
    
    def get_stats(self) -> Dict[str, Any]:
        """获取WebSocket管理器统计信息"""
        stats = self.stats.copy()
        stats["connected_devices"] = list(self.active_connections.keys())
        stats["total_queued_messages"] = sum(len(queue) for queue in self.message_queues.values())
        
        return stats
    
    async def ping_all_connections(self):
        """向所有连接发送ping消息检查连接状态"""
        ping_message = {
            "type": "ping",
            "timestamp": int(time.time() * 1000000)
        }
        
        disconnected_devices = []
        
        for device_id, websocket in self.active_connections.items():
            try:
                await websocket.send_json(ping_message)
            except Exception as e:
                logger.warning(f"设备 {device_id} ping失败: {e}")
                disconnected_devices.append(device_id)
        
        # 清理失效连接
        for device_id in disconnected_devices:
            await self._disconnect_device(device_id, "ping_failed")
    
    async def cleanup_inactive_connections(self, timeout_seconds: int = 300):
        """清理不活跃的连接"""
        current_time = time.time()
        inactive_devices = []
        
        for device_id, metadata in self.connection_metadata.items():
            if current_time - metadata["last_activity"] > timeout_seconds:
                inactive_devices.append(device_id)
        
        for device_id in inactive_devices:
            logger.info(f"清理不活跃连接: {device_id}")
            await self._disconnect_device(device_id, "inactive_timeout")
    
    async def start_background_tasks(self):
        """启动后台任务"""
        async def ping_task():
            while True:
                await asyncio.sleep(30)  # 每30秒ping一次
                await self.ping_all_connections()
        
        async def cleanup_task():
            while True:
                await asyncio.sleep(60)  # 每分钟清理一次
                await self.cleanup_inactive_connections()
        
        # 启动后台任务
        asyncio.create_task(ping_task())
        asyncio.create_task(cleanup_task())
