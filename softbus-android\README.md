# SoftBus Android Framework & Service

基于OpenHarmony DSoftBus的Android Framework和Service实现，为Android用户空间应用提供分布式软总线能力。

## 项目概述

SoftBus Android是OpenHarmony分布式软总线在Android平台的完整移植，采用Android Framework + System Service架构，为用户空间应用提供统一的分布式设备发现、连接、传输和管理能力。

### 核心特性

- 🚀 **Android Framework集成** - 完整的Android Framework API
- 🔧 **System Service架构** - 独立的系统服务进程
- 🌐 **分布式设备发现** - WiFi、蓝牙、BLE多协议支持
- 🔗 **智能连接管理** - 自动连接建立和故障恢复
- 📡 **多模式数据传输** - 字节流、消息、文件、音视频流
- 🔐 **端到端安全** - 设备认证和数据加密
- 📱 **跨进程通信** - AIDL接口和Binder机制

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                   Android Applications                      │
├─────────────────────────────────────────────────────────────┤
│                   SoftBus Framework API                    │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ Discovery   │ Connection  │Transmission │   Device    │  │
│  │   Manager   │   Manager   │   Manager   │  Manager    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    AIDL Interface Layer                    │
├─────────────────────────────────────────────────────────────┤
│                  SoftBus System Service                    │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                SoftBus Core Engine                     │  │
│  │  ┌─────────┬─────────┬─────────┬─────────┬─────────┐  │  │
│  │  │Discovery│Connection│Transport│  Auth   │Bus Center│  │  │
│  │  │ Module  │ Module  │ Module  │ Module  │ Module  │  │  │
│  │  └─────────┴─────────┴─────────┴─────────┴─────────┘  │  │
│  └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    Android System                          │
│              (WiFi, Bluetooth, Network)                    │
└─────────────────────────────────────────────────────────────┘
```

## 目录结构

```
softbus-android/
├── framework/                          # Android Framework层
│   ├── java/                          # Java Framework API
│   │   └── android/softbus/           # SoftBus Framework包
│   │       ├── SoftBusManager.java    # 主管理器
│   │       ├── discovery/             # 设备发现API
│   │       ├── connection/            # 连接管理API
│   │       ├── transmission/          # 数据传输API
│   │       └── device/                # 设备管理API
│   └── aidl/                          # AIDL接口定义
│       └── android/softbus/           # AIDL包
├── service/                           # System Service层
│   ├── java/                          # Java Service实现
│   │   └── com/android/server/softbus/ # Service包
│   └── native/                        # Native核心实现
│       ├── core/                      # 核心模块
│       │   ├── discovery/             # 设备发现
│       │   ├── connection/            # 连接管理
│       │   ├── transmission/          # 数据传输
│       │   ├── authentication/        # 安全认证
│       │   └── bus_center/            # 总线中心
│       ├── adapter/                   # 平台适配
│       └── interfaces/                # JNI接口
├── sdk/                               # 客户端SDK
│   └── java/                          # Java SDK
├── tools/                             # 开发工具
├── tests/                             # 测试代码
└── docs/                              # 文档
```

## 快速开始

### 环境要求

**开发环境**:
- Android Studio 4.2+
- Android SDK API 26+
- Android NDK r21+
- OpenSSL 1.1.1+

**AOSP构建环境**:
- Ubuntu 18.04+ / macOS 10.15+
- Android Open Source Project (AOSP)
- 完整的Android构建工具链

### 编译构建

#### 1. 独立构建（开发测试）

```bash
# 克隆项目
git clone https://github.com/your-org/softbus-android.git
cd softbus-android

# 构建所有组件
./build.sh

# 构建特定组件
./build.sh --tests --docs

# 清理构建
./build.sh clean
```

#### 2. 集成到AOSP构建

```bash
# 设置Android构建环境
cd /path/to/aosp
source build/envsetup.sh
lunch aosp_arm64-eng

# 安装SoftBus到AOSP源码树
cd /path/to/softbus-android
./build.sh install --android-root /path/to/aosp

# 构建Android系统
cd /path/to/aosp
m -j$(nproc)
```

### 集成到Android系统

1. **添加到AOSP源码**
```bash
# 复制到Android源码树
cp -r softbus-android/ frameworks/base/core/java/android/softbus/
cp -r service/ frameworks/base/services/core/java/com/android/server/softbus/
```

2. **修改系统配置**
```xml
<!-- frameworks/base/core/res/res/values/config.xml -->
<bool name="config_enableSoftBusService">true</bool>
```

3. **添加权限定义**
```xml
<!-- frameworks/base/core/res/AndroidManifest.xml -->
<permission android:name="android.permission.ACCESS_SOFTBUS"
    android:protectionLevel="normal" />
```

### 应用集成

```java
// 获取SoftBus管理器
SoftBusManager softBusManager = (SoftBusManager) getSystemService(Context.SOFTBUS_SERVICE);

// 设备发现
DiscoveryConfig config = new DiscoveryConfig.Builder()
    .setDiscoveryMode(DiscoveryMode.ACTIVE)
    .setMedium(Medium.WIFI | Medium.BLUETOOTH)
    .setCapability("osdData")
    .build();

softBusManager.startDiscovery(config, new DiscoveryCallback() {
    @Override
    public void onDeviceFound(DeviceInfo device) {
        Log.i(TAG, "Device found: " + device.getDeviceName());
    }
});

// 建立连接
softBusManager.connectDevice(deviceId, new ConnectionCallback() {
    @Override
    public void onConnected(String sessionId) {
        // 发送数据
        softBusManager.sendData(sessionId, data);
    }
});
```

## 核心功能

### 1. 设备发现 (Discovery)
- **多协议支持**: WiFi (mDNS)、蓝牙 (BR/EDR)、BLE
- **发现模式**: 主动发现、被动广播、混合模式
- **能力匹配**: 基于设备能力的智能过滤
- **范围控制**: 近场、局域网、广域网

### 2. 连接管理 (Connection)
- **自动连接**: 智能选择最优连接方式
- **连接池**: 复用和管理多个连接
- **故障恢复**: 自动重连和降级处理
- **QoS保证**: 不同业务的服务质量

### 3. 数据传输 (Transmission)
- **多种数据类型**: 字节流、消息、文件、音视频流
- **传输模式**: 可靠传输、实时传输
- **流控制**: 拥塞控制和流量整形
- **大文件传输**: 分片传输和断点续传

### 4. 安全认证 (Authentication)
- **设备认证**: 证书认证、PIN码认证
- **数据加密**: AES-256-GCM端到端加密
- **密钥管理**: 动态密钥协商和更新
- **权限控制**: 细粒度访问控制

### 5. 设备管理 (Device Management)
- **设备信息**: 设备类型、能力、状态
- **拓扑管理**: 网络拓扑发现和维护
- **状态同步**: 设备状态实时同步
- **生命周期**: 设备上线、离线管理

## API文档

### Framework API

```java
public class SoftBusManager {
    // 设备发现
    public void startDiscovery(DiscoveryConfig config, DiscoveryCallback callback);
    public void stopDiscovery(int discoveryId);

    // 连接管理
    public void connectDevice(String deviceId, ConnectionCallback callback);
    public void disconnectDevice(String sessionId);

    // 数据传输
    public void sendData(String sessionId, byte[] data);
    public void sendMessage(String sessionId, String message);
    public void sendFile(String sessionId, String filePath);

    // 设备管理
    public List<DeviceInfo> getConnectedDevices();
    public DeviceInfo getLocalDeviceInfo();
}
```

### AIDL接口

```java
interface ISoftBusService {
    int startDiscovery(in DiscoveryConfig config, ISoftBusCallback callback);
    void stopDiscovery(int discoveryId);

    String connectDevice(String deviceId, ISoftBusCallback callback);
    void disconnectDevice(String sessionId);

    void sendData(String sessionId, in byte[] data);
    List<DeviceInfo> getConnectedDevices();
}
```

## 性能指标

### 延迟指标
- **设备发现**: <2秒 (WiFi), <5秒 (蓝牙)
- **连接建立**: <500ms (WiFi), <1秒 (蓝牙)
- **数据传输**: <10ms (WiFi), <50ms (蓝牙)
- **跨进程调用**: <5ms (Binder)

### 吞吐量指标
- **WiFi传输**: >100MB/s
- **蓝牙传输**: >1MB/s
- **BLE传输**: >100KB/s
- **并发连接**: >50个设备

### 资源占用
- **内存占用**: <50MB (Service进程)
- **CPU占用**: <5% (空闲时)
- **电量消耗**: <1% (后台运行)

## 开发指南

### 添加新的传输协议

1. **实现协议适配器**
```java
public class CustomProtocolAdapter implements IProtocolAdapter {
    @Override
    public void startDiscovery(DiscoveryConfig config) { }

    @Override
    public void connect(String deviceId, ConnectionCallback callback) { }
}
```

2. **注册协议**
```java
ProtocolManager.getInstance().registerProtocol("custom", new CustomProtocolAdapter());
```

### 扩展设备能力

```java
public class CustomCapability extends DeviceCapability {
    public static final String CAPABILITY_CUSTOM = "custom_capability";

    @Override
    public boolean isSupported(DeviceInfo device) {
        return device.hasCapability(CAPABILITY_CUSTOM);
    }
}
```

## 测试验证

### 单元测试
```bash
./gradlew test
```

### 集成测试
```bash
./gradlew connectedAndroidTest
```

### 性能测试
```bash
./tools/performance_test.sh
```

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查权限配置
   - 查看系统日志

2. **设备发现失败**
   - 确认网络连接
   - 检查防火墙设置

3. **连接建立失败**
   - 验证设备认证
   - 检查协议兼容性

### 调试方法

```bash
# 查看服务日志
adb logcat | grep SoftBus

# 检查服务状态
adb shell dumpsys softbus

# 性能分析
adb shell top | grep softbus
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用Apache 2.0许可证。

## 联系我们

- 项目主页: https://github.com/your-org/softbus-android
- 问题反馈: https://github.com/your-org/softbus-android/issues
- 邮箱: <EMAIL>

---

**SoftBus Android Framework & Service** - 让分布式连接更简单 🚀
