/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * SoftBus Session Management - 会话管理和数据传输
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#ifndef SESSION_H
#define SESSION_H

#include <stdint.h>
#include <stdbool.h>
#include "softbus_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 创建会话服务器
 * @param pkgName 包名
 * @param sessionName 会话名称
 * @param listener 会话监听器
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t CreateSessionServer(const char *pkgName, const char *sessionName, 
                           const ISessionListener *listener);

/**
 * 移除会话服务器
 * @param pkgName 包名
 * @param sessionName 会话名称
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t RemoveSessionServer(const char *pkgName, const char *sessionName);

/**
 * 打开会话
 * @param mySessionName 本地会话名称
 * @param peerSessionName 对端会话名称
 * @param peerNetworkId 对端网络ID
 * @param groupId 组ID
 * @param attr 会话属性
 * @return 会话ID，负数表示失败
 */
int32_t OpenSession(const char *mySessionName, const char *peerSessionName, 
                   const char *peerNetworkId, const char *groupId, 
                   const SessionAttribute *attr);

/**
 * 打开认证会话
 * @param mySessionName 本地会话名称
 * @param peerSessionName 对端会话名称
 * @param peerNetworkId 对端网络ID
 * @param groupId 组ID
 * @param attr 会话属性
 * @return 会话ID，负数表示失败
 */
int32_t OpenAuthSession(const char *mySessionName, const char *peerSessionName, 
                       const char *peerNetworkId, const char *groupId, 
                       const SessionAttribute *attr);

/**
 * 关闭会话
 * @param sessionId 会话ID
 */
void CloseSession(int32_t sessionId);

/**
 * 发送字节数据
 * @param sessionId 会话ID
 * @param data 数据指针
 * @param len 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SendBytes(int32_t sessionId, const void *data, uint32_t len);

/**
 * 发送消息
 * @param sessionId 会话ID
 * @param data 消息数据
 * @param len 消息长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SendMessage(int32_t sessionId, const void *data, uint32_t len);

/**
 * 发送流数据
 * @param sessionId 会话ID
 * @param data 流数据
 * @param ext 扩展数据
 * @param param 流参数
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SendStream(int32_t sessionId, const StreamData *data, 
                  const StreamData *ext, const StreamFrameInfo *param);

/**
 * 发送文件
 * @param sessionId 会话ID
 * @param sFileList 源文件列表
 * @param dFileList 目标文件列表
 * @param fileCnt 文件数量
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SendFile(int32_t sessionId, const char *sFileList[], 
                const char *dFileList[], uint32_t fileCnt);

/**
 * 获取会话名称
 * @param sessionId 会话ID
 * @param sessionName 会话名称缓冲区
 * @param len 缓冲区长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetMySessionName(int32_t sessionId, char *sessionName, uint32_t len);

/**
 * 获取对端会话名称
 * @param sessionId 会话ID
 * @param sessionName 会话名称缓冲区
 * @param len 缓冲区长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetPeerSessionName(int32_t sessionId, char *sessionName, uint32_t len);

/**
 * 获取对端设备ID
 * @param sessionId 会话ID
 * @param networkId 设备ID缓冲区
 * @param len 缓冲区长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetPeerDeviceId(int32_t sessionId, char *networkId, uint32_t len);

/**
 * 获取会话侧
 * @param sessionId 会话ID
 * @return 会话侧，负数表示失败
 */
int32_t GetSessionSide(int32_t sessionId);

/**
 * 设置文件接收监听器
 * @param sessionId 会话ID
 * @param recvListener 接收监听器
 * @param rootDir 根目录
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SetFileReceiveListener(int32_t sessionId, const IFileCb *recvListener, 
                              const char *rootDir);

/**
 * 设置文件发送监听器
 * @param sessionId 会话ID
 * @param sendListener 发送监听器
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SetFileSendListener(int32_t sessionId, const IFileCb *sendListener);

/**
 * 获取会话选项
 * @param sessionId 会话ID
 * @param type 选项类型
 * @param optionData 选项数据
 * @param len 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetSessionOption(int32_t sessionId, int32_t type, void *optionData, uint32_t len);

/**
 * 设置会话选项
 * @param sessionId 会话ID
 * @param type 选项类型
 * @param optionData 选项数据
 * @param len 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SetSessionOption(int32_t sessionId, int32_t type, const void *optionData, uint32_t len);

/**
 * 是否为服务器端会话
 * @param sessionId 会话ID
 * @return true 是服务器端，false 是客户端
 */
bool IsServerSide(int32_t sessionId);

/**
 * 获取会话句柄
 * @param sessionId 会话ID
 * @param handle 句柄缓冲区
 * @param handleLen 缓冲区长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetSessionHandle(int32_t sessionId, int32_t *handle, uint32_t handleLen);

/**
 * 禁用会话监听器
 * @param sessionId 会话ID
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t DisableSessionListener(int32_t sessionId);

/**
 * 启用会话监听器
 * @param sessionId 会话ID
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t EnableSessionListener(int32_t sessionId);

/**
 * 获取会话统计信息
 * @param sessionId 会话ID
 * @param statistics 统计信息
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetSessionStatistics(int32_t sessionId, SessionStatistics *statistics);

/**
 * 重置会话统计信息
 * @param sessionId 会话ID
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ResetSessionStatistics(int32_t sessionId);

/**
 * QoS报告
 * @param sessionId 会话ID
 * @param appType 应用类型
 * @param quality 质量等级
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t QosReport(int32_t sessionId, int32_t appType, int32_t quality);

/**
 * 流统计信息
 */
typedef struct {
    uint64_t sendBytes;
    uint64_t sendPackets;
    uint64_t recvBytes;
    uint64_t recvPackets;
    uint32_t sendBitRate;
    uint32_t recvBitRate;
    uint32_t sendPacketLossRate;
    uint32_t recvPacketLossRate;
    uint32_t sendJitter;
    uint32_t recvJitter;
    uint32_t rtt;
} StreamStatistics;

/**
 * 会话统计信息
 */
typedef struct {
    uint64_t totalSendBytes;
    uint64_t totalRecvBytes;
    uint32_t totalSendTime;
    uint32_t totalRecvTime;
    uint32_t connectTime;
    uint32_t maxSendBitRate;
    uint32_t maxRecvBitRate;
    uint32_t avgSendBitRate;
    uint32_t avgRecvBitRate;
    StreamStatistics streamStats;
} SessionStatistics;

/**
 * 会话选项类型
 */
typedef enum {
    SESSION_OPTION_ENCRYPT_TYPE = 0,
    SESSION_OPTION_ALGORITHM_TYPE = 1,
    SESSION_OPTION_CRC_TYPE = 2,
    SESSION_OPTION_COMPRESS_TYPE = 3,
    SESSION_OPTION_MAX_IDLE_TIMEOUT = 4,
    SESSION_OPTION_MAX_LATENCY = 5,
    SESSION_OPTION_MAX_WAIT_TIMEOUT = 6,
    SESSION_OPTION_BUTT
} SessionOptionType;

/**
 * 加密类型
 */
typedef enum {
    ENCRYPT_TYPE_NONE = 0,
    ENCRYPT_TYPE_AES_GCM_128 = 1,
    ENCRYPT_TYPE_AES_GCM_256 = 2
} EncryptType;

/**
 * 算法类型
 */
typedef enum {
    ALGORITHM_TYPE_AES_GCM_128 = 0,
    ALGORITHM_TYPE_AES_GCM_256 = 1,
    ALGORITHM_TYPE_CHACHA20_POLY1305 = 2
} AlgorithmType;

/**
 * CRC类型
 */
typedef enum {
    CRC_TYPE_NONE = 0,
    CRC_TYPE_CRC32 = 1,
    CRC_TYPE_CRC16 = 2
} CrcType;

/**
 * 压缩类型
 */
typedef enum {
    COMPRESS_TYPE_NONE = 0,
    COMPRESS_TYPE_GZIP = 1,
    COMPRESS_TYPE_LZ4 = 2,
    COMPRESS_TYPE_ZSTD = 3
} CompressType;

#ifdef __cplusplus
}
#endif

#endif // SESSION_H
