# "灵境笔记 (Sentient Notes)" AI系统设计 - 第二部分

## 02. 交互接口：输入/输出模式 (面向SoftBus与用户)

### 2.1 SN_EVENT_STREAM (统一输入对象) 完整设计

#### 2.1.1 JSON Schema定义

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "SN_EVENT_STREAM",
  "description": "灵境笔记统一输入事件流对象",
  "type": "object",
  "required": ["event_id", "timestamp", "device_id", "input_type", "input_data"],
  "properties": {
    "event_id": {
      "type": "string",
      "description": "全局唯一事件标识符，用于事件追踪和去重"
    },
    "timestamp": {
      "type": "integer",
      "description": "事件发生的精确时间戳（微秒级），用于多模态数据同步"
    },
    "device_id": {
      "type": "string",
      "description": "产生事件的设备SoftBus唯一标识"
    },
    "user_id": {
      "type": "string",
      "description": "用户唯一标识符（已脱敏）"
    },
    "input_type": {
      "type": "string",
      "enum": ["AR_GESTURE", "PENCIL_STROKE", "VOICE_COMMAND", "IMAGE_ROI", "TEXT_INPUT", "SPATIAL_INTERACTION", "PHYSICAL_OBJECT", "BIOMETRIC_SIGNAL"],
      "description": "输入模态类型"
    },
    "input_data": {
      "type": "object",
      "description": "根据input_type的具体数据结构",
      "oneOf": [
        {"$ref": "#/definitions/AR_GESTURE_COMMAND"},
        {"$ref": "#/definitions/PENCIL_STROKE_SEQUENCE"},
        {"$ref": "#/definitions/VOICE_COMMAND_WITH_SPATIAL_CONTEXT"},
        {"$ref": "#/definitions/IMAGE_ROI_FOR_AI_PROCESSING"},
        {"$ref": "#/definitions/TEXT_INPUT_WITH_CONTEXT"},
        {"$ref": "#/definitions/SPATIAL_INTERACTION_EVENT"},
        {"$ref": "#/definitions/PHYSICAL_OBJECT_INTERACTION"},
        {"$ref": "#/definitions/BIOMETRIC_SIGNAL_DATA"}
      ]
    },
    "current_xr_context_optional": {
      "type": "object",
      "description": "当前AR/XR环境上下文信息",
      "properties": {
        "head_pose_6dof": {
          "type": "object",
          "properties": {
            "position": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
            "rotation": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4}
          }
        },
        "left_hand_pose_6dof": {"$ref": "#/definitions/pose_6dof"},
        "right_hand_pose_6dof": {"$ref": "#/definitions/pose_6dof"},
        "current_ar_scene_id": {"type": "string"},
        "active_spatial_ui_elements": {
          "type": "array",
          "items": {"type": "string"}
        },
        "environment_lighting": {
          "type": "object",
          "properties": {
            "ambient_light_level": {"type": "number"},
            "dominant_light_direction": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3}
          }
        },
        "acoustic_environment": {
          "type": "object",
          "properties": {
            "noise_level_db": {"type": "number"},
            "reverberation_time": {"type": "number"}
          }
        }
      }
    },
    "available_distributed_ai_optional": {
      "type": "array",
      "description": "当前可用的分布式AI能力列表",
      "items": {
        "type": "object",
        "properties": {
          "capability_name": {"type": "string"},
          "capability_version": {"type": "string"},
          "provider_device_id": {"type": "string"},
          "input_format": {"type": "object"},
          "output_format": {"type": "object"},
          "processing_latency": {
            "type": "object",
            "properties": {
              "best_case_ms": {"type": "number"},
              "average_ms": {"type": "number"},
              "worst_case_ms": {"type": "number"}
            }
          },
          "resource_cost": {
            "type": "object",
            "properties": {
              "cpu_usage_percent": {"type": "number"},
              "memory_mb": {"type": "number"},
              "power_consumption_mw": {"type": "number"}
            }
          },
          "softbus_interface": {
            "type": "object",
            "properties": {
              "protocol": {"type": "string", "enum": ["RPC", "MESSAGE", "STREAM"]},
              "endpoint": {"type": "string"},
              "serialization": {"type": "string", "enum": ["PROTOBUF", "FLATBUFFERS", "JSON"]},
              "qos_requirements": {
                "type": "object",
                "properties": {
                  "reliability": {"type": "string", "enum": ["RELIABLE", "UNRELIABLE"]},
                  "max_latency_ms": {"type": "number"},
                  "min_bandwidth_mbps": {"type": "number"}
                }
              }
            }
          }
        }
      }
    },
    "softbus_transmission_metadata": {
      "type": "object",
      "description": "SoftBus传输相关的元数据",
      "properties": {
        "channel_type": {"type": "string", "enum": ["CoDHC", "CoDC", "MESSAGE_BUS"]},
        "compression_algorithm": {"type": "string", "enum": ["NONE", "GZIP", "LZ4", "ZSTD"]},
        "encryption_enabled": {"type": "boolean"},
        "transmission_priority": {"type": "integer", "minimum": 1, "maximum": 10},
        "expected_data_size_bytes": {"type": "integer"}
      }
    }
  },
  "definitions": {
    "pose_6dof": {
      "type": "object",
      "properties": {
        "position": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
        "rotation": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4}
      }
    },
    "AR_GESTURE_COMMAND": {
      "type": "object",
      "description": "AR手势指令数据结构",
      "properties": {
        "hand_skeleton_sequence": {
          "type": "array",
          "description": "手部骨骼关键点序列",
          "items": {
            "type": "object",
            "properties": {
              "timestamp_us": {"type": "integer"},
              "left_hand_keypoints": {"type": "array", "items": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3}},
              "right_hand_keypoints": {"type": "array", "items": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3}},
              "confidence_scores": {"type": "array", "items": {"type": "number"}}
            }
          }
        },
        "gesture_start_timestamp": {"type": "integer"},
        "gesture_end_timestamp": {"type": "integer"},
        "gesture_3d_space_vector": {
          "type": "object",
          "properties": {
            "start_position": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
            "end_position": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
            "trajectory_points": {"type": "array", "items": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3}}
          }
        },
        "target_object_id": {"type": "string", "description": "手势作用的目标物体ID"},
        "target_spatial_region": {
          "type": "object",
          "properties": {
            "region_type": {"type": "string", "enum": ["SPHERE", "BOX", "PLANE"]},
            "center": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
            "dimensions": {"type": "array", "items": {"type": "number"}}
          }
        },
        "user_gaze_focus": {
          "type": "object",
          "properties": {
            "gaze_direction": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
            "focus_object_id": {"type": "string"},
            "focus_confidence": {"type": "number"}
          }
        },
        "softbus_transmission_info": {
          "type": "object",
          "properties": {
            "data_compression_ratio": {"type": "number"},
            "transmission_latency_ms": {"type": "number"},
            "packet_loss_rate": {"type": "number"}
          }
        }
      }
    },
    "PENCIL_STROKE_SEQUENCE": {
      "type": "object",
      "description": "Pencil笔迹序列数据",
      "properties": {
        "pencil_unique_id": {"type": "string"},
        "stroke_sequence": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "stroke_id": {"type": "string"},
              "sampling_points": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "timestamp_us": {"type": "integer"},
                    "position": {"type": "array", "items": {"type": "number"}, "minItems": 2, "maxItems": 3},
                    "pressure": {"type": "number", "minimum": 0, "maximum": 1},
                    "tilt_x": {"type": "number"},
                    "tilt_y": {"type": "number"},
                    "azimuth": {"type": "number"}
                  }
                }
              },
              "stroke_start_timestamp": {"type": "integer"},
              "stroke_end_timestamp": {"type": "integer"}
            }
          }
        },
        "virtual_ink_properties": {
          "type": "object",
          "properties": {
            "color_rgba": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4},
            "brush_size": {"type": "number"},
            "brush_type": {"type": "string", "enum": ["PEN", "MARKER", "BRUSH", "PENCIL"]}
          }
        },
        "writing_surface": {
          "type": "object",
          "properties": {
            "surface_type": {"type": "string", "enum": ["PHYSICAL_PAPER", "VIRTUAL_AR_CANVAS", "TABLET_SCREEN"]},
            "surface_id": {"type": "string"},
            "surface_material_properties": {
              "type": "object",
              "properties": {
                "texture_roughness": {"type": "number"},
                "friction_coefficient": {"type": "number"}
              }
            }
          }
        },
        "softbus_streaming_config": {
          "type": "object",
          "properties": {
            "sampling_rate_hz": {"type": "number"},
            "data_smoothing_enabled": {"type": "boolean"},
            "real_time_prediction_enabled": {"type": "boolean"}
          }
        }
      }
    },
    "VOICE_COMMAND_WITH_SPATIAL_CONTEXT": {
      "type": "object",
      "description": "带空间上下文的语音指令",
      "properties": {
        "audio_stream": {
          "type": "object",
          "properties": {
            "format": {"type": "string", "enum": ["PCM", "WAV", "MP3", "AAC"]},
            "sample_rate": {"type": "integer"},
            "bit_depth": {"type": "integer"},
            "channels": {"type": "integer"},
            "duration_ms": {"type": "integer"},
            "audio_data_uri": {"type": "string", "description": "SoftBus可访问的音频数据URI"}
          }
        },
        "preliminary_asr_result": {
          "type": "object",
          "properties": {
            "transcription": {"type": "string"},
            "confidence_score": {"type": "number"},
            "language_detected": {"type": "string"},
            "processing_device_id": {"type": "string"}
          }
        },
        "spatial_context": {
          "type": "object",
          "properties": {
            "user_gaze_target": {
              "type": "object",
              "properties": {
                "target_type": {"type": "string", "enum": ["NOTE_ATOM", "PHYSICAL_OBJECT", "SPATIAL_REGION"]},
                "target_id": {"type": "string"},
                "target_position": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3}
              }
            },
            "pointing_gesture": {
              "type": "object",
              "properties": {
                "is_pointing": {"type": "boolean"},
                "pointing_direction": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
                "pointed_object_id": {"type": "string"}
              }
            },
            "nearby_note_atoms": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "atom_id": {"type": "string"},
                  "distance_from_user": {"type": "number"},
                  "relevance_score": {"type": "number"}
                }
              }
            }
          }
        },
        "audio_quality_metrics": {
          "type": "object",
          "properties": {
            "signal_to_noise_ratio": {"type": "number"},
            "background_noise_level": {"type": "number"},
            "speech_clarity_score": {"type": "number"}
          }
        }
      }
    },
    "IMAGE_ROI_FOR_AI_PROCESSING": {
      "type": "object",
      "description": "用于AI处理的图像感兴趣区域",
      "properties": {
        "image_source": {
          "type": "object",
          "properties": {
            "image_uri": {"type": "string", "description": "SoftBus可访问的图像URI"},
            "image_format": {"type": "string", "enum": ["JPEG", "PNG", "RAW", "HEIF"]},
            "resolution": {
              "type": "object",
              "properties": {
                "width": {"type": "integer"},
                "height": {"type": "integer"}
              }
            },
            "capture_timestamp": {"type": "integer"}
          }
        },
        "roi_coordinates": {
          "type": "object",
          "properties": {
            "coordinate_system": {"type": "string", "enum": ["2D_PIXEL", "3D_WORLD"]},
            "bounding_box": {
              "type": "object",
              "properties": {
                "top_left": {"type": "array", "items": {"type": "number"}},
                "bottom_right": {"type": "array", "items": {"type": "number"}}
              }
            },
            "polygon_vertices": {
              "type": "array",
              "items": {"type": "array", "items": {"type": "number"}}
            }
          }
        },
        "camera_parameters": {
          "type": "object",
          "properties": {
            "intrinsic_matrix": {"type": "array", "items": {"type": "number"}},
            "extrinsic_matrix": {"type": "array", "items": {"type": "number"}},
            "distortion_coefficients": {"type": "array", "items": {"type": "number"}},
            "exposure_settings": {
              "type": "object",
              "properties": {
                "iso": {"type": "integer"},
                "shutter_speed": {"type": "number"},
                "aperture": {"type": "number"}
              }
            }
          }
        },
        "requested_ai_processing": {
          "type": "array",
          "items": {
            "type": "string",
            "enum": ["OCR", "OBJECT_DETECTION", "SCENE_UNDERSTANDING", "3D_RECONSTRUCTION", "STYLE_TRANSFER", "CONTENT_ANALYSIS"]
          }
        },
        "processing_priority": {"type": "integer", "minimum": 1, "maximum": 10},
        "quality_requirements": {
          "type": "object",
          "properties": {
            "min_accuracy_threshold": {"type": "number"},
            "max_processing_time_ms": {"type": "integer"},
            "preferred_processing_device": {"type": "string"}
          }
        }
      }
    }
  }
}
```

#### 2.1.2 SN_EVENT_STREAM Few-Shot示例

**示例1：AR智能Cut&Paste场景**
```json
{
  "event_id": "evt_ar_cutpaste_20241128_001",
  "timestamp": 1701158400123456,
  "device_id": "ar_glasses_user001_main",
  "user_id": "user_hash_abc123",
  "input_type": "AR_GESTURE",
  "input_data": {
    "hand_skeleton_sequence": [
      {
        "timestamp_us": 1701158400123456,
        "right_hand_keypoints": [
          [0.15, 0.25, 0.8], [0.16, 0.24, 0.79], [0.17, 0.23, 0.78]
        ],
        "confidence_scores": [0.95, 0.92, 0.89]
      }
    ],
    "gesture_start_timestamp": 1701158400120000,
    "gesture_end_timestamp": 1701158400125000,
    "gesture_3d_space_vector": {
      "start_position": [0.1, 0.2, 0.8],
      "end_position": [0.2, 0.3, 0.8],
      "trajectory_points": [[0.1, 0.2, 0.8], [0.15, 0.25, 0.8], [0.2, 0.3, 0.8]]
    },
    "target_object_id": "physical_document_page_5",
    "target_spatial_region": {
      "region_type": "BOX",
      "center": [0.15, 0.25, 0.8],
      "dimensions": [0.1, 0.05, 0.01]
    },
    "user_gaze_focus": {
      "gaze_direction": [0.0, 0.0, -1.0],
      "focus_object_id": "physical_document_page_5",
      "focus_confidence": 0.87
    }
  },
  "current_xr_context_optional": {
    "head_pose_6dof": {
      "position": [0.0, 1.7, 0.0],
      "rotation": [0.0, 0.0, 0.0, 1.0]
    },
    "current_ar_scene_id": "study_room_scene_001",
    "environment_lighting": {
      "ambient_light_level": 450,
      "dominant_light_direction": [0.3, -0.8, 0.5]
    }
  },
  "available_distributed_ai_optional": [
    {
      "capability_name": "high_accuracy_ocr",
      "capability_version": "v2.1.0",
      "provider_device_id": "edge_server_home_001",
      "processing_latency": {
        "average_ms": 150,
        "worst_case_ms": 300
      },
      "softbus_interface": {
        "protocol": "RPC",
        "serialization": "PROTOBUF",
        "qos_requirements": {
          "reliability": "RELIABLE",
          "max_latency_ms": 500,
          "min_bandwidth_mbps": 10
        }
      }
    }
  ],
  "softbus_transmission_metadata": {
    "channel_type": "CoDHC",
    "compression_algorithm": "LZ4",
    "encryption_enabled": true,
    "transmission_priority": 8,
    "expected_data_size_bytes": 2048
  }
}
```

**示例2：多模态语音+手势融合场景**
```json
{
  "event_id": "evt_multimodal_20241128_002",
  "timestamp": 1701158500234567,
  "device_id": "ar_glasses_user001_main",
  "user_id": "user_hash_abc123",
  "input_type": "VOICE_COMMAND",
  "input_data": {
    "audio_stream": {
      "format": "PCM",
      "sample_rate": 16000,
      "bit_depth": 16,
      "channels": 1,
      "duration_ms": 2500,
      "audio_data_uri": "softbus://audio_stream/temp_voice_001"
    },
    "preliminary_asr_result": {
      "transcription": "把这个红色的笔记移动到那个文件夹里",
      "confidence_score": 0.89,
      "language_detected": "zh-CN",
      "processing_device_id": "ar_glasses_user001_main"
    },
    "spatial_context": {
      "user_gaze_target": {
        "target_type": "NOTE_ATOM",
        "target_id": "note_atom_red_001",
        "target_position": [0.2, 0.3, 0.9]
      },
      "pointing_gesture": {
        "is_pointing": true,
        "pointing_direction": [0.5, 0.2, 0.8],
        "pointed_object_id": "folder_icon_projects"
      },
      "nearby_note_atoms": [
        {
          "atom_id": "note_atom_red_001",
          "distance_from_user": 0.8,
          "relevance_score": 0.95
        },
        {
          "atom_id": "note_atom_blue_002",
          "distance_from_user": 1.2,
          "relevance_score": 0.3
        }
      ]
    },
    "audio_quality_metrics": {
      "signal_to_noise_ratio": 18.5,
      "background_noise_level": 35.2,
      "speech_clarity_score": 0.87
    }
  },
  "softbus_transmission_metadata": {
    "channel_type": "MESSAGE_BUS",
    "compression_algorithm": "GZIP",
    "encryption_enabled": true,
    "transmission_priority": 9,
    "expected_data_size_bytes": 8192
  }
}
```

### 2.2 SN_ACTION_AND_STATE_UPDATE (统一输出对象) 设计

#### 2.2.1 JSON Schema定义

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "SN_ACTION_AND_STATE_UPDATE",
  "description": "灵境笔记统一输出/状态更新对象",
  "type": "object",
  "required": ["update_id", "timestamp", "update_type", "target_devices"],
  "properties": {
    "update_id": {
      "type": "string",
      "description": "更新操作的全局唯一标识符"
    },
    "timestamp": {
      "type": "integer",
      "description": "更新生成的精确时间戳（微秒级）"
    },
    "correlation_event_id": {
      "type": "string",
      "description": "关联的输入事件ID，用于追踪因果关系"
    },
    "update_type": {
      "type": "string",
      "enum": ["NOTE_ATOM_CREATION", "NOTE_ATOM_UPDATE", "LINK_CREATION", "CLUSTER_UPDATE", "AR_VISUALIZATION_UPDATE", "AI_PROCESSING_REQUEST", "SYSTEM_STATE_CHANGE"],
      "description": "更新操作类型"
    },
    "target_devices": {
      "type": "array",
      "items": {"type": "string"},
      "description": "需要接收此更新的目标设备SoftBus ID列表"
    },
    "note_atoms_updates": {
      "type": "array",
      "description": "笔记原子的创建或更新",
      "items": {"$ref": "#/definitions/NoteAtom"}
    },
    "links_and_clusters_updates": {
      "type": "array",
      "description": "链接和聚类的创建或更新",
      "items": {"$ref": "#/definitions/LinkOrCluster"}
    },
    "ar_visualization_commands_optional": {
      "type": "array",
      "description": "AR空间可视化指令集",
      "items": {"$ref": "#/definitions/ARVisualizationCommand"}
    },
    "request_for_distributed_ai_optional": {
      "type": "array",
      "description": "分布式AI处理请求",
      "items": {"$ref": "#/definitions/DistributedAIRequest"}
    },
    "system_notifications": {
      "type": "array",
      "description": "系统级通知和状态变更",
      "items": {"$ref": "#/definitions/SystemNotification"}
    },
    "softbus_distribution_strategy": {
      "type": "object",
      "description": "SoftBus分发策略配置",
      "properties": {
        "distribution_mode": {"type": "string", "enum": ["BROADCAST", "MULTICAST", "UNICAST", "P2P"]},
        "priority_level": {"type": "integer", "minimum": 1, "maximum": 10},
        "reliability_requirement": {"type": "string", "enum": ["RELIABLE", "UNRELIABLE", "BEST_EFFORT"]},
        "max_distribution_latency_ms": {"type": "integer"},
        "compression_enabled": {"type": "boolean"},
        "delta_update_enabled": {"type": "boolean"}
      }
    }
  },
  "definitions": {
    "NoteAtom": {
      "type": "object",
      "description": "笔记原子完整数据结构",
      "properties": {
        "atom_id": {"type": "string"},
        "atom_type": {"type": "string", "enum": ["TEXT", "IMAGE", "AUDIO", "VIDEO", "HANDWRITING", "3D_MODEL", "LINK", "ANNOTATION"]},
        "content": {
          "type": "object",
          "description": "原子内容，根据atom_type变化"
        },
        "metadata": {
          "type": "object",
          "properties": {
            "creation_timestamp": {"type": "integer"},
            "last_modified_timestamp": {"type": "integer"},
            "creator_user_id": {"type": "string"},
            "source_device_id": {"type": "string"},
            "importance_score": {"type": "number", "minimum": 0, "maximum": 1},
            "privacy_level": {"type": "string", "enum": ["PUBLIC", "PRIVATE", "SHARED", "CONFIDENTIAL"]}
          }
        },
        "embedding_vector_multimodal_optional": {
          "type": "array",
          "items": {"type": "number"},
          "description": "多模态语义向量嵌入，用于快速相似度计算"
        },
        "temporal_link_to_raw_source_optional": {
          "type": "object",
          "properties": {
            "source_media_uri": {"type": "string"},
            "start_timestamp_ms": {"type": "integer"},
            "end_timestamp_ms": {"type": "integer"}
          }
        },
        "user_annotation_and_metadata_custom": {
          "type": "object",
          "description": "用户自定义标注和元数据"
        },
        "ai_generated_insights_on_atom": {
          "type": "object",
          "properties": {
            "summary": {"type": "string"},
            "keywords": {"type": "array", "items": {"type": "string"}},
            "related_questions": {"type": "array", "items": {"type": "string"}},
            "potential_connections": {"type": "array", "items": {"type": "string"}},
            "confidence_score": {"type": "number"}
          }
        },
        "spatial_neighborhood_graph_local_optional": {
          "type": "object",
          "properties": {
            "connected_atoms": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "atom_id": {"type": "string"},
                  "relationship_type": {"type": "string"},
                  "spatial_distance": {"type": "number"},
                  "connection_strength": {"type": "number"}
                }
              }
            }
          }
        },
        "version_history_pointer_optional": {
          "type": "object",
          "properties": {
            "current_version": {"type": "string"},
            "version_history_uri": {"type": "string"},
            "branching_info": {"type": "object"}
          }
        },
        "softbus_sync_metadata_optional": {
          "type": "object",
          "properties": {
            "crdt_timestamp": {"type": "integer"},
            "last_modifier_device_id": {"type": "string"},
            "conflict_resolution_status": {"type": "string", "enum": ["RESOLVED", "PENDING", "CONFLICTED"]},
            "sync_vector_clock": {"type": "object"}
          }
        },
        "ar_spatial_properties": {
          "type": "object",
          "properties": {
            "position_3d": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
            "rotation_quaternion": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4},
            "scale_factor": {"type": "number"},
            "anchor_type": {"type": "string", "enum": ["WORLD_LOCKED", "USER_RELATIVE", "OBJECT_ATTACHED"]},
            "visibility_rules": {
              "type": "object",
              "properties": {
                "min_distance": {"type": "number"},
                "max_distance": {"type": "number"},
                "viewing_angle_constraints": {"type": "object"}
              }
            }
          }
        }
      }
    }
  }
}
```
