/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * Discovery Manager - 设备发现管理器实现
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#include "disc_manager.h"
#include "softbus_common.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <errno.h>

#define MAX_DISCOVERY_SESSIONS 32
#define MAX_PUBLISH_SESSIONS 32
#define DISCOVERY_THREAD_STACK_SIZE (64 * 1024)

/* 发现会话信息 */
typedef struct {
    bool isUsed;
    int32_t subscribeId;
    char pkgName[PKG_NAME_SIZE_MAX];
    SubscribeInfo subscribeInfo;
    IDiscoveryCallback callback;
    uint64_t startTime;
    uint32_t deviceFoundCount;
} DiscoverySession;

/* 发布会话信息 */
typedef struct {
    bool isUsed;
    int32_t publishId;
    char pkgName[PKG_NAME_SIZE_MAX];
    PublishInfo publishInfo;
    IPublishCb callback;
    uint64_t startTime;
} PublishSession;

/* 发现管理器上下文 */
typedef struct {
    bool isInitialized;
    bool isRunning;
    pthread_mutex_t mutex;
    pthread_t discoveryThread;
    DiscoverySession discoverySessions[MAX_DISCOVERY_SESSIONS];
    PublishSession publishSessions[MAX_PUBLISH_SESSIONS];
    int32_t nextSubscribeId;
    int32_t nextPublishId;
    DeviceInfo localDevice;
    uint32_t totalDevicesFound;
    uint64_t totalDiscoveryTime;
} DiscoveryManager;

static DiscoveryManager g_discoveryMgr = {0};

/* 获取当前时间戳（毫秒） */
static uint64_t GetCurrentTimeMs(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

/* 生成设备ID */
static void GenerateDeviceId(char* deviceId, uint32_t len) {
    snprintf(deviceId, len, "android_device_%ld", (long)getpid());
}

/* 生成网络ID */
static void GenerateNetworkId(char* networkId, uint32_t len) {
    snprintf(networkId, len, "network_%ld_%llu", (long)getpid(), GetCurrentTimeMs());
}

/* 创建模拟设备信息 */
static void CreateMockDevice(DeviceFoundInfo* device, int32_t index) {
    memset(device, 0, sizeof(DeviceFoundInfo));
    
    snprintf(device->deviceId, sizeof(device->deviceId), "mock_device_%d", index);
    snprintf(device->deviceName, sizeof(device->deviceName), "Mock Device %d", index);
    
    device->deviceTypeId = index % 3; // 0: phone, 1: tablet, 2: tv
    device->capabilityBitmap[0] = CAPABILITY_OSDDATA | CAPABILITY_SHARE;
    device->addrNum = 1;
    
    /* 设置连接地址 */
    device->addr[0].type = CONNECTION_ADDR_WLAN;
    snprintf(device->addr[0].info.ip.ip, sizeof(device->addr[0].info.ip.ip), 
             "192.168.1.%d", 100 + index);
    device->addr[0].info.ip.port = 8080 + index;
    
    device->range = 100 + (index * 10); // 模拟距离
}

/* 查找空闲的发现会话 */
static DiscoverySession* FindFreeDiscoverySession(void) {
    for (int32_t i = 0; i < MAX_DISCOVERY_SESSIONS; i++) {
        if (!g_discoveryMgr.discoverySessions[i].isUsed) {
            return &g_discoveryMgr.discoverySessions[i];
        }
    }
    return NULL;
}

/* 根据订阅ID查找发现会话 */
static DiscoverySession* FindDiscoverySession(int32_t subscribeId) {
    for (int32_t i = 0; i < MAX_DISCOVERY_SESSIONS; i++) {
        if (g_discoveryMgr.discoverySessions[i].isUsed && 
            g_discoveryMgr.discoverySessions[i].subscribeId == subscribeId) {
            return &g_discoveryMgr.discoverySessions[i];
        }
    }
    return NULL;
}

/* 查找空闲的发布会话 */
static PublishSession* FindFreePublishSession(void) {
    for (int32_t i = 0; i < MAX_PUBLISH_SESSIONS; i++) {
        if (!g_discoveryMgr.publishSessions[i].isUsed) {
            return &g_discoveryMgr.publishSessions[i];
        }
    }
    return NULL;
}

/* 根据发布ID查找发布会话 */
static PublishSession* FindPublishSession(int32_t publishId) {
    for (int32_t i = 0; i < MAX_PUBLISH_SESSIONS; i++) {
        if (g_discoveryMgr.publishSessions[i].isUsed && 
            g_discoveryMgr.publishSessions[i].publishId == publishId) {
            return &g_discoveryMgr.publishSessions[i];
        }
    }
    return NULL;
}

/* 设备发现线程 */
static void* DiscoveryThreadFunc(void* arg) {
    (void)arg;
    
    SOFTBUS_LOG_INFO("Discovery thread started");
    
    int32_t deviceIndex = 0;
    
    while (g_discoveryMgr.isRunning) {
        pthread_mutex_lock(&g_discoveryMgr.mutex);
        
        /* 遍历所有活跃的发现会话 */
        for (int32_t i = 0; i < MAX_DISCOVERY_SESSIONS; i++) {
            DiscoverySession* session = &g_discoveryMgr.discoverySessions[i];
            if (!session->isUsed) {
                continue;
            }
            
            /* 模拟设备发现 */
            if (session->subscribeInfo.mode == DISCOVER_MODE_ACTIVE) {
                DeviceFoundInfo device;
                CreateMockDevice(&device, deviceIndex++);
                
                /* 调用发现回调 */
                if (session->callback.OnDeviceFound != NULL) {
                    session->callback.OnDeviceFound(&device);
                }
                
                session->deviceFoundCount++;
                g_discoveryMgr.totalDevicesFound++;
                
                SOFTBUS_LOG_DEBUG("Device found: %s", device.deviceName);
            }
        }
        
        pthread_mutex_unlock(&g_discoveryMgr.mutex);
        
        /* 根据发现频率调整睡眠时间 */
        usleep(2000000); // 2秒
        
        /* 重置设备索引，避免无限增长 */
        if (deviceIndex > 100) {
            deviceIndex = 0;
        }
    }
    
    SOFTBUS_LOG_INFO("Discovery thread stopped");
    return NULL;
}

/* 初始化发现管理器 */
int32_t InitDiscoveryManager(void) {
    if (g_discoveryMgr.isInitialized) {
        return SOFTBUS_OK;
    }
    
    memset(&g_discoveryMgr, 0, sizeof(g_discoveryMgr));
    
    if (pthread_mutex_init(&g_discoveryMgr.mutex, NULL) != 0) {
        SOFTBUS_LOG_ERROR("Failed to initialize discovery mutex");
        return SOFTBUS_ERR;
    }
    
    /* 初始化本地设备信息 */
    GenerateDeviceId(g_discoveryMgr.localDevice.deviceId, 
                    sizeof(g_discoveryMgr.localDevice.deviceId));
    GenerateNetworkId(g_discoveryMgr.localDevice.networkId, 
                     sizeof(g_discoveryMgr.localDevice.networkId));
    
    strncpy(g_discoveryMgr.localDevice.deviceName, "Android SoftBus Device", 
            sizeof(g_discoveryMgr.localDevice.deviceName) - 1);
    strncpy(g_discoveryMgr.localDevice.deviceType, "phone", 
            sizeof(g_discoveryMgr.localDevice.deviceType) - 1);
    
    g_discoveryMgr.localDevice.deviceTypeId = 0; // phone
    g_discoveryMgr.localDevice.isOnline = true;
    g_discoveryMgr.localDevice.timestamp = GetCurrentTimeMs();
    
    g_discoveryMgr.nextSubscribeId = 1;
    g_discoveryMgr.nextPublishId = 1;
    g_discoveryMgr.isInitialized = true;
    
    SOFTBUS_LOG_INFO("Discovery manager initialized");
    return SOFTBUS_OK;
}

/* 反初始化发现管理器 */
void DeinitDiscoveryManager(void) {
    if (!g_discoveryMgr.isInitialized) {
        return;
    }
    
    /* 停止发现线程 */
    if (g_discoveryMgr.isRunning) {
        g_discoveryMgr.isRunning = false;
        pthread_join(g_discoveryMgr.discoveryThread, NULL);
    }
    
    pthread_mutex_destroy(&g_discoveryMgr.mutex);
    
    memset(&g_discoveryMgr, 0, sizeof(g_discoveryMgr));
    
    SOFTBUS_LOG_INFO("Discovery manager deinitialized");
}

/* 启动设备发现 */
int32_t StartDiscovery(const char* pkgName, const SubscribeInfo* subscribeInfo, 
                      const IDiscoveryCallback* cb) {
    if (!g_discoveryMgr.isInitialized) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL || subscribeInfo == NULL || cb == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_discoveryMgr.mutex);
    
    DiscoverySession* session = FindFreeDiscoverySession();
    if (session == NULL) {
        pthread_mutex_unlock(&g_discoveryMgr.mutex);
        SOFTBUS_LOG_ERROR("No free discovery session available");
        return SOFTBUS_ERR;
    }
    
    /* 初始化会话 */
    memset(session, 0, sizeof(DiscoverySession));
    session->isUsed = true;
    session->subscribeId = g_discoveryMgr.nextSubscribeId++;
    strncpy(session->pkgName, pkgName, sizeof(session->pkgName) - 1);
    memcpy(&session->subscribeInfo, subscribeInfo, sizeof(SubscribeInfo));
    memcpy(&session->callback, cb, sizeof(IDiscoveryCallback));
    session->startTime = GetCurrentTimeMs();
    
    int32_t subscribeId = session->subscribeId;
    
    /* 启动发现线程（如果还未启动） */
    if (!g_discoveryMgr.isRunning) {
        g_discoveryMgr.isRunning = true;
        if (pthread_create(&g_discoveryMgr.discoveryThread, NULL, 
                          DiscoveryThreadFunc, NULL) != 0) {
            session->isUsed = false;
            g_discoveryMgr.isRunning = false;
            pthread_mutex_unlock(&g_discoveryMgr.mutex);
            SOFTBUS_LOG_ERROR("Failed to create discovery thread");
            return SOFTBUS_ERR;
        }
    }
    
    pthread_mutex_unlock(&g_discoveryMgr.mutex);
    
    /* 调用成功回调 */
    if (cb->OnDiscoverySuccess != NULL) {
        cb->OnDiscoverySuccess(subscribeId);
    }
    
    SOFTBUS_LOG_INFO("Discovery started: subscribeId=%d, pkgName=%s", 
                     subscribeId, pkgName);
    
    return subscribeId;
}

/* 停止设备发现 */
int32_t StopDiscovery(const char* pkgName, int32_t subscribeId) {
    if (!g_discoveryMgr.isInitialized) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_discoveryMgr.mutex);
    
    DiscoverySession* session = FindDiscoverySession(subscribeId);
    if (session == NULL || strcmp(session->pkgName, pkgName) != 0) {
        pthread_mutex_unlock(&g_discoveryMgr.mutex);
        SOFTBUS_LOG_ERROR("Discovery session not found: subscribeId=%d", subscribeId);
        return SOFTBUS_ERR;
    }
    
    /* 清理会话 */
    uint64_t duration = GetCurrentTimeMs() - session->startTime;
    g_discoveryMgr.totalDiscoveryTime += duration;
    
    SOFTBUS_LOG_INFO("Discovery stopped: subscribeId=%d, duration=%llu ms, devices=%u", 
                     subscribeId, duration, session->deviceFoundCount);
    
    memset(session, 0, sizeof(DiscoverySession));
    
    pthread_mutex_unlock(&g_discoveryMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 发布服务 */
int32_t PublishLNN(const char* pkgName, const PublishInfo* publishInfo, 
                   const IPublishCb* cb) {
    if (!g_discoveryMgr.isInitialized) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL || publishInfo == NULL || cb == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_discoveryMgr.mutex);
    
    PublishSession* session = FindFreePublishSession();
    if (session == NULL) {
        pthread_mutex_unlock(&g_discoveryMgr.mutex);
        SOFTBUS_LOG_ERROR("No free publish session available");
        return SOFTBUS_ERR;
    }
    
    /* 初始化会话 */
    memset(session, 0, sizeof(PublishSession));
    session->isUsed = true;
    session->publishId = g_discoveryMgr.nextPublishId++;
    strncpy(session->pkgName, pkgName, sizeof(session->pkgName) - 1);
    memcpy(&session->publishInfo, publishInfo, sizeof(PublishInfo));
    memcpy(&session->callback, cb, sizeof(IPublishCb));
    session->startTime = GetCurrentTimeMs();
    
    int32_t publishId = session->publishId;
    
    pthread_mutex_unlock(&g_discoveryMgr.mutex);
    
    /* 调用成功回调 */
    if (cb->OnPublishResult != NULL) {
        cb->OnPublishResult(publishId, PUBLISH_LNN_SUCCESS);
    }
    
    SOFTBUS_LOG_INFO("Service published: publishId=%d, pkgName=%s", 
                     publishId, pkgName);
    
    return publishId;
}

/* 停止发布服务 */
int32_t StopPublishLNN(const char* pkgName, int32_t publishId) {
    if (!g_discoveryMgr.isInitialized) {
        return SOFTBUS_NO_INIT;
    }
    
    if (pkgName == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_discoveryMgr.mutex);
    
    PublishSession* session = FindPublishSession(publishId);
    if (session == NULL || strcmp(session->pkgName, pkgName) != 0) {
        pthread_mutex_unlock(&g_discoveryMgr.mutex);
        SOFTBUS_LOG_ERROR("Publish session not found: publishId=%d", publishId);
        return SOFTBUS_ERR;
    }
    
    /* 清理会话 */
    uint64_t duration = GetCurrentTimeMs() - session->startTime;
    
    SOFTBUS_LOG_INFO("Service unpublished: publishId=%d, duration=%llu ms", 
                     publishId, duration);
    
    memset(session, 0, sizeof(PublishSession));
    
    pthread_mutex_unlock(&g_discoveryMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 获取本地设备信息 */
int32_t GetLocalDeviceInfo(DeviceInfo* deviceInfo) {
    if (!g_discoveryMgr.isInitialized || deviceInfo == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_discoveryMgr.mutex);
    memcpy(deviceInfo, &g_discoveryMgr.localDevice, sizeof(DeviceInfo));
    pthread_mutex_unlock(&g_discoveryMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 获取发现统计信息 */
int32_t GetDiscoveryStatistics(DiscoveryStatistics* stats) {
    if (!g_discoveryMgr.isInitialized || stats == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_discoveryMgr.mutex);
    
    memset(stats, 0, sizeof(DiscoveryStatistics));
    stats->totalDevicesFound = g_discoveryMgr.totalDevicesFound;
    stats->totalDiscoveryTime = g_discoveryMgr.totalDiscoveryTime;
    
    /* 统计活跃会话 */
    for (int32_t i = 0; i < MAX_DISCOVERY_SESSIONS; i++) {
        if (g_discoveryMgr.discoverySessions[i].isUsed) {
            stats->activeDiscoverySessions++;
        }
    }
    
    for (int32_t i = 0; i < MAX_PUBLISH_SESSIONS; i++) {
        if (g_discoveryMgr.publishSessions[i].isUsed) {
            stats->activePublishSessions++;
        }
    }
    
    pthread_mutex_unlock(&g_discoveryMgr.mutex);
    
    return SOFTBUS_OK;
}
