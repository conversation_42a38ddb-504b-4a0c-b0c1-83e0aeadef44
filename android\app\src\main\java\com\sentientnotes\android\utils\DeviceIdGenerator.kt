package com.sentientnotes.android.utils

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.provider.Settings
import timber.log.Timber
import java.security.MessageDigest
import java.util.*

/**
 * 设备ID生成器
 * 生成唯一且持久的设备标识符
 */
object DeviceIdGenerator {
    
    private const val PREFS_NAME = "sentient_notes_device"
    private const val KEY_DEVICE_ID = "device_id"
    private const val KEY_DEVICE_ID_VERSION = "device_id_version"
    private const val CURRENT_VERSION = 1

    /**
     * 生成或获取设备ID
     */
    fun generateDeviceId(context: Context): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        // 检查是否已有设备ID且版本匹配
        val existingId = prefs.getString(KEY_DEVICE_ID, null)
        val existingVersion = prefs.getInt(KEY_DEVICE_ID_VERSION, 0)
        
        if (existingId != null && existingVersion == CURRENT_VERSION) {
            Timber.d("使用现有设备ID: $existingId")
            return existingId
        }
        
        // 生成新的设备ID
        val newDeviceId = createDeviceId(context)
        
        // 保存到SharedPreferences
        prefs.edit()
            .putString(KEY_DEVICE_ID, newDeviceId)
            .putInt(KEY_DEVICE_ID_VERSION, CURRENT_VERSION)
            .apply()
        
        Timber.i("生成新设备ID: $newDeviceId")
        return newDeviceId
    }

    /**
     * 创建设备ID
     */
    private fun createDeviceId(context: Context): String {
        val components = mutableListOf<String>()
        
        try {
            // 1. Android ID (最稳定的标识符)
            val androidId = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ANDROID_ID
            )
            if (!androidId.isNullOrEmpty() && androidId != "9774d56d682e549c") {
                components.add("aid:$androidId")
            }
            
            // 2. 设备硬件信息
            components.add("mfg:${Build.MANUFACTURER}")
            components.add("mdl:${Build.MODEL}")
            components.add("brd:${Build.BOARD}")
            
            // 3. 系统信息
            components.add("sdk:${Build.VERSION.SDK_INT}")
            components.add("abi:${Build.SUPPORTED_ABIS.firstOrNull() ?: "unknown"}")
            
            // 4. 应用包名
            components.add("pkg:${context.packageName}")
            
            // 5. 时间戳（确保唯一性）
            components.add("ts:${System.currentTimeMillis()}")
            
            // 6. 随机数（额外的唯一性保证）
            components.add("rnd:${UUID.randomUUID().toString().take(8)}")
            
        } catch (e: Exception) {
            Timber.e(e, "获取设备信息时发生错误")
            // 如果获取设备信息失败，使用随机UUID
            return "fallback:${UUID.randomUUID()}"
        }
        
        // 将所有组件连接并生成哈希
        val combinedString = components.joinToString("|")
        val hash = sha256(combinedString)
        
        // 格式化为易读的设备ID
        return "sn_${hash.take(16)}"
    }

    /**
     * 计算SHA-256哈希
     */
    private fun sha256(input: String): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(input.toByteArray())
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Timber.e(e, "计算SHA-256哈希失败")
            // 如果哈希计算失败，使用简单的字符串哈希
            input.hashCode().toString()
        }
    }

    /**
     * 重置设备ID（用于调试或重置）
     */
    fun resetDeviceId(context: Context): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().clear().apply()
        
        val newDeviceId = generateDeviceId(context)
        Timber.i("设备ID已重置: $newDeviceId")
        return newDeviceId
    }

    /**
     * 获取设备信息摘要
     */
    fun getDeviceInfo(context: Context): Map<String, String> {
        return mapOf(
            "device_id" to generateDeviceId(context),
            "manufacturer" to Build.MANUFACTURER,
            "model" to Build.MODEL,
            "android_version" to Build.VERSION.RELEASE,
            "api_level" to Build.VERSION.SDK_INT.toString(),
            "board" to Build.BOARD,
            "brand" to Build.BRAND,
            "device" to Build.DEVICE,
            "hardware" to Build.HARDWARE,
            "product" to Build.PRODUCT,
            "supported_abis" to Build.SUPPORTED_ABIS.joinToString(","),
            "package_name" to context.packageName
        )
    }

    /**
     * 验证设备ID格式
     */
    fun isValidDeviceId(deviceId: String): Boolean {
        return deviceId.matches(Regex("^sn_[a-f0-9]{16}$")) || 
               deviceId.startsWith("fallback:")
    }

    /**
     * 生成短设备ID（用于显示）
     */
    fun getShortDeviceId(context: Context): String {
        val fullId = generateDeviceId(context)
        return if (fullId.startsWith("sn_")) {
            fullId.takeLast(8).uppercase()
        } else {
            fullId.take(8).uppercase()
        }
    }

    /**
     * 获取设备类型
     */
    fun getDeviceType(): String {
        return when {
            isTablet() -> "tablet"
            isTV() -> "tv"
            isWearable() -> "wearable"
            isAutomotive() -> "automotive"
            else -> "smartphone"
        }
    }

    /**
     * 判断是否为平板
     */
    private fun isTablet(): Boolean {
        return try {
            val screenSize = android.content.res.Resources.getSystem()
                .configuration.screenLayout and 
                android.content.res.Configuration.SCREENLAYOUT_SIZE_MASK
            
            screenSize >= android.content.res.Configuration.SCREENLAYOUT_SIZE_LARGE
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 判断是否为TV
     */
    private fun isTV(): Boolean {
        return Build.MODEL.contains("TV", ignoreCase = true) ||
               Build.MANUFACTURER.contains("Android TV", ignoreCase = true)
    }

    /**
     * 判断是否为可穿戴设备
     */
    private fun isWearable(): Boolean {
        return Build.MODEL.contains("Watch", ignoreCase = true) ||
               Build.MANUFACTURER.contains("Wear", ignoreCase = true)
    }

    /**
     * 判断是否为车载设备
     */
    private fun isAutomotive(): Boolean {
        return Build.MODEL.contains("Auto", ignoreCase = true) ||
               Build.MANUFACTURER.contains("Automotive", ignoreCase = true)
    }

    /**
     * 生成设备指纹（用于设备识别）
     */
    fun generateDeviceFingerprint(context: Context): String {
        val components = listOf(
            Build.MANUFACTURER,
            Build.MODEL,
            Build.DEVICE,
            Build.PRODUCT,
            Build.HARDWARE,
            Build.BOARD,
            Build.VERSION.RELEASE,
            Build.VERSION.SDK_INT.toString()
        )
        
        val fingerprint = components.joinToString("|")
        return sha256(fingerprint).take(32)
    }
}
