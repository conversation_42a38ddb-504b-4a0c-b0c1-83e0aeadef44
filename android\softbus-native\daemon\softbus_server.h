/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * SoftBus Server Core - 服务器核心功能定义
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#ifndef SOFTBUS_SERVER_H
#define SOFTBUS_SERVER_H

#include <stdint.h>
#include <stdbool.h>
#include <pthread.h>
#include "softbus_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 客户端信息结构 */
typedef struct {
    int32_t socket;
    char pkgName[PKG_NAME_SIZE_MAX];
    pid_t pid;
    uid_t uid;
    bool isAuthenticated;
    uint64_t connectTime;
    uint64_t lastActiveTime;
} ClientInfo;

/* 服务器统计信息 */
typedef struct {
    uint32_t totalClients;
    uint32_t activeClients;
    uint64_t totalRequests;
    uint64_t totalErrors;
    uint64_t uptime;
    uint64_t totalBytesReceived;
    uint64_t totalBytesSent;
} ServerStatistics;

/* 请求类型枚举 */
typedef enum {
    REQUEST_TYPE_DISCOVERY_START = 1,
    REQUEST_TYPE_DISCOVERY_STOP = 2,
    REQUEST_TYPE_PUBLISH_START = 3,
    REQUEST_TYPE_PUBLISH_STOP = 4,
    REQUEST_TYPE_SESSION_CREATE = 5,
    REQUEST_TYPE_SESSION_REMOVE = 6,
    REQUEST_TYPE_SESSION_OPEN = 7,
    REQUEST_TYPE_SESSION_CLOSE = 8,
    REQUEST_TYPE_SESSION_SEND = 9,
    REQUEST_TYPE_JOIN_LNN = 10,
    REQUEST_TYPE_LEAVE_LNN = 11,
    REQUEST_TYPE_GET_NODE_INFO = 12,
    REQUEST_TYPE_REG_NODE_STATE = 13,
    REQUEST_TYPE_UNREG_NODE_STATE = 14,
    REQUEST_TYPE_TIME_SYNC = 15,
    REQUEST_TYPE_AUTH_START = 16,
    REQUEST_TYPE_AUTH_STOP = 17,
    REQUEST_TYPE_HEARTBEAT = 18,
    REQUEST_TYPE_GET_STATISTICS = 19,
    REQUEST_TYPE_BUTT
} RequestType;

/* 响应类型枚举 */
typedef enum {
    RESPONSE_TYPE_SUCCESS = 0,
    RESPONSE_TYPE_ERROR = 1,
    RESPONSE_TYPE_EVENT = 2,
    RESPONSE_TYPE_DATA = 3,
    RESPONSE_TYPE_BUTT
} ResponseType;

/* 请求消息结构 */
typedef struct {
    uint32_t magic;
    uint32_t version;
    RequestType type;
    uint32_t dataLen;
    uint32_t checksum;
    char data[0];
} RequestMessage;

/* 响应消息结构 */
typedef struct {
    uint32_t magic;
    uint32_t version;
    ResponseType type;
    int32_t result;
    uint32_t dataLen;
    uint32_t checksum;
    char data[0];
} ResponseMessage;

/* 事件消息结构 */
typedef struct {
    uint32_t eventType;
    uint32_t dataLen;
    char data[0];
} EventMessage;

/* 消息魔数 */
#define SOFTBUS_MESSAGE_MAGIC 0x534F4654  // "SOFT"

/* 协议版本 */
#define SOFTBUS_PROTOCOL_VERSION 0x00010000  // 1.0.0

/**
 * 初始化传输层
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t InitTransport(void);

/**
 * 反初始化传输层
 */
void DeinitTransport(void);

/**
 * 初始化认证模块
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t InitAuth(void);

/**
 * 反初始化认证模块
 */
void DeinitAuth(void);

/**
 * 初始化连接管理
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t InitConnection(void);

/**
 * 反初始化连接管理
 */
void DeinitConnection(void);

/**
 * 处理客户端请求
 * @param clientSocket 客户端套接字
 * @param data 请求数据
 * @param dataLen 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ProcessClientRequest(int32_t clientSocket, const char* data, uint32_t dataLen);

/**
 * 发送响应消息
 * @param clientSocket 客户端套接字
 * @param type 响应类型
 * @param result 结果码
 * @param data 响应数据
 * @param dataLen 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SendResponse(int32_t clientSocket, ResponseType type, int32_t result, 
                    const void* data, uint32_t dataLen);

/**
 * 发送事件消息
 * @param clientSocket 客户端套接字
 * @param eventType 事件类型
 * @param data 事件数据
 * @param dataLen 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t SendEvent(int32_t clientSocket, uint32_t eventType, 
                 const void* data, uint32_t dataLen);

/**
 * 广播事件消息
 * @param eventType 事件类型
 * @param data 事件数据
 * @param dataLen 数据长度
 * @param excludeSocket 排除的套接字
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t BroadcastEvent(uint32_t eventType, const void* data, uint32_t dataLen, 
                      int32_t excludeSocket);

/**
 * 注册客户端
 * @param clientSocket 客户端套接字
 * @param pkgName 包名
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t RegisterClient(int32_t clientSocket, const char* pkgName);

/**
 * 注销客户端
 * @param clientSocket 客户端套接字
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t UnregisterClient(int32_t clientSocket);

/**
 * 获取客户端信息
 * @param clientSocket 客户端套接字
 * @return 客户端信息指针，NULL表示未找到
 */
ClientInfo* GetClientInfo(int32_t clientSocket);

/**
 * 验证客户端权限
 * @param clientSocket 客户端套接字
 * @param permission 权限名称
 * @return true 有权限，false 无权限
 */
bool CheckClientPermission(int32_t clientSocket, const char* permission);

/**
 * 获取服务器统计信息
 * @param stats 统计信息结构
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t GetServerStatistics(ServerStatistics* stats);

/**
 * 重置服务器统计信息
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ResetServerStatistics(void);

/**
 * 计算校验和
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验和
 */
uint32_t CalculateChecksum(const void* data, uint32_t len);

/**
 * 验证消息完整性
 * @param message 消息指针
 * @param messageLen 消息长度
 * @return true 验证通过，false 验证失败
 */
bool ValidateMessage(const void* message, uint32_t messageLen);

/**
 * 序列化设备信息
 * @param device 设备信息
 * @param buffer 缓冲区
 * @param bufferLen 缓冲区长度
 * @return 序列化后的长度，负数表示失败
 */
int32_t SerializeDeviceInfo(const DeviceInfo* device, char* buffer, uint32_t bufferLen);

/**
 * 反序列化设备信息
 * @param buffer 缓冲区
 * @param bufferLen 缓冲区长度
 * @param device 设备信息
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t DeserializeDeviceInfo(const char* buffer, uint32_t bufferLen, DeviceInfo* device);

/**
 * 序列化会话信息
 * @param sessionId 会话ID
 * @param buffer 缓冲区
 * @param bufferLen 缓冲区长度
 * @return 序列化后的长度，负数表示失败
 */
int32_t SerializeSessionInfo(int32_t sessionId, char* buffer, uint32_t bufferLen);

/**
 * 处理发现请求
 * @param clientSocket 客户端套接字
 * @param data 请求数据
 * @param dataLen 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ProcessDiscoveryRequest(int32_t clientSocket, const char* data, uint32_t dataLen);

/**
 * 处理发布请求
 * @param clientSocket 客户端套接字
 * @param data 请求数据
 * @param dataLen 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ProcessPublishRequest(int32_t clientSocket, const char* data, uint32_t dataLen);

/**
 * 处理会话请求
 * @param clientSocket 客户端套接字
 * @param data 请求数据
 * @param dataLen 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ProcessSessionRequest(int32_t clientSocket, const char* data, uint32_t dataLen);

/**
 * 处理LNN请求
 * @param clientSocket 客户端套接字
 * @param data 请求数据
 * @param dataLen 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ProcessLnnRequest(int32_t clientSocket, const char* data, uint32_t dataLen);

/**
 * 处理认证请求
 * @param clientSocket 客户端套接字
 * @param data 请求数据
 * @param dataLen 数据长度
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t ProcessAuthRequest(int32_t clientSocket, const char* data, uint32_t dataLen);

/**
 * 启动心跳检测
 * @return SOFTBUS_OK 成功，其他值失败
 */
int32_t StartHeartbeat(void);

/**
 * 停止心跳检测
 */
void StopHeartbeat(void);

/**
 * 清理过期客户端
 * @param timeoutMs 超时时间（毫秒）
 * @return 清理的客户端数量
 */
int32_t CleanupExpiredClients(uint32_t timeoutMs);

#ifdef __cplusplus
}
#endif

#endif // SOFTBUS_SERVER_H
