# 灵境笔记 (Sentient Notes)

基于OpenHarmony SoftBus的下一代AI驱动空间化笔记系统

## 项目概述

灵境笔记是一个创新的笔记应用，通过AR/XR技术和分布式AI能力，实现10倍效率提升的智能笔记体验。系统深度集成OpenHarmony SoftBus，提供无缝的多设备协同和分布式AI处理能力。

### 核心特性

- 🚀 **超流体信息捕捉**：从传统6-8步操作简化为0-1步的无感交互
- 🌐 **空间化知识图谱**：AR环境中的3D知识可视化和空间化组织
- 🧠 **智能涌现系统**：AI驱动的深度洞察、关联发现和创意催化
- 🤝 **无缝协同创作**：基于SoftBus的实时多用户协同编辑
- 📱 **分布式AI架构**：跨设备的智能任务分解和负载均衡

## 技术架构

### 服务端 (Python)
- **框架**: FastAPI + WebSocket
- **AI引擎**: PyTorch + Transformers + MediaPipe
- **数据库**: Neo4j (图数据库) + Milvus (向量数据库) + Redis (缓存)
- **核心模块**:
  - MIFAE: 多模态输入融合和原子化引擎
  - SKG-Engine: 空间知识图谱引擎
  - AIE2: AI洞察与涌现引擎
  - XR-ISM: XR交互和空间UI管理器
  - DCS-Engine: 分布式协同和同步引擎
  - DACO: 分布式AI能力协调器

### Android客户端
- **框架**: Kotlin + Jetpack Compose + Hilt
- **AR支持**: ARCore + MediaPipe
- **网络**: WebSocket (模拟SoftBus) + Retrofit
- **数据库**: Room + DataStore
- **核心服务**:
  - SoftBusService: 设备发现和连接管理
  - MultimodalInputService: 多模态输入处理
  - AIProcessingService: 本地AI处理

## 快速开始

### 环境要求

**服务端**:
- Python 3.9+
- CUDA 11.8+ (可选，用于GPU加速)
- Neo4j 5.0+
- Redis 6.0+
- Milvus 2.3+

**Android客户端**:
- Android Studio Hedgehog | 2023.1.1+
- Android SDK 26+ (目标SDK 34)
- 支持ARCore的Android设备

### 服务端部署

1. **克隆项目**
```bash
git clone https://github.com/your-org/sentient-notes.git
cd sentient-notes/server
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等
```

4. **启动数据库服务**
```bash
# 启动Neo4j
neo4j start

# 启动Redis
redis-server

# 启动Milvus
docker-compose -f docker-compose-milvus.yml up -d
```

5. **启动服务端**
```bash
python main.py
```

服务端将在 `http://localhost:8000` 启动，WebSocket端点为 `ws://localhost:8000/ws/{device_id}`

### Android客户端构建

1. **打开Android Studio**
```bash
cd sentient-notes/android
# 使用Android Studio打开项目
```

2. **配置服务端地址**
在 `app/src/main/java/com/sentientnotes/android/utils/Constants.kt` 中配置服务端地址：
```kotlin
const val SERVER_BASE_URL = "http://your-server-ip:8000"
const val WEBSOCKET_URL = "ws://your-server-ip:8000/ws"
```

3. **构建和安装**
```bash
./gradlew assembleDebug
adb install app/build/outputs/apk/debug/app-debug.apk
```

## API文档

### WebSocket事件流

**输入事件流 (SN_EVENT_STREAM)**:
```json
{
  "event_id": "evt_001",
  "timestamp": 1701158400123456,
  "device_id": "sn_abc123def456",
  "user_id": "user_hash_xyz",
  "input_type": "AR_GESTURE",
  "input_data": {
    // 根据input_type的具体数据结构
  },
  "current_xr_context_optional": {
    // AR/XR环境上下文
  }
}
```

**输出状态更新 (SN_ACTION_AND_STATE_UPDATE)**:
```json
{
  "update_id": "upd_001",
  "timestamp": 1701158400123456,
  "update_type": "NOTE_ATOM_CREATION",
  "target_devices": ["sn_abc123def456"],
  "note_atoms_updates": [
    // 笔记原子更新列表
  ],
  "ar_visualization_commands_optional": [
    // AR可视化指令
  ]
}
```

### REST API端点

- `GET /api/v1/health` - 健康检查
- `POST /api/v1/events/process` - 处理事件流
- `GET /api/v1/knowledge-graph/atoms` - 获取笔记原子
- `GET /api/v1/knowledge-graph/search` - 搜索笔记
- `POST /api/v1/ai/insights` - 生成AI洞察

详细API文档请访问：`http://localhost:8000/docs`

## 核心功能演示

### 1. AR智能手势OCR
```kotlin
// Android端手势识别
val gestureData = ARGestureCommand(
    handSkeletonSequence = handKeypoints,
    gestureStartTimestamp = startTime,
    gestureEndTimestamp = endTime,
    gesture3dSpaceVector = trajectory,
    targetObjectId = "document_page_1"
)

// 发送到服务端处理
softBusService.sendEventStream(
    SNEventStream(
        eventId = UUID.randomUUID().toString(),
        deviceId = deviceId,
        inputType = InputType.AR_GESTURE,
        inputData = gestureData
    )
)
```

### 2. 多模态融合处理
```python
# 服务端多模态融合
async def process_multimodal_input(self, event_stream: SN_EVENT_STREAM):
    # 1. 路由到相应处理器
    atoms = await self.mifae.process_input_stream(event_stream)
    
    # 2. 添加到知识图谱
    await self.skg_engine.add_note_atoms(atoms)
    
    # 3. 生成AI洞察
    insights = await self.aie2.analyze_new_atoms(atoms, event_stream)
    
    # 4. 生成AR可视化
    ar_commands = await self.xr_ism.generate_visualization_commands(atoms)
    
    return SN_ACTION_AND_STATE_UPDATE(...)
```

### 3. 分布式AI任务调度
```python
# AI任务分解和分布式执行
class DistributedAITaskScheduler:
    async def schedule_ai_task(self, ai_request: AIRequest):
        # 任务分解
        subtasks = self.task_decomposer.decompose(ai_request)
        
        # 能力匹配
        assignments = self.capability_matcher.match(subtasks, available_capabilities)
        
        # 负载均衡
        optimized_assignments = self.load_balancer.optimize(assignments)
        
        # 分布式执行
        return await self.execute_distributed_tasks(optimized_assignments)
```

## 性能指标

### 延迟指标
- SoftBus P2P传输：<10ms
- 多模态融合处理：<50ms
- AI洞察生成：<200ms
- 多用户状态同步：<50ms
- AR渲染更新：<20ms

### 准确率指标
- 手势识别准确率：>90%
- 多模态融合准确率：>85%
- 知识关联推荐准确率：>80%
- OCR识别准确率：>95%
- 语音识别准确率：>92%

### 效率提升指标
- 学生文献综述：5倍效率提升
- 设计师灵感收集：10倍效率提升
- 团队头脑风暴：2.1倍效率提升

## 开发路线图

### 阶段一：基础MVP (2024年12月 - 2025年3月)
- [x] SoftBus集成与基础通信
- [x] 基础AI模块实现
- [x] 简单AR界面
- [ ] 单用户AR笔记功能

### 阶段二：核心功能 (2025年4月 - 2025年7月)
- [ ] 多模态融合引擎
- [ ] 知识图谱引擎
- [ ] 智能推荐系统
- [ ] 用户效率提升>3倍

### 阶段三：协同与优化 (2025年8月 - 2025年11月)
- [ ] 多用户协同系统
- [ ] 性能优化
- [ ] 支持10+设备协同

### 阶段四：商业化 (2025年12月 - 2026年3月)
- [ ] 企业级功能
- [ ] 开放平台
- [ ] 全球发布

## 贡献指南

我们欢迎社区贡献！请阅读 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细的贡献指南。

### 开发环境设置
1. Fork项目
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add amazing feature'`
4. 推送分支：`git push origin feature/amazing-feature`
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 联系我们

- 项目主页：https://github.com/your-org/sentient-notes
- 问题反馈：https://github.com/your-org/sentient-notes/issues
- 邮箱：<EMAIL>

## 致谢

感谢以下开源项目和技术：
- OpenHarmony SoftBus
- Google ARCore & MediaPipe
- PyTorch & Transformers
- Neo4j & Milvus
- Android Jetpack Compose

---

**灵境笔记** - 让思维在空间中自由流动 🚀
