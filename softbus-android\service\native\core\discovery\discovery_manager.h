/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef DISCOVERY_MANAGER_H
#define DISCOVERY_MANAGER_H

#include <memory>
#include <vector>
#include <map>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <functional>

#include "softbus_core.h"

namespace android {

// 前向声明
class WifiDiscovery;
class BluetoothDiscovery;
class BleDiscovery;
class UsbDiscovery;

// 发现会话信息
struct DiscoverySession {
    int discoveryId;
    DiscoveryConfig config;
    std::chrono::steady_clock::time_point startTime;
    std::atomic<bool> isActive;
    uint32_t deviceFoundCount;
    
    DiscoverySession(int id, const DiscoveryConfig& cfg) 
        : discoveryId(id), config(cfg), startTime(std::chrono::steady_clock::now()),
          isActive(true), deviceFoundCount(0) {}
};

// 发布会话信息
struct PublishSession {
    int publishId;
    std::string capability;
    std::chrono::steady_clock::time_point startTime;
    std::atomic<bool> isActive;
    
    PublishSession(int id, const std::string& cap)
        : publishId(id), capability(cap), startTime(std::chrono::steady_clock::now()),
          isActive(true) {}
};

// 发现统计信息
struct DiscoveryStatistics {
    uint32_t totalDiscoveryCount;
    uint32_t activeDiscoveryCount;
    uint32_t totalDevicesFound;
    uint32_t totalPublishCount;
    uint32_t activePublishCount;
    uint64_t totalDiscoveryTime; // 毫秒
    
    DiscoveryStatistics() {
        totalDiscoveryCount = 0;
        activeDiscoveryCount = 0;
        totalDevicesFound = 0;
        totalPublishCount = 0;
        activePublishCount = 0;
        totalDiscoveryTime = 0;
    }
};

/**
 * 设备发现管理器
 * 
 * 负责管理多种协议的设备发现，包括WiFi、蓝牙、BLE、USB等。
 * 提供统一的设备发现接口和能力发布功能。
 */
class DiscoveryManager {
public:
    DiscoveryManager();
    ~DiscoveryManager();
    
    // 禁用拷贝构造和赋值
    DiscoveryManager(const DiscoveryManager&) = delete;
    DiscoveryManager& operator=(const DiscoveryManager&) = delete;
    
    /**
     * 初始化发现管理器
     * 
     * @param callback 设备发现回调
     * @return true 成功，false 失败
     */
    bool Initialize(const DeviceFoundCallback& callback);
    
    /**
     * 反初始化发现管理器
     */
    void Deinitialize();
    
    /**
     * 启动发现管理器
     * 
     * @return true 成功，false 失败
     */
    bool Start();
    
    /**
     * 停止发现管理器
     */
    void Stop();
    
    /**
     * 检查是否已初始化
     * 
     * @return true 已初始化，false 未初始化
     */
    bool IsInitialized() const { return mInitialized; }
    
    /**
     * 检查是否正在运行
     * 
     * @return true 正在运行，false 已停止
     */
    bool IsRunning() const { return mRunning; }
    
    // ==================== 设备发现接口 ====================
    
    /**
     * 开始设备发现
     * 
     * @param config 发现配置
     * @return 发现ID，负数表示失败
     */
    int StartDiscovery(const DiscoveryConfig& config);
    
    /**
     * 停止设备发现
     * 
     * @param discoveryId 发现ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int StopDiscovery(int discoveryId);
    
    /**
     * 停止所有发现
     * 
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int StopAllDiscovery();
    
    /**
     * 获取活跃的发现会话
     * 
     * @return 发现会话列表
     */
    std::vector<DiscoverySession> GetActiveDiscoverySessions() const;
    
    // ==================== 服务发布接口 ====================
    
    /**
     * 发布服务能力
     * 
     * @param capability 服务能力
     * @return 发布ID，负数表示失败
     */
    int PublishService(const std::string& capability);
    
    /**
     * 停止发布服务
     * 
     * @param publishId 发布ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int StopPublishService(int publishId);
    
    /**
     * 停止所有发布
     * 
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int StopAllPublish();
    
    /**
     * 获取活跃的发布会话
     * 
     * @return 发布会话列表
     */
    std::vector<PublishSession> GetActivePublishSessions() const;
    
    // ==================== 设备管理接口 ====================
    
    /**
     * 获取发现的设备列表
     * 
     * @return 设备信息列表
     */
    std::vector<DeviceInfo> GetDiscoveredDevices() const;
    
    /**
     * 获取指定设备信息
     * 
     * @param deviceId 设备ID
     * @return 设备信息，如果设备不存在则返回空的DeviceInfo
     */
    DeviceInfo GetDeviceInfo(const std::string& deviceId) const;
    
    /**
     * 清除设备缓存
     */
    void ClearDeviceCache();
    
    // ==================== 统计信息接口 ====================
    
    /**
     * 获取发现统计信息
     * 
     * @return 统计信息
     */
    DiscoveryStatistics GetStatistics() const;
    
    /**
     * 重置统计信息
     */
    void ResetStatistics();
    
    // ==================== 配置管理接口 ====================
    
    /**
     * 设置发现超时时间
     * 
     * @param timeoutMs 超时时间（毫秒）
     */
    void SetDiscoveryTimeout(uint32_t timeoutMs);
    
    /**
     * 获取发现超时时间
     * 
     * @return 超时时间（毫秒）
     */
    uint32_t GetDiscoveryTimeout() const { return mDiscoveryTimeoutMs; }
    
    /**
     * 设置设备缓存超时时间
     * 
     * @param timeoutMs 超时时间（毫秒）
     */
    void SetDeviceCacheTimeout(uint32_t timeoutMs);
    
    /**
     * 获取设备缓存超时时间
     * 
     * @return 超时时间（毫秒）
     */
    uint32_t GetDeviceCacheTimeout() const { return mDeviceCacheTimeoutMs; }
    
    /**
     * 启用或禁用指定协议
     * 
     * @param medium 传输介质
     * @param enabled 是否启用
     */
    void SetMediumEnabled(Medium medium, bool enabled);
    
    /**
     * 检查指定协议是否启用
     * 
     * @param medium 传输介质
     * @return true 启用，false 禁用
     */
    bool IsMediumEnabled(Medium medium) const;

private:
    // 初始化状态
    std::atomic<bool> mInitialized;
    std::atomic<bool> mRunning;
    
    // 回调函数
    DeviceFoundCallback mDeviceFoundCallback;
    
    // 协议发现器
    std::unique_ptr<WifiDiscovery> mWifiDiscovery;
    std::unique_ptr<BluetoothDiscovery> mBluetoothDiscovery;
    std::unique_ptr<BleDiscovery> mBleDiscovery;
    std::unique_ptr<UsbDiscovery> mUsbDiscovery;
    
    // 会话管理
    mutable std::mutex mSessionMutex;
    std::map<int, std::unique_ptr<DiscoverySession>> mDiscoverySessions;
    std::map<int, std::unique_ptr<PublishSession>> mPublishSessions;
    
    // 设备缓存
    mutable std::mutex mDeviceMutex;
    std::map<std::string, DeviceInfo> mDiscoveredDevices;
    
    // 统计信息
    mutable std::mutex mStatsMutex;
    DiscoveryStatistics mStatistics;
    
    // 配置参数
    std::atomic<uint32_t> mDiscoveryTimeoutMs;
    std::atomic<uint32_t> mDeviceCacheTimeoutMs;
    std::map<Medium, bool> mMediumEnabled;
    
    // 工作线程
    std::thread mWorkerThread;
    std::atomic<bool> mWorkerRunning;
    std::mutex mWorkerMutex;
    std::condition_variable mWorkerCondition;
    
    // ID生成器
    std::atomic<int> mNextDiscoveryId;
    std::atomic<int> mNextPublishId;
    
    // 内部方法
    bool InitializeDiscoverers();
    void DeinitializeDiscoverers();
    void WorkerThreadFunc();
    void ProcessDiscoveryTimeout();
    void ProcessDeviceCacheTimeout();
    void OnDeviceFoundInternal(const DeviceInfo& device);
    bool IsValidDiscoveryConfig(const DiscoveryConfig& config) const;
    bool IsValidCapability(const std::string& capability) const;
    void UpdateStatistics();
    
    // 协议特定的发现方法
    int StartWifiDiscovery(const DiscoveryConfig& config);
    int StartBluetoothDiscovery(const DiscoveryConfig& config);
    int StartBleDiscovery(const DiscoveryConfig& config);
    int StartUsbDiscovery(const DiscoveryConfig& config);
    
    int StopWifiDiscovery(int discoveryId);
    int StopBluetoothDiscovery(int discoveryId);
    int StopBleDiscovery(int discoveryId);
    int StopUsbDiscovery(int discoveryId);
    
    // 协议特定的发布方法
    int PublishWifiService(const std::string& capability);
    int PublishBluetoothService(const std::string& capability);
    int PublishBleService(const std::string& capability);
    int PublishUsbService(const std::string& capability);
    
    int StopWifiPublish(int publishId);
    int StopBluetoothPublish(int publishId);
    int StopBlePublish(int publishId);
    int StopUsbPublish(int publishId);
};

} // namespace android

#endif // DISCOVERY_MANAGER_H
