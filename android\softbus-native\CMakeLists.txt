# Copyright (c) 2024 SentientNotes Project
# Licensed under the Apache License, Version 2.0 (the "License");
# 
# SoftBus Native CMake Build Configuration
# 基于OpenHarmony SoftBus架构移植到Android用户态

cmake_minimum_required(VERSION 3.18)

project(SoftBusNative VERSION 1.0.0 LANGUAGES C CXX)

# 设置C/C++标准
set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -Werror -fPIC")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Werror -fPIC")

# Debug模式下的编译选项
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0 -DDEBUG")

# Release模式下的编译选项
set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -O2 -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2 -DNDEBUG")

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/core/common
    ${CMAKE_CURRENT_SOURCE_DIR}/core/discovery
    ${CMAKE_CURRENT_SOURCE_DIR}/core/connection
    ${CMAKE_CURRENT_SOURCE_DIR}/core/transmission
    ${CMAKE_CURRENT_SOURCE_DIR}/core/authentication
    ${CMAKE_CURRENT_SOURCE_DIR}/daemon
    ${CMAKE_CURRENT_SOURCE_DIR}/sdk
)

# 查找依赖库
find_package(PkgConfig REQUIRED)
find_package(Threads REQUIRED)

# OpenSSL
find_package(OpenSSL REQUIRED)
if(OPENSSL_FOUND)
    message(STATUS "Found OpenSSL: ${OPENSSL_VERSION}")
    include_directories(${OPENSSL_INCLUDE_DIR})
else()
    message(FATAL_ERROR "OpenSSL not found")
endif()

# 定义源文件
set(SOFTBUS_COMMON_SOURCES
    core/common/softbus_utils.c
    core/common/softbus_log.c
    core/common/softbus_thread.c
    core/common/softbus_queue.c
)

set(SOFTBUS_DISCOVERY_SOURCES
    core/discovery/disc_manager.c
    core/discovery/disc_wifi.c
    core/discovery/disc_ble.c
    core/discovery/disc_br.c
)

set(SOFTBUS_CONNECTION_SOURCES
    core/connection/conn_manager.c
    core/connection/conn_wifi.c
    core/connection/conn_ble.c
    core/connection/conn_br.c
)

set(SOFTBUS_TRANSMISSION_SOURCES
    core/transmission/trans_session.c
    core/transmission/trans_channel.c
    core/transmission/trans_stream.c
    core/transmission/trans_file.c
)

set(SOFTBUS_AUTH_SOURCES
    core/authentication/auth_manager.c
    core/authentication/auth_crypto.c
    core/authentication/auth_device.c
)

set(SOFTBUS_BUS_CENTER_SOURCES
    core/bus_center/lnn_bus_center.c
    core/bus_center/lnn_node_manager.c
    core/bus_center/lnn_time_sync.c
    core/bus_center/lnn_meta_node.c
)

set(SOFTBUS_DAEMON_SOURCES
    daemon/softbus_server_main.c
    daemon/softbus_server.c
    daemon/softbus_permission.c
    daemon/softbus_ipc.c
)

set(SOFTBUS_CLIENT_SOURCES
    sdk/softbus_client.c
    sdk/softbus_client_stub.c
)

# 创建静态库 - SoftBus Core
add_library(softbus_core STATIC
    ${SOFTBUS_COMMON_SOURCES}
    ${SOFTBUS_DISCOVERY_SOURCES}
    ${SOFTBUS_CONNECTION_SOURCES}
    ${SOFTBUS_TRANSMISSION_SOURCES}
    ${SOFTBUS_AUTH_SOURCES}
    ${SOFTBUS_BUS_CENTER_SOURCES}
)

target_link_libraries(softbus_core
    ${CMAKE_THREAD_LIBS_INIT}
    ${OPENSSL_LIBRARIES}
    m
)

# 创建共享库 - SoftBus Client SDK
add_library(softbus_client SHARED
    ${SOFTBUS_CLIENT_SOURCES}
)

target_link_libraries(softbus_client
    softbus_core
    ${CMAKE_THREAD_LIBS_INIT}
    ${OPENSSL_LIBRARIES}
)

# 设置共享库版本
set_target_properties(softbus_client PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
)

# 创建可执行文件 - SoftBus Server Daemon
add_executable(softbus_server
    ${SOFTBUS_DAEMON_SOURCES}
)

target_link_libraries(softbus_server
    softbus_core
    ${CMAKE_THREAD_LIBS_INIT}
    ${OPENSSL_LIBRARIES}
)

# 创建测试程序
if(BUILD_TESTING)
    enable_testing()
    
    # 发现测试
    add_executable(test_discovery
        tests/test_discovery.c
    )
    target_link_libraries(test_discovery
        softbus_client
        ${CMAKE_THREAD_LIBS_INIT}
    )
    add_test(NAME discovery_test COMMAND test_discovery)
    
    # 连接测试
    add_executable(test_connection
        tests/test_connection.c
    )
    target_link_libraries(test_connection
        softbus_client
        ${CMAKE_THREAD_LIBS_INIT}
    )
    add_test(NAME connection_test COMMAND test_connection)
    
    # 会话测试
    add_executable(test_session
        tests/test_session.c
    )
    target_link_libraries(test_session
        softbus_client
        ${CMAKE_THREAD_LIBS_INIT}
    )
    add_test(NAME session_test COMMAND test_session)
    
    # 认证测试
    add_executable(test_auth
        tests/test_auth.c
    )
    target_link_libraries(test_auth
        softbus_client
        ${CMAKE_THREAD_LIBS_INIT}
    )
    add_test(NAME auth_test COMMAND test_auth)
endif()

# 创建示例程序
add_subdirectory(examples)

# 安装配置
install(TARGETS softbus_server
    RUNTIME DESTINATION bin
)

install(TARGETS softbus_client
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(TARGETS softbus_core
    ARCHIVE DESTINATION lib
)

install(DIRECTORY include/
    DESTINATION include/softbus
    FILES_MATCHING PATTERN "*.h"
)

# 创建pkg-config文件
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/softbus.pc.in
    ${CMAKE_CURRENT_BINARY_DIR}/softbus.pc
    @ONLY
)

install(FILES ${CMAKE_CURRENT_BINARY_DIR}/softbus.pc
    DESTINATION lib/pkgconfig
)

# 创建systemd服务文件（如果支持）
if(SYSTEMD_FOUND)
    configure_file(
        ${CMAKE_CURRENT_SOURCE_DIR}/softbus.service.in
        ${CMAKE_CURRENT_BINARY_DIR}/softbus.service
        @ONLY
    )
    
    install(FILES ${CMAKE_CURRENT_BINARY_DIR}/softbus.service
        DESTINATION ${SYSTEMD_SERVICES_INSTALL_DIR}
    )
endif()

# 打包配置
set(CPACK_PACKAGE_NAME "softbus-native")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "SoftBus Native Library for Android")
set(CPACK_PACKAGE_VENDOR "SentientNotes Project")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

# 设置包类型
if(UNIX AND NOT APPLE)
    set(CPACK_GENERATOR "DEB;RPM;TGZ")
    
    # DEB包配置
    set(CPACK_DEBIAN_PACKAGE_DEPENDS "libc6, libssl1.1, libpthread-stubs0-dev")
    set(CPACK_DEBIAN_PACKAGE_SECTION "libs")
    set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")
    
    # RPM包配置
    set(CPACK_RPM_PACKAGE_GROUP "System/Libraries")
    set(CPACK_RPM_PACKAGE_REQUIRES "glibc, openssl-libs")
    set(CPACK_RPM_PACKAGE_LICENSE "Apache-2.0")
    
elseif(WIN32)
    set(CPACK_GENERATOR "NSIS;ZIP")
    
elseif(APPLE)
    set(CPACK_GENERATOR "DragNDrop;TGZ")
endif()

include(CPack)

# 自定义目标
add_custom_target(format
    COMMAND find ${CMAKE_CURRENT_SOURCE_DIR} -name "*.c" -o -name "*.h" | xargs clang-format -i
    COMMENT "Formatting source code"
)

add_custom_target(lint
    COMMAND find ${CMAKE_CURRENT_SOURCE_DIR} -name "*.c" -o -name "*.h" | xargs cppcheck --enable=all --std=c11
    COMMENT "Running static analysis"
)

add_custom_target(docs
    COMMAND doxygen ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT "Generating documentation"
)

# 清理目标
add_custom_target(clean-all
    COMMAND ${CMAKE_BUILD_TOOL} clean
    COMMAND rm -rf ${CMAKE_CURRENT_BINARY_DIR}/CMakeCache.txt
    COMMAND rm -rf ${CMAKE_CURRENT_BINARY_DIR}/CMakeFiles
    COMMENT "Cleaning all build files"
)

# 打印配置信息
message(STATUS "")
message(STATUS "SoftBus Native Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C Compiler: ${CMAKE_C_COMPILER}")
message(STATUS "  C++ Compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "  C Flags: ${CMAKE_C_FLAGS}")
message(STATUS "  C++ Flags: ${CMAKE_CXX_FLAGS}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  OpenSSL version: ${OPENSSL_VERSION}")
message(STATUS "  Build testing: ${BUILD_TESTING}")
message(STATUS "")
