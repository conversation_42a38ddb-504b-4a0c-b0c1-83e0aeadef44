/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * Connection Manager - 连接管理器实现
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#include "conn_manager.h"
#include "softbus_common.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <errno.h>
#include <sys/time.h>

#define MAX_CONNECTIONS 64
#define CONNECTION_TIMEOUT_MS 30000
#define HEARTBEAT_INTERVAL_MS 30000
#define CONNECTION_RETRY_MAX 3

/* 连接状态枚举 */
typedef enum {
    CONN_STATE_IDLE = 0,
    CONN_STATE_CONNECTING = 1,
    CONN_STATE_CONNECTED = 2,
    CONN_STATE_DISCONNECTING = 3,
    CONN_STATE_DISCONNECTED = 4,
    CONN_STATE_ERROR = 5
} ConnectionState;

/* 连接信息结构 */
typedef struct {
    bool isUsed;
    uint32_t connectionId;
    char deviceId[DEVICE_ID_SIZE_MAX];
    char networkId[NETWORK_ID_SIZE_MAX];
    ConnectionAddr addr;
    ConnectionState state;
    int32_t socket;
    uint64_t connectTime;
    uint64_t lastActiveTime;
    uint32_t retryCount;
    pthread_t recvThread;
    bool threadRunning;
    ConnectCallback connectCb;
    DisconnectCallback disconnectCb;
    DataReceivedCallback dataReceivedCb;
    void* userdata;
} ConnectionInfo;

/* 连接管理器上下文 */
typedef struct {
    bool isInitialized;
    bool isRunning;
    pthread_mutex_t mutex;
    pthread_t heartbeatThread;
    ConnectionInfo connections[MAX_CONNECTIONS];
    uint32_t nextConnectionId;
    uint32_t totalConnections;
    uint64_t totalDataSent;
    uint64_t totalDataReceived;
} ConnectionManager;

static ConnectionManager g_connMgr = {0};

/* 获取当前时间戳（毫秒） */
static uint64_t GetCurrentTimeMs(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

/* 查找空闲连接槽 */
static ConnectionInfo* FindFreeConnection(void) {
    for (int32_t i = 0; i < MAX_CONNECTIONS; i++) {
        if (!g_connMgr.connections[i].isUsed) {
            return &g_connMgr.connections[i];
        }
    }
    return NULL;
}

/* 根据连接ID查找连接 */
static ConnectionInfo* FindConnection(uint32_t connectionId) {
    for (int32_t i = 0; i < MAX_CONNECTIONS; i++) {
        if (g_connMgr.connections[i].isUsed && 
            g_connMgr.connections[i].connectionId == connectionId) {
            return &g_connMgr.connections[i];
        }
    }
    return NULL;
}

/* 根据设备ID查找连接 */
static ConnectionInfo* FindConnectionByDevice(const char* deviceId) {
    for (int32_t i = 0; i < MAX_CONNECTIONS; i++) {
        if (g_connMgr.connections[i].isUsed && 
            strcmp(g_connMgr.connections[i].deviceId, deviceId) == 0) {
            return &g_connMgr.connections[i];
        }
    }
    return NULL;
}

/* 创建TCP套接字连接 */
static int32_t CreateTcpConnection(const ConnectionAddr* addr) {
    int32_t sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        SOFTBUS_LOG_ERROR("Failed to create TCP socket: %s", strerror(errno));
        return -1;
    }
    
    /* 设置非阻塞模式 */
    int flags = fcntl(sockfd, F_GETFL, 0);
    fcntl(sockfd, F_SETFL, flags | O_NONBLOCK);
    
    struct sockaddr_in serverAddr;
    memset(&serverAddr, 0, sizeof(serverAddr));
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(addr->info.ip.port);
    
    if (inet_pton(AF_INET, addr->info.ip.ip, &serverAddr.sin_addr) <= 0) {
        SOFTBUS_LOG_ERROR("Invalid IP address: %s", addr->info.ip.ip);
        close(sockfd);
        return -1;
    }
    
    int32_t result = connect(sockfd, (struct sockaddr*)&serverAddr, sizeof(serverAddr));
    if (result < 0 && errno != EINPROGRESS) {
        SOFTBUS_LOG_ERROR("Failed to connect: %s", strerror(errno));
        close(sockfd);
        return -1;
    }
    
    /* 等待连接完成 */
    fd_set writeSet;
    struct timeval timeout;
    FD_ZERO(&writeSet);
    FD_SET(sockfd, &writeSet);
    timeout.tv_sec = CONNECTION_TIMEOUT_MS / 1000;
    timeout.tv_usec = (CONNECTION_TIMEOUT_MS % 1000) * 1000;
    
    result = select(sockfd + 1, NULL, &writeSet, NULL, &timeout);
    if (result <= 0) {
        SOFTBUS_LOG_ERROR("Connection timeout or error");
        close(sockfd);
        return -1;
    }
    
    /* 检查连接状态 */
    int error;
    socklen_t len = sizeof(error);
    if (getsockopt(sockfd, SOL_SOCKET, SO_ERROR, &error, &len) < 0 || error != 0) {
        SOFTBUS_LOG_ERROR("Connection failed: %s", strerror(error));
        close(sockfd);
        return -1;
    }
    
    /* 恢复阻塞模式 */
    fcntl(sockfd, F_SETFL, flags);
    
    return sockfd;
}

/* 数据接收线程 */
static void* RecvThreadFunc(void* arg) {
    ConnectionInfo* conn = (ConnectionInfo*)arg;
    char buffer[4096];
    ssize_t bytesReceived;
    
    SOFTBUS_LOG_INFO("Receive thread started for connection %u", conn->connectionId);
    
    while (conn->threadRunning && conn->state == CONN_STATE_CONNECTED) {
        bytesReceived = recv(conn->socket, buffer, sizeof(buffer), 0);
        if (bytesReceived > 0) {
            conn->lastActiveTime = GetCurrentTimeMs();
            g_connMgr.totalDataReceived += bytesReceived;
            
            /* 调用数据接收回调 */
            if (conn->dataReceivedCb != NULL) {
                conn->dataReceivedCb(conn->connectionId, buffer, bytesReceived, conn->userdata);
            }
            
            SOFTBUS_LOG_DEBUG("Received %zd bytes from connection %u", 
                             bytesReceived, conn->connectionId);
        } else if (bytesReceived == 0) {
            /* 连接被对端关闭 */
            SOFTBUS_LOG_INFO("Connection %u closed by peer", conn->connectionId);
            break;
        } else {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                usleep(10000); // 10ms
                continue;
            }
            SOFTBUS_LOG_ERROR("Receive error on connection %u: %s", 
                             conn->connectionId, strerror(errno));
            break;
        }
    }
    
    /* 连接断开处理 */
    pthread_mutex_lock(&g_connMgr.mutex);
    if (conn->state == CONN_STATE_CONNECTED) {
        conn->state = CONN_STATE_DISCONNECTED;
        if (conn->disconnectCb != NULL) {
            conn->disconnectCb(conn->connectionId, conn->userdata);
        }
    }
    pthread_mutex_unlock(&g_connMgr.mutex);
    
    SOFTBUS_LOG_INFO("Receive thread stopped for connection %u", conn->connectionId);
    return NULL;
}

/* 心跳线程 */
static void* HeartbeatThreadFunc(void* arg) {
    (void)arg;
    
    SOFTBUS_LOG_INFO("Heartbeat thread started");
    
    while (g_connMgr.isRunning) {
        uint64_t currentTime = GetCurrentTimeMs();
        
        pthread_mutex_lock(&g_connMgr.mutex);
        
        for (int32_t i = 0; i < MAX_CONNECTIONS; i++) {
            ConnectionInfo* conn = &g_connMgr.connections[i];
            if (!conn->isUsed || conn->state != CONN_STATE_CONNECTED) {
                continue;
            }
            
            /* 检查连接是否超时 */
            if (currentTime - conn->lastActiveTime > HEARTBEAT_INTERVAL_MS * 2) {
                SOFTBUS_LOG_WARN("Connection %u timeout, disconnecting", conn->connectionId);
                conn->state = CONN_STATE_DISCONNECTED;
                if (conn->disconnectCb != NULL) {
                    conn->disconnectCb(conn->connectionId, conn->userdata);
                }
                continue;
            }
            
            /* 发送心跳包 */
            const char* heartbeat = "HEARTBEAT";
            ssize_t bytesSent = send(conn->socket, heartbeat, strlen(heartbeat), MSG_NOSIGNAL);
            if (bytesSent > 0) {
                g_connMgr.totalDataSent += bytesSent;
                SOFTBUS_LOG_DEBUG("Heartbeat sent to connection %u", conn->connectionId);
            } else {
                SOFTBUS_LOG_WARN("Failed to send heartbeat to connection %u", conn->connectionId);
            }
        }
        
        pthread_mutex_unlock(&g_connMgr.mutex);
        
        sleep(HEARTBEAT_INTERVAL_MS / 1000);
    }
    
    SOFTBUS_LOG_INFO("Heartbeat thread stopped");
    return NULL;
}

/* 初始化连接管理器 */
int32_t InitConnectionManager(void) {
    if (g_connMgr.isInitialized) {
        return SOFTBUS_OK;
    }
    
    memset(&g_connMgr, 0, sizeof(g_connMgr));
    
    if (pthread_mutex_init(&g_connMgr.mutex, NULL) != 0) {
        SOFTBUS_LOG_ERROR("Failed to initialize connection mutex");
        return SOFTBUS_ERR;
    }
    
    g_connMgr.nextConnectionId = 1;
    g_connMgr.isInitialized = true;
    g_connMgr.isRunning = true;
    
    /* 启动心跳线程 */
    if (pthread_create(&g_connMgr.heartbeatThread, NULL, HeartbeatThreadFunc, NULL) != 0) {
        SOFTBUS_LOG_ERROR("Failed to create heartbeat thread");
        pthread_mutex_destroy(&g_connMgr.mutex);
        return SOFTBUS_ERR;
    }
    
    SOFTBUS_LOG_INFO("Connection manager initialized");
    return SOFTBUS_OK;
}

/* 反初始化连接管理器 */
void DeinitConnectionManager(void) {
    if (!g_connMgr.isInitialized) {
        return;
    }
    
    g_connMgr.isRunning = false;
    
    /* 等待心跳线程结束 */
    pthread_join(g_connMgr.heartbeatThread, NULL);
    
    /* 关闭所有连接 */
    pthread_mutex_lock(&g_connMgr.mutex);
    for (int32_t i = 0; i < MAX_CONNECTIONS; i++) {
        ConnectionInfo* conn = &g_connMgr.connections[i];
        if (conn->isUsed) {
            DisconnectDevice(conn->connectionId);
        }
    }
    pthread_mutex_unlock(&g_connMgr.mutex);
    
    pthread_mutex_destroy(&g_connMgr.mutex);
    
    memset(&g_connMgr, 0, sizeof(g_connMgr));
    
    SOFTBUS_LOG_INFO("Connection manager deinitialized");
}

/* 连接到设备 */
uint32_t ConnectToDevice(const char* deviceId, const ConnectionAddr* addr,
                        ConnectCallback connectCb, DisconnectCallback disconnectCb,
                        DataReceivedCallback dataReceivedCb, void* userdata) {
    if (!g_connMgr.isInitialized || deviceId == NULL || addr == NULL) {
        return 0;
    }
    
    pthread_mutex_lock(&g_connMgr.mutex);
    
    /* 检查是否已经连接到该设备 */
    ConnectionInfo* existingConn = FindConnectionByDevice(deviceId);
    if (existingConn != NULL && existingConn->state == CONN_STATE_CONNECTED) {
        pthread_mutex_unlock(&g_connMgr.mutex);
        SOFTBUS_LOG_WARN("Already connected to device: %s", deviceId);
        return existingConn->connectionId;
    }
    
    /* 查找空闲连接槽 */
    ConnectionInfo* conn = FindFreeConnection();
    if (conn == NULL) {
        pthread_mutex_unlock(&g_connMgr.mutex);
        SOFTBUS_LOG_ERROR("No free connection slot available");
        return 0;
    }
    
    /* 初始化连接信息 */
    memset(conn, 0, sizeof(ConnectionInfo));
    conn->isUsed = true;
    conn->connectionId = g_connMgr.nextConnectionId++;
    strncpy(conn->deviceId, deviceId, sizeof(conn->deviceId) - 1);
    memcpy(&conn->addr, addr, sizeof(ConnectionAddr));
    conn->state = CONN_STATE_CONNECTING;
    conn->connectCb = connectCb;
    conn->disconnectCb = disconnectCb;
    conn->dataReceivedCb = dataReceivedCb;
    conn->userdata = userdata;
    conn->connectTime = GetCurrentTimeMs();
    conn->lastActiveTime = conn->connectTime;
    
    uint32_t connectionId = conn->connectionId;
    
    pthread_mutex_unlock(&g_connMgr.mutex);
    
    /* 创建连接 */
    int32_t socket = -1;
    switch (addr->type) {
        case CONNECTION_ADDR_WLAN:
            socket = CreateTcpConnection(addr);
            break;
        case CONNECTION_ADDR_BR:
        case CONNECTION_ADDR_BLE:
        case CONNECTION_ADDR_ETH:
        default:
            SOFTBUS_LOG_ERROR("Unsupported connection type: %d", addr->type);
            break;
    }
    
    pthread_mutex_lock(&g_connMgr.mutex);
    
    if (socket < 0) {
        /* 连接失败 */
        conn->state = CONN_STATE_ERROR;
        conn->isUsed = false;
        pthread_mutex_unlock(&g_connMgr.mutex);
        
        if (connectCb != NULL) {
            connectCb(connectionId, SOFTBUS_ERR, userdata);
        }
        
        SOFTBUS_LOG_ERROR("Failed to connect to device: %s", deviceId);
        return 0;
    }
    
    /* 连接成功 */
    conn->socket = socket;
    conn->state = CONN_STATE_CONNECTED;
    conn->threadRunning = true;
    g_connMgr.totalConnections++;
    
    /* 启动接收线程 */
    if (pthread_create(&conn->recvThread, NULL, RecvThreadFunc, conn) != 0) {
        close(socket);
        conn->state = CONN_STATE_ERROR;
        conn->isUsed = false;
        pthread_mutex_unlock(&g_connMgr.mutex);
        
        if (connectCb != NULL) {
            connectCb(connectionId, SOFTBUS_ERR, userdata);
        }
        
        SOFTBUS_LOG_ERROR("Failed to create receive thread for connection %u", connectionId);
        return 0;
    }
    
    pthread_mutex_unlock(&g_connMgr.mutex);
    
    /* 调用连接成功回调 */
    if (connectCb != NULL) {
        connectCb(connectionId, SOFTBUS_OK, userdata);
    }
    
    SOFTBUS_LOG_INFO("Connected to device: %s, connectionId=%u", deviceId, connectionId);
    return connectionId;
}

/* 断开设备连接 */
int32_t DisconnectDevice(uint32_t connectionId) {
    if (!g_connMgr.isInitialized) {
        return SOFTBUS_NO_INIT;
    }
    
    pthread_mutex_lock(&g_connMgr.mutex);
    
    ConnectionInfo* conn = FindConnection(connectionId);
    if (conn == NULL) {
        pthread_mutex_unlock(&g_connMgr.mutex);
        SOFTBUS_LOG_ERROR("Connection not found: %u", connectionId);
        return SOFTBUS_ERR;
    }
    
    if (conn->state == CONN_STATE_DISCONNECTED || conn->state == CONN_STATE_DISCONNECTING) {
        pthread_mutex_unlock(&g_connMgr.mutex);
        return SOFTBUS_OK;
    }
    
    conn->state = CONN_STATE_DISCONNECTING;
    conn->threadRunning = false;
    
    /* 关闭套接字 */
    if (conn->socket >= 0) {
        close(conn->socket);
        conn->socket = -1;
    }
    
    pthread_mutex_unlock(&g_connMgr.mutex);
    
    /* 等待接收线程结束 */
    if (conn->recvThread != 0) {
        pthread_join(conn->recvThread, NULL);
    }
    
    pthread_mutex_lock(&g_connMgr.mutex);
    
    /* 调用断开连接回调 */
    if (conn->disconnectCb != NULL) {
        conn->disconnectCb(connectionId, conn->userdata);
    }
    
    /* 清理连接信息 */
    SOFTBUS_LOG_INFO("Disconnected from device: %s, connectionId=%u", 
                     conn->deviceId, connectionId);
    
    memset(conn, 0, sizeof(ConnectionInfo));
    
    pthread_mutex_unlock(&g_connMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 发送数据 */
int32_t SendData(uint32_t connectionId, const void* data, uint32_t dataLen) {
    if (!g_connMgr.isInitialized || data == NULL || dataLen == 0) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_connMgr.mutex);
    
    ConnectionInfo* conn = FindConnection(connectionId);
    if (conn == NULL || conn->state != CONN_STATE_CONNECTED) {
        pthread_mutex_unlock(&g_connMgr.mutex);
        SOFTBUS_LOG_ERROR("Connection not available: %u", connectionId);
        return SOFTBUS_ERR;
    }
    
    int32_t socket = conn->socket;
    pthread_mutex_unlock(&g_connMgr.mutex);
    
    ssize_t bytesSent = send(socket, data, dataLen, MSG_NOSIGNAL);
    if (bytesSent < 0) {
        SOFTBUS_LOG_ERROR("Failed to send data on connection %u: %s", 
                         connectionId, strerror(errno));
        return SOFTBUS_ERR;
    }
    
    if ((uint32_t)bytesSent != dataLen) {
        SOFTBUS_LOG_WARN("Partial data sent on connection %u: %zd/%u bytes", 
                         connectionId, bytesSent, dataLen);
    }
    
    pthread_mutex_lock(&g_connMgr.mutex);
    g_connMgr.totalDataSent += bytesSent;
    if (conn->isUsed) {
        conn->lastActiveTime = GetCurrentTimeMs();
    }
    pthread_mutex_unlock(&g_connMgr.mutex);
    
    SOFTBUS_LOG_DEBUG("Sent %zd bytes on connection %u", bytesSent, connectionId);
    return SOFTBUS_OK;
}

/* 获取连接统计信息 */
int32_t GetConnectionStatistics(ConnectionStatistics* stats) {
    if (!g_connMgr.isInitialized || stats == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_connMgr.mutex);
    
    memset(stats, 0, sizeof(ConnectionStatistics));
    stats->totalConnections = g_connMgr.totalConnections;
    stats->totalDataSent = g_connMgr.totalDataSent;
    stats->totalDataReceived = g_connMgr.totalDataReceived;
    
    /* 统计当前活跃连接 */
    for (int32_t i = 0; i < MAX_CONNECTIONS; i++) {
        if (g_connMgr.connections[i].isUsed) {
            stats->activeConnections++;
            if (g_connMgr.connections[i].state == CONN_STATE_CONNECTED) {
                stats->connectedDevices++;
            }
        }
    }
    
    pthread_mutex_unlock(&g_connMgr.mutex);
    
    return SOFTBUS_OK;
}
