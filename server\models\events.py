"""
灵境笔记事件流数据模型
基于设计文档中的SN_EVENT_STREAM定义
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from enum import Enum
import time


class InputType(str, Enum):
    """输入模态类型枚举"""
    AR_GESTURE = "AR_GESTURE"
    PENCIL_STROKE = "PENCIL_STROKE"
    VOICE_COMMAND = "VOICE_COMMAND"
    IMAGE_ROI = "IMAGE_ROI"
    TEXT_INPUT = "TEXT_INPUT"
    SPATIAL_INTERACTION = "SPATIAL_INTERACTION"
    PHYSICAL_OBJECT = "PHYSICAL_OBJECT"
    BIOMETRIC_SIGNAL = "BIOMETRIC_SIGNAL"


class Pose6DOF(BaseModel):
    """6自由度姿态数据"""
    position: List[float] = Field(..., min_items=3, max_items=3, description="3D位置坐标")
    rotation: List[float] = Field(..., min_items=4, max_items=4, description="四元数旋转")


class HandKeypoint(BaseModel):
    """手部关键点数据"""
    timestamp_us: int = Field(..., description="时间戳（微秒）")
    left_hand_keypoints: Optional[List[List[float]]] = Field(None, description="左手关键点")
    right_hand_keypoints: Optional[List[List[float]]] = Field(None, description="右手关键点")
    confidence_scores: List[float] = Field(..., description="置信度分数")


class GestureTrajectory(BaseModel):
    """手势轨迹数据"""
    start_position: List[float] = Field(..., min_items=3, max_items=3)
    end_position: List[float] = Field(..., min_items=3, max_items=3)
    trajectory_points: List[List[float]] = Field(..., description="轨迹点序列")


class SpatialRegion(BaseModel):
    """空间区域定义"""
    region_type: str = Field(..., description="区域类型: SPHERE, BOX, PLANE")
    center: List[float] = Field(..., min_items=3, max_items=3)
    dimensions: List[float] = Field(..., description="区域尺寸参数")


class UserGazeFocus(BaseModel):
    """用户注视焦点数据"""
    gaze_direction: List[float] = Field(..., min_items=3, max_items=3)
    focus_object_id: Optional[str] = Field(None, description="注视目标对象ID")
    focus_confidence: float = Field(..., ge=0.0, le=1.0, description="注视置信度")


class ARGestureCommand(BaseModel):
    """AR手势指令数据"""
    hand_skeleton_sequence: List[HandKeypoint] = Field(..., description="手部骨骼序列")
    gesture_start_timestamp: int = Field(..., description="手势开始时间戳")
    gesture_end_timestamp: int = Field(..., description="手势结束时间戳")
    gesture_3d_space_vector: GestureTrajectory = Field(..., description="3D空间手势轨迹")
    target_object_id: Optional[str] = Field(None, description="目标对象ID")
    target_spatial_region: Optional[SpatialRegion] = Field(None, description="目标空间区域")
    user_gaze_focus: Optional[UserGazeFocus] = Field(None, description="用户注视焦点")


class PencilStrokePoint(BaseModel):
    """笔迹采样点"""
    timestamp_us: int = Field(..., description="时间戳（微秒）")
    position: List[float] = Field(..., min_items=2, max_items=3, description="位置坐标")
    pressure: float = Field(..., ge=0.0, le=1.0, description="压力值")
    tilt_x: Optional[float] = Field(None, description="X轴倾斜")
    tilt_y: Optional[float] = Field(None, description="Y轴倾斜")
    azimuth: Optional[float] = Field(None, description="方位角")


class PencilStroke(BaseModel):
    """单个笔迹"""
    stroke_id: str = Field(..., description="笔迹ID")
    sampling_points: List[PencilStrokePoint] = Field(..., description="采样点序列")
    stroke_start_timestamp: int = Field(..., description="笔迹开始时间")
    stroke_end_timestamp: int = Field(..., description="笔迹结束时间")


class VirtualInkProperties(BaseModel):
    """虚拟墨水属性"""
    color_rgba: List[float] = Field(..., min_items=4, max_items=4, description="RGBA颜色")
    brush_size: float = Field(..., gt=0, description="笔刷大小")
    brush_type: str = Field(..., description="笔刷类型: PEN, MARKER, BRUSH, PENCIL")


class WritingSurface(BaseModel):
    """书写表面信息"""
    surface_type: str = Field(..., description="表面类型")
    surface_id: str = Field(..., description="表面ID")
    texture_roughness: Optional[float] = Field(None, description="纹理粗糙度")
    friction_coefficient: Optional[float] = Field(None, description="摩擦系数")


class PencilStrokeSequence(BaseModel):
    """Pencil笔迹序列数据"""
    pencil_unique_id: str = Field(..., description="笔的唯一ID")
    stroke_sequence: List[PencilStroke] = Field(..., description="笔迹序列")
    virtual_ink_properties: VirtualInkProperties = Field(..., description="虚拟墨水属性")
    writing_surface: WritingSurface = Field(..., description="书写表面")
    sampling_rate_hz: float = Field(default=120.0, description="采样率")


class AudioStream(BaseModel):
    """音频流数据"""
    format: str = Field(..., description="音频格式: PCM, WAV, MP3, AAC")
    sample_rate: int = Field(..., description="采样率")
    bit_depth: int = Field(..., description="位深度")
    channels: int = Field(..., description="声道数")
    duration_ms: int = Field(..., description="时长（毫秒）")
    audio_data_uri: str = Field(..., description="音频数据URI")


class ASRResult(BaseModel):
    """语音识别结果"""
    transcription: str = Field(..., description="转录文本")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="置信度")
    language_detected: str = Field(..., description="检测到的语言")
    processing_device_id: str = Field(..., description="处理设备ID")


class SpatialTarget(BaseModel):
    """空间目标"""
    target_type: str = Field(..., description="目标类型")
    target_id: str = Field(..., description="目标ID")
    target_position: List[float] = Field(..., min_items=3, max_items=3)


class PointingGesture(BaseModel):
    """指向手势"""
    is_pointing: bool = Field(..., description="是否在指向")
    pointing_direction: Optional[List[float]] = Field(None, min_items=3, max_items=3)
    pointed_object_id: Optional[str] = Field(None, description="指向的对象ID")


class NearbyNoteAtom(BaseModel):
    """附近的笔记原子"""
    atom_id: str = Field(..., description="原子ID")
    distance_from_user: float = Field(..., description="与用户的距离")
    relevance_score: float = Field(..., ge=0.0, le=1.0, description="相关性分数")


class SpatialContext(BaseModel):
    """空间上下文"""
    user_gaze_target: Optional[SpatialTarget] = Field(None, description="用户注视目标")
    pointing_gesture: Optional[PointingGesture] = Field(None, description="指向手势")
    nearby_note_atoms: List[NearbyNoteAtom] = Field(default=[], description="附近的笔记原子")


class VoiceCommandWithSpatialContext(BaseModel):
    """带空间上下文的语音指令"""
    audio_stream: AudioStream = Field(..., description="音频流")
    preliminary_asr_result: Optional[ASRResult] = Field(None, description="初步ASR结果")
    spatial_context: Optional[SpatialContext] = Field(None, description="空间上下文")
    signal_to_noise_ratio: Optional[float] = Field(None, description="信噪比")
    background_noise_level: Optional[float] = Field(None, description="背景噪音级别")
    speech_clarity_score: Optional[float] = Field(None, description="语音清晰度")


class ImageSource(BaseModel):
    """图像源信息"""
    image_uri: str = Field(..., description="图像URI")
    image_format: str = Field(..., description="图像格式")
    width: int = Field(..., description="图像宽度")
    height: int = Field(..., description="图像高度")
    capture_timestamp: int = Field(..., description="捕获时间戳")


class ROICoordinates(BaseModel):
    """感兴趣区域坐标"""
    coordinate_system: str = Field(..., description="坐标系统")
    top_left: List[float] = Field(..., description="左上角坐标")
    bottom_right: List[float] = Field(..., description="右下角坐标")
    polygon_vertices: Optional[List[List[float]]] = Field(None, description="多边形顶点")


class CameraParameters(BaseModel):
    """相机参数"""
    intrinsic_matrix: List[float] = Field(..., description="内参矩阵")
    extrinsic_matrix: List[float] = Field(..., description="外参矩阵")
    distortion_coefficients: List[float] = Field(..., description="畸变系数")
    iso: Optional[int] = Field(None, description="ISO值")
    shutter_speed: Optional[float] = Field(None, description="快门速度")
    aperture: Optional[float] = Field(None, description="光圈值")


class ImageROIForAIProcessing(BaseModel):
    """用于AI处理的图像ROI"""
    image_source: ImageSource = Field(..., description="图像源")
    roi_coordinates: ROICoordinates = Field(..., description="ROI坐标")
    camera_parameters: Optional[CameraParameters] = Field(None, description="相机参数")
    requested_ai_processing: List[str] = Field(..., description="请求的AI处理类型")
    processing_priority: int = Field(default=5, ge=1, le=10, description="处理优先级")
    min_accuracy_threshold: Optional[float] = Field(None, description="最小准确度阈值")
    max_processing_time_ms: Optional[int] = Field(None, description="最大处理时间")


class TextInputWithContext(BaseModel):
    """带上下文的文本输入"""
    text_content: str = Field(..., description="文本内容")
    input_method: str = Field(..., description="输入方法")
    language: Optional[str] = Field(None, description="语言")
    context_tags: List[str] = Field(default=[], description="上下文标签")


class XRContext(BaseModel):
    """XR环境上下文"""
    head_pose_6dof: Optional[Pose6DOF] = Field(None, description="头部6DOF姿态")
    left_hand_pose_6dof: Optional[Pose6DOF] = Field(None, description="左手6DOF姿态")
    right_hand_pose_6dof: Optional[Pose6DOF] = Field(None, description="右手6DOF姿态")
    current_ar_scene_id: Optional[str] = Field(None, description="当前AR场景ID")
    active_spatial_ui_elements: List[str] = Field(default=[], description="活跃的空间UI元素")
    ambient_light_level: Optional[float] = Field(None, description="环境光照级别")
    dominant_light_direction: Optional[List[float]] = Field(None, description="主要光照方向")
    noise_level_db: Optional[float] = Field(None, description="噪音级别")
    reverberation_time: Optional[float] = Field(None, description="混响时间")


class SoftBusTransmissionMetadata(BaseModel):
    """SoftBus传输元数据"""
    channel_type: str = Field(..., description="通道类型")
    compression_algorithm: str = Field(default="LZ4", description="压缩算法")
    encryption_enabled: bool = Field(default=True, description="是否启用加密")
    transmission_priority: int = Field(default=5, ge=1, le=10, description="传输优先级")
    expected_data_size_bytes: Optional[int] = Field(None, description="预期数据大小")


class SN_EVENT_STREAM(BaseModel):
    """灵境笔记统一输入事件流对象"""
    
    event_id: str = Field(..., description="全局唯一事件标识符")
    timestamp: int = Field(default_factory=lambda: int(time.time() * 1000000), description="事件时间戳（微秒）")
    device_id: str = Field(..., description="产生事件的设备SoftBus唯一标识")
    user_id: Optional[str] = Field(None, description="用户唯一标识符（已脱敏）")
    input_type: InputType = Field(..., description="输入模态类型")
    
    # 根据input_type的具体数据，使用Union类型
    input_data: Union[
        ARGestureCommand,
        PencilStrokeSequence, 
        VoiceCommandWithSpatialContext,
        ImageROIForAIProcessing,
        TextInputWithContext,
        Dict[str, Any]  # 其他类型的fallback
    ] = Field(..., description="输入数据")
    
    current_xr_context_optional: Optional[XRContext] = Field(None, description="当前XR环境上下文")
    softbus_transmission_metadata: Optional[SoftBusTransmissionMetadata] = Field(None, description="SoftBus传输元数据")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            # 自定义JSON编码器
        }
