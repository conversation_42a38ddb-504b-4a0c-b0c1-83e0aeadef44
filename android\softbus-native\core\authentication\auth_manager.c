/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * Authentication Manager - 认证管理器实现
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#include "auth_manager.h"
#include "softbus_common.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/sha.h>
#include <openssl/aes.h>

#define MAX_AUTH_SESSIONS 32
#define AUTH_TIMEOUT_MS 60000  // 1分钟
#define AUTH_KEY_LENGTH 32
#define AUTH_IV_LENGTH 16
#define AUTH_CHALLENGE_LENGTH 32
#define AUTH_RESPONSE_LENGTH 32

/* 认证状态枚举 */
typedef enum {
    AUTH_STATE_IDLE = 0,
    AUTH_STATE_CHALLENGE_SENT = 1,
    AUTH_STATE_CHALLENGE_RECEIVED = 2,
    AUTH_STATE_RESPONSE_SENT = 3,
    AUTH_STATE_RESPONSE_RECEIVED = 4,
    AUTH_STATE_SUCCESS = 5,
    AUTH_STATE_FAILED = 6,
    AUTH_STATE_TIMEOUT = 7
} AuthState;

/* 认证会话信息 */
typedef struct {
    bool isUsed;
    uint32_t authId;
    char deviceId[DEVICE_ID_SIZE_MAX];
    char peerDeviceId[DEVICE_ID_SIZE_MAX];
    AuthState state;
    AuthForm authForm;
    uint64_t startTime;
    uint64_t lastActiveTime;
    uint8_t localChallenge[AUTH_CHALLENGE_LENGTH];
    uint8_t peerChallenge[AUTH_CHALLENGE_LENGTH];
    uint8_t sessionKey[AUTH_KEY_LENGTH];
    uint8_t iv[AUTH_IV_LENGTH];
    AuthCallback callback;
    void* userdata;
    uint32_t retryCount;
} AuthSession;

/* 认证管理器上下文 */
typedef struct {
    bool isInitialized;
    pthread_mutex_t mutex;
    AuthSession authSessions[MAX_AUTH_SESSIONS];
    uint32_t nextAuthId;
    uint8_t deviceKey[AUTH_KEY_LENGTH];
    char deviceId[DEVICE_ID_SIZE_MAX];
    uint32_t totalAuthAttempts;
    uint32_t successfulAuths;
    uint32_t failedAuths;
} AuthManager;

static AuthManager g_authMgr = {0};

/* 获取当前时间戳（毫秒） */
static uint64_t GetCurrentTimeMs(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

/* 生成随机数据 */
static int32_t GenerateRandomData(uint8_t* data, uint32_t len) {
    if (RAND_bytes(data, len) != 1) {
        SOFTBUS_LOG_ERROR("Failed to generate random data");
        return SOFTBUS_ERR;
    }
    return SOFTBUS_OK;
}

/* 计算SHA256哈希 */
static int32_t CalculateSHA256(const uint8_t* data, uint32_t dataLen, uint8_t* hash) {
    SHA256_CTX ctx;
    if (SHA256_Init(&ctx) != 1) {
        return SOFTBUS_ERR;
    }
    
    if (SHA256_Update(&ctx, data, dataLen) != 1) {
        return SOFTBUS_ERR;
    }
    
    if (SHA256_Final(hash, &ctx) != 1) {
        return SOFTBUS_ERR;
    }
    
    return SOFTBUS_OK;
}

/* AES加密 */
static int32_t AESEncrypt(const uint8_t* plaintext, uint32_t plaintextLen,
                         const uint8_t* key, const uint8_t* iv,
                         uint8_t* ciphertext, uint32_t* ciphertextLen) {
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (ctx == NULL) {
        return SOFTBUS_ERR;
    }
    
    if (EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key, iv) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return SOFTBUS_ERR;
    }
    
    int len;
    if (EVP_EncryptUpdate(ctx, ciphertext, &len, plaintext, plaintextLen) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return SOFTBUS_ERR;
    }
    *ciphertextLen = len;
    
    if (EVP_EncryptFinal_ex(ctx, ciphertext + len, &len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return SOFTBUS_ERR;
    }
    *ciphertextLen += len;
    
    EVP_CIPHER_CTX_free(ctx);
    return SOFTBUS_OK;
}

/* AES解密 */
static int32_t AESDecrypt(const uint8_t* ciphertext, uint32_t ciphertextLen,
                         const uint8_t* key, const uint8_t* iv,
                         uint8_t* plaintext, uint32_t* plaintextLen) {
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (ctx == NULL) {
        return SOFTBUS_ERR;
    }
    
    if (EVP_DecryptInit_ex(ctx, EVP_aes_256_cbc(), NULL, key, iv) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return SOFTBUS_ERR;
    }
    
    int len;
    if (EVP_DecryptUpdate(ctx, plaintext, &len, ciphertext, ciphertextLen) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return SOFTBUS_ERR;
    }
    *plaintextLen = len;
    
    if (EVP_DecryptFinal_ex(ctx, plaintext + len, &len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return SOFTBUS_ERR;
    }
    *plaintextLen += len;
    
    EVP_CIPHER_CTX_free(ctx);
    return SOFTBUS_OK;
}

/* 查找空闲认证会话 */
static AuthSession* FindFreeAuthSession(void) {
    for (int32_t i = 0; i < MAX_AUTH_SESSIONS; i++) {
        if (!g_authMgr.authSessions[i].isUsed) {
            return &g_authMgr.authSessions[i];
        }
    }
    return NULL;
}

/* 根据认证ID查找认证会话 */
static AuthSession* FindAuthSession(uint32_t authId) {
    for (int32_t i = 0; i < MAX_AUTH_SESSIONS; i++) {
        if (g_authMgr.authSessions[i].isUsed && 
            g_authMgr.authSessions[i].authId == authId) {
            return &g_authMgr.authSessions[i];
        }
    }
    return NULL;
}

/* 根据设备ID查找认证会话 */
static AuthSession* FindAuthSessionByDevice(const char* deviceId) {
    for (int32_t i = 0; i < MAX_AUTH_SESSIONS; i++) {
        if (g_authMgr.authSessions[i].isUsed && 
            strcmp(g_authMgr.authSessions[i].peerDeviceId, deviceId) == 0) {
            return &g_authMgr.authSessions[i];
        }
    }
    return NULL;
}

/* 生成认证响应 */
static int32_t GenerateAuthResponse(const uint8_t* challenge, const uint8_t* deviceKey,
                                   uint8_t* response) {
    uint8_t combinedData[AUTH_CHALLENGE_LENGTH + AUTH_KEY_LENGTH];
    memcpy(combinedData, challenge, AUTH_CHALLENGE_LENGTH);
    memcpy(combinedData + AUTH_CHALLENGE_LENGTH, deviceKey, AUTH_KEY_LENGTH);
    
    return CalculateSHA256(combinedData, sizeof(combinedData), response);
}

/* 验证认证响应 */
static bool VerifyAuthResponse(const uint8_t* challenge, const uint8_t* deviceKey,
                              const uint8_t* response) {
    uint8_t expectedResponse[AUTH_RESPONSE_LENGTH];
    if (GenerateAuthResponse(challenge, deviceKey, expectedResponse) != SOFTBUS_OK) {
        return false;
    }
    
    return memcmp(response, expectedResponse, AUTH_RESPONSE_LENGTH) == 0;
}

/* 初始化认证管理器 */
int32_t InitAuthManager(void) {
    if (g_authMgr.isInitialized) {
        return SOFTBUS_OK;
    }
    
    memset(&g_authMgr, 0, sizeof(g_authMgr));
    
    if (pthread_mutex_init(&g_authMgr.mutex, NULL) != 0) {
        SOFTBUS_LOG_ERROR("Failed to initialize auth mutex");
        return SOFTBUS_ERR;
    }
    
    /* 生成设备密钥 */
    if (GenerateRandomData(g_authMgr.deviceKey, sizeof(g_authMgr.deviceKey)) != SOFTBUS_OK) {
        pthread_mutex_destroy(&g_authMgr.mutex);
        return SOFTBUS_ERR;
    }
    
    /* 设置设备ID */
    snprintf(g_authMgr.deviceId, sizeof(g_authMgr.deviceId), "android_device_%ld", (long)getpid());
    
    g_authMgr.nextAuthId = 1;
    g_authMgr.isInitialized = true;
    
    SOFTBUS_LOG_INFO("Auth manager initialized, deviceId: %s", g_authMgr.deviceId);
    return SOFTBUS_OK;
}

/* 反初始化认证管理器 */
void DeinitAuthManager(void) {
    if (!g_authMgr.isInitialized) {
        return;
    }
    
    /* 清理所有认证会话 */
    pthread_mutex_lock(&g_authMgr.mutex);
    for (int32_t i = 0; i < MAX_AUTH_SESSIONS; i++) {
        if (g_authMgr.authSessions[i].isUsed) {
            StopAuth(g_authMgr.authSessions[i].authId);
        }
    }
    pthread_mutex_unlock(&g_authMgr.mutex);
    
    pthread_mutex_destroy(&g_authMgr.mutex);
    
    /* 清零敏感数据 */
    memset(&g_authMgr, 0, sizeof(g_authMgr));
    
    SOFTBUS_LOG_INFO("Auth manager deinitialized");
}

/* 开始认证 */
uint32_t StartAuth(const char* peerDeviceId, AuthForm authForm, 
                  AuthCallback callback, void* userdata) {
    if (!g_authMgr.isInitialized || peerDeviceId == NULL || callback == NULL) {
        return 0;
    }
    
    pthread_mutex_lock(&g_authMgr.mutex);
    
    /* 检查是否已有对该设备的认证会话 */
    AuthSession* existingSession = FindAuthSessionByDevice(peerDeviceId);
    if (existingSession != NULL) {
        pthread_mutex_unlock(&g_authMgr.mutex);
        SOFTBUS_LOG_WARN("Auth session already exists for device: %s", peerDeviceId);
        return existingSession->authId;
    }
    
    /* 查找空闲会话 */
    AuthSession* session = FindFreeAuthSession();
    if (session == NULL) {
        pthread_mutex_unlock(&g_authMgr.mutex);
        SOFTBUS_LOG_ERROR("No free auth session available");
        return 0;
    }
    
    /* 初始化认证会话 */
    memset(session, 0, sizeof(AuthSession));
    session->isUsed = true;
    session->authId = g_authMgr.nextAuthId++;
    strncpy(session->deviceId, g_authMgr.deviceId, sizeof(session->deviceId) - 1);
    strncpy(session->peerDeviceId, peerDeviceId, sizeof(session->peerDeviceId) - 1);
    session->state = AUTH_STATE_IDLE;
    session->authForm = authForm;
    session->startTime = GetCurrentTimeMs();
    session->lastActiveTime = session->startTime;
    session->callback = callback;
    session->userdata = userdata;
    
    /* 生成本地挑战 */
    if (GenerateRandomData(session->localChallenge, sizeof(session->localChallenge)) != SOFTBUS_OK) {
        memset(session, 0, sizeof(AuthSession));
        pthread_mutex_unlock(&g_authMgr.mutex);
        SOFTBUS_LOG_ERROR("Failed to generate local challenge");
        return 0;
    }
    
    /* 生成会话密钥和IV */
    if (GenerateRandomData(session->sessionKey, sizeof(session->sessionKey)) != SOFTBUS_OK ||
        GenerateRandomData(session->iv, sizeof(session->iv)) != SOFTBUS_OK) {
        memset(session, 0, sizeof(AuthSession));
        pthread_mutex_unlock(&g_authMgr.mutex);
        SOFTBUS_LOG_ERROR("Failed to generate session key or IV");
        return 0;
    }
    
    uint32_t authId = session->authId;
    g_authMgr.totalAuthAttempts++;
    
    pthread_mutex_unlock(&g_authMgr.mutex);
    
    /* 发送认证挑战 */
    if (SendAuthChallenge(authId) != SOFTBUS_OK) {
        StopAuth(authId);
        return 0;
    }
    
    SOFTBUS_LOG_INFO("Auth started: authId=%u, peerDevice=%s", authId, peerDeviceId);
    return authId;
}

/* 停止认证 */
int32_t StopAuth(uint32_t authId) {
    if (!g_authMgr.isInitialized) {
        return SOFTBUS_NO_INIT;
    }
    
    pthread_mutex_lock(&g_authMgr.mutex);
    
    AuthSession* session = FindAuthSession(authId);
    if (session == NULL) {
        pthread_mutex_unlock(&g_authMgr.mutex);
        SOFTBUS_LOG_ERROR("Auth session not found: %u", authId);
        return SOFTBUS_ERR;
    }
    
    /* 调用回调通知认证结束 */
    if (session->callback != NULL && session->state != AUTH_STATE_SUCCESS) {
        session->callback(authId, AUTH_RESULT_FAILED, NULL, 0, session->userdata);
    }
    
    /* 记录统计信息 */
    if (session->state == AUTH_STATE_SUCCESS) {
        g_authMgr.successfulAuths++;
    } else {
        g_authMgr.failedAuths++;
    }
    
    uint64_t duration = GetCurrentTimeMs() - session->startTime;
    SOFTBUS_LOG_INFO("Auth stopped: authId=%u, state=%d, duration=%llu ms", 
                     authId, session->state, duration);
    
    /* 清理会话信息 */
    memset(session, 0, sizeof(AuthSession));
    
    pthread_mutex_unlock(&g_authMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 发送认证挑战 */
int32_t SendAuthChallenge(uint32_t authId) {
    if (!g_authMgr.isInitialized) {
        return SOFTBUS_NO_INIT;
    }
    
    pthread_mutex_lock(&g_authMgr.mutex);
    
    AuthSession* session = FindAuthSession(authId);
    if (session == NULL) {
        pthread_mutex_unlock(&g_authMgr.mutex);
        return SOFTBUS_ERR;
    }
    
    /* 构造认证挑战消息 */
    AuthMessage authMsg;
    memset(&authMsg, 0, sizeof(authMsg));
    authMsg.msgType = AUTH_MSG_TYPE_CHALLENGE;
    authMsg.authId = authId;
    strncpy(authMsg.deviceId, session->deviceId, sizeof(authMsg.deviceId) - 1);
    memcpy(authMsg.challenge, session->localChallenge, sizeof(authMsg.challenge));
    
    /* 这里应该通过连接管理器发送消息 */
    /* 为了简化，我们模拟发送成功 */
    session->state = AUTH_STATE_CHALLENGE_SENT;
    session->lastActiveTime = GetCurrentTimeMs();
    
    pthread_mutex_unlock(&g_authMgr.mutex);
    
    SOFTBUS_LOG_DEBUG("Auth challenge sent: authId=%u", authId);
    return SOFTBUS_OK;
}

/* 处理认证挑战 */
int32_t ProcessAuthChallenge(const AuthMessage* authMsg) {
    if (!g_authMgr.isInitialized || authMsg == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    if (authMsg->msgType != AUTH_MSG_TYPE_CHALLENGE) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_authMgr.mutex);
    
    /* 查找或创建认证会话 */
    AuthSession* session = FindAuthSession(authMsg->authId);
    if (session == NULL) {
        /* 创建新的认证会话来响应挑战 */
        session = FindFreeAuthSession();
        if (session == NULL) {
            pthread_mutex_unlock(&g_authMgr.mutex);
            SOFTBUS_LOG_ERROR("No free auth session for challenge response");
            return SOFTBUS_ERR;
        }
        
        memset(session, 0, sizeof(AuthSession));
        session->isUsed = true;
        session->authId = authMsg->authId;
        strncpy(session->deviceId, g_authMgr.deviceId, sizeof(session->deviceId) - 1);
        strncpy(session->peerDeviceId, authMsg->deviceId, sizeof(session->peerDeviceId) - 1);
        session->startTime = GetCurrentTimeMs();
    }
    
    /* 保存对端挑战 */
    memcpy(session->peerChallenge, authMsg->challenge, sizeof(session->peerChallenge));
    session->state = AUTH_STATE_CHALLENGE_RECEIVED;
    session->lastActiveTime = GetCurrentTimeMs();
    
    pthread_mutex_unlock(&g_authMgr.mutex);
    
    /* 发送认证响应 */
    return SendAuthResponse(authMsg->authId);
}

/* 发送认证响应 */
int32_t SendAuthResponse(uint32_t authId) {
    if (!g_authMgr.isInitialized) {
        return SOFTBUS_NO_INIT;
    }
    
    pthread_mutex_lock(&g_authMgr.mutex);
    
    AuthSession* session = FindAuthSession(authId);
    if (session == NULL) {
        pthread_mutex_unlock(&g_authMgr.mutex);
        return SOFTBUS_ERR;
    }
    
    /* 生成认证响应 */
    uint8_t response[AUTH_RESPONSE_LENGTH];
    if (GenerateAuthResponse(session->peerChallenge, g_authMgr.deviceKey, response) != SOFTBUS_OK) {
        pthread_mutex_unlock(&g_authMgr.mutex);
        return SOFTBUS_ERR;
    }
    
    /* 构造认证响应消息 */
    AuthMessage authMsg;
    memset(&authMsg, 0, sizeof(authMsg));
    authMsg.msgType = AUTH_MSG_TYPE_RESPONSE;
    authMsg.authId = authId;
    strncpy(authMsg.deviceId, session->deviceId, sizeof(authMsg.deviceId) - 1);
    memcpy(authMsg.response, response, sizeof(authMsg.response));
    
    /* 这里应该通过连接管理器发送消息 */
    /* 为了简化，我们模拟发送成功 */
    session->state = AUTH_STATE_RESPONSE_SENT;
    session->lastActiveTime = GetCurrentTimeMs();
    
    pthread_mutex_unlock(&g_authMgr.mutex);
    
    SOFTBUS_LOG_DEBUG("Auth response sent: authId=%u", authId);
    return SOFTBUS_OK;
}

/* 处理认证响应 */
int32_t ProcessAuthResponse(const AuthMessage* authMsg) {
    if (!g_authMgr.isInitialized || authMsg == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    if (authMsg->msgType != AUTH_MSG_TYPE_RESPONSE) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_authMgr.mutex);
    
    AuthSession* session = FindAuthSession(authMsg->authId);
    if (session == NULL) {
        pthread_mutex_unlock(&g_authMgr.mutex);
        SOFTBUS_LOG_ERROR("Auth session not found for response: %u", authMsg->authId);
        return SOFTBUS_ERR;
    }
    
    /* 验证认证响应 */
    bool isValid = VerifyAuthResponse(session->localChallenge, g_authMgr.deviceKey, authMsg->response);
    
    if (isValid) {
        session->state = AUTH_STATE_SUCCESS;
        g_authMgr.successfulAuths++;
        
        /* 调用成功回调 */
        if (session->callback != NULL) {
            session->callback(authMsg->authId, AUTH_RESULT_SUCCESS, 
                            session->sessionKey, sizeof(session->sessionKey), session->userdata);
        }
        
        SOFTBUS_LOG_INFO("Auth successful: authId=%u, peerDevice=%s", 
                         authMsg->authId, session->peerDeviceId);
    } else {
        session->state = AUTH_STATE_FAILED;
        g_authMgr.failedAuths++;
        
        /* 调用失败回调 */
        if (session->callback != NULL) {
            session->callback(authMsg->authId, AUTH_RESULT_FAILED, NULL, 0, session->userdata);
        }
        
        SOFTBUS_LOG_WARN("Auth failed: authId=%u, peerDevice=%s", 
                         authMsg->authId, session->peerDeviceId);
    }
    
    session->lastActiveTime = GetCurrentTimeMs();
    
    pthread_mutex_unlock(&g_authMgr.mutex);
    
    return SOFTBUS_OK;
}

/* 获取认证统计信息 */
int32_t GetAuthStatistics(AuthStatistics* stats) {
    if (!g_authMgr.isInitialized || stats == NULL) {
        return SOFTBUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&g_authMgr.mutex);
    
    memset(stats, 0, sizeof(AuthStatistics));
    stats->totalAuthAttempts = g_authMgr.totalAuthAttempts;
    stats->successfulAuths = g_authMgr.successfulAuths;
    stats->failedAuths = g_authMgr.failedAuths;
    
    /* 统计当前活跃认证会话 */
    for (int32_t i = 0; i < MAX_AUTH_SESSIONS; i++) {
        if (g_authMgr.authSessions[i].isUsed) {
            stats->activeAuthSessions++;
        }
    }
    
    pthread_mutex_unlock(&g_authMgr.mutex);
    
    return SOFTBUS_OK;
}
