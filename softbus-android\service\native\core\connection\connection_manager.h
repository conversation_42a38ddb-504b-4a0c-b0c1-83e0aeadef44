/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef CONNECTION_MANAGER_H
#define CONNECTION_MANAGER_H

#include <memory>
#include <vector>
#include <map>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <functional>
#include <chrono>

#include "softbus_core.h"

namespace android {

// 前向声明
class WifiConnection;
class BluetoothConnection;
class BleConnection;
class UsbConnection;

// 连接信息结构
struct ConnectionInfo {
    std::string sessionId;
    std::string deviceId;
    LinkType linkType;
    ConnectionState state;
    std::chrono::steady_clock::time_point createTime;
    std::chrono::steady_clock::time_point lastActiveTime;
    uint64_t bytesSent;
    uint64_t bytesReceived;
    uint32_t retryCount;
    bool isReliable;
    
    ConnectionInfo(const std::string& sid, const std::string& did, LinkType lt)
        : sessionId(sid), deviceId(did), linkType(lt), state(CONNECTION_STATE_DISCONNECTED),
          createTime(std::chrono::steady_clock::now()), lastActiveTime(createTime),
          bytesSent(0), bytesReceived(0), retryCount(0), isReliable(true) {}
};

// 会话服务器信息
struct SessionServerInfo {
    int serverId;
    std::string sessionName;
    std::chrono::steady_clock::time_point createTime;
    std::atomic<bool> isActive;
    uint32_t connectionCount;
    
    SessionServerInfo(int id, const std::string& name)
        : serverId(id), sessionName(name), createTime(std::chrono::steady_clock::now()),
          isActive(true), connectionCount(0) {}
};

// 连接统计信息
struct ConnectionStatistics {
    uint32_t totalConnections;
    uint32_t activeConnections;
    uint32_t successfulConnections;
    uint32_t failedConnections;
    uint64_t totalBytesSent;
    uint64_t totalBytesReceived;
    uint64_t totalConnectionTime; // 毫秒
    uint32_t averageConnectionTime; // 毫秒
    
    ConnectionStatistics() {
        totalConnections = 0;
        activeConnections = 0;
        successfulConnections = 0;
        failedConnections = 0;
        totalBytesSent = 0;
        totalBytesReceived = 0;
        totalConnectionTime = 0;
        averageConnectionTime = 0;
    }
};

// QoS策略
struct QosPolicy {
    QosType type;
    uint32_t value;
    uint32_t priority;
    
    QosPolicy(QosType t = QOS_TYPE_MIN_LATENCY, uint32_t v = 0, uint32_t p = 0)
        : type(t), value(v), priority(p) {}
};

/**
 * 连接管理器
 * 
 * 负责管理设备间的连接，包括连接建立、维护、断开和重连。
 * 支持多种连接类型：WiFi、蓝牙、BLE、USB等。
 */
class ConnectionManager {
public:
    ConnectionManager();
    ~ConnectionManager();
    
    // 禁用拷贝构造和赋值
    ConnectionManager(const ConnectionManager&) = delete;
    ConnectionManager& operator=(const ConnectionManager&) = delete;
    
    /**
     * 初始化连接管理器
     * 
     * @param stateCallback 连接状态变化回调
     * @return true 成功，false 失败
     */
    bool Initialize(const ConnectionStateCallback& stateCallback);
    
    /**
     * 反初始化连接管理器
     */
    void Deinitialize();
    
    /**
     * 启动连接管理器
     * 
     * @return true 成功，false 失败
     */
    bool Start();
    
    /**
     * 停止连接管理器
     */
    void Stop();
    
    /**
     * 检查是否已初始化
     * 
     * @return true 已初始化，false 未初始化
     */
    bool IsInitialized() const { return mInitialized; }
    
    /**
     * 检查是否正在运行
     * 
     * @return true 正在运行，false 已停止
     */
    bool IsRunning() const { return mRunning; }
    
    // ==================== 连接管理接口 ====================
    
    /**
     * 连接到指定设备
     * 
     * @param deviceId 设备ID
     * @param config 连接配置
     * @return 会话ID，空字符串表示失败
     */
    std::string ConnectDevice(const std::string& deviceId, const ConnectionConfig& config);
    
    /**
     * 断开设备连接
     * 
     * @param sessionId 会话ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int DisconnectDevice(const std::string& sessionId);
    
    /**
     * 断开所有连接
     * 
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int DisconnectAllDevices();
    
    /**
     * 获取连接信息
     * 
     * @param sessionId 会话ID
     * @return 连接信息，如果连接不存在则返回nullptr
     */
    std::shared_ptr<ConnectionInfo> GetConnectionInfo(const std::string& sessionId) const;
    
    /**
     * 获取所有活跃连接
     * 
     * @return 连接信息列表
     */
    std::vector<std::shared_ptr<ConnectionInfo>> GetActiveConnections() const;
    
    /**
     * 检查设备是否已连接
     * 
     * @param deviceId 设备ID
     * @return true 已连接，false 未连接
     */
    bool IsDeviceConnected(const std::string& deviceId) const;
    
    /**
     * 根据设备ID获取会话ID
     * 
     * @param deviceId 设备ID
     * @return 会话ID，空字符串表示未连接
     */
    std::string GetSessionIdByDeviceId(const std::string& deviceId) const;
    
    // ==================== 会话服务器接口 ====================
    
    /**
     * 创建会话服务器
     * 
     * @param sessionName 会话名称
     * @return 服务器ID，负数表示失败
     */
    int CreateSessionServer(const std::string& sessionName);
    
    /**
     * 移除会话服务器
     * 
     * @param serverId 服务器ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int RemoveSessionServer(int serverId);
    
    /**
     * 获取会话服务器信息
     * 
     * @param serverId 服务器ID
     * @return 服务器信息，如果服务器不存在则返回nullptr
     */
    std::shared_ptr<SessionServerInfo> GetSessionServerInfo(int serverId) const;
    
    /**
     * 获取所有活跃的会话服务器
     * 
     * @return 服务器信息列表
     */
    std::vector<std::shared_ptr<SessionServerInfo>> GetActiveSessionServers() const;
    
    // ==================== QoS管理接口 ====================
    
    /**
     * 设置连接QoS策略
     * 
     * @param sessionId 会话ID
     * @param policy QoS策略
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int SetQosPolicy(const std::string& sessionId, const QosPolicy& policy);
    
    /**
     * 获取连接QoS策略
     * 
     * @param sessionId 会话ID
     * @return QoS策略
     */
    QosPolicy GetQosPolicy(const std::string& sessionId) const;
    
    /**
     * 获取连接QoS状态
     * 
     * @param sessionId 会话ID
     * @return QoS状态值
     */
    uint32_t GetQosState(const std::string& sessionId) const;
    
    // ==================== 连接监控接口 ====================
    
    /**
     * 启用连接心跳检测
     * 
     * @param sessionId 会话ID
     * @param intervalMs 心跳间隔（毫秒）
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int EnableHeartbeat(const std::string& sessionId, uint32_t intervalMs);
    
    /**
     * 禁用连接心跳检测
     * 
     * @param sessionId 会话ID
     * @return SOFTBUS_OK 成功，其他值失败
     */
    int DisableHeartbeat(const std::string& sessionId);
    
    /**
     * 检查连接是否健康
     * 
     * @param sessionId 会话ID
     * @return true 健康，false 不健康
     */
    bool IsConnectionHealthy(const std::string& sessionId) const;
    
    /**
     * 获取连接延迟
     * 
     * @param sessionId 会话ID
     * @return 延迟时间（毫秒），-1表示无法获取
     */
    int32_t GetConnectionLatency(const std::string& sessionId) const;
    
    // ==================== 统计信息接口 ====================
    
    /**
     * 获取连接统计信息
     * 
     * @return 统计信息
     */
    ConnectionStatistics GetStatistics() const;
    
    /**
     * 重置统计信息
     */
    void ResetStatistics();
    
    /**
     * 更新连接统计信息
     * 
     * @param sessionId 会话ID
     * @param bytesSent 发送字节数
     * @param bytesReceived 接收字节数
     */
    void UpdateConnectionStats(const std::string& sessionId, uint64_t bytesSent, uint64_t bytesReceived);
    
    // ==================== 配置管理接口 ====================
    
    /**
     * 设置连接超时时间
     * 
     * @param timeoutMs 超时时间（毫秒）
     */
    void SetConnectionTimeout(uint32_t timeoutMs);
    
    /**
     * 获取连接超时时间
     * 
     * @return 超时时间（毫秒）
     */
    uint32_t GetConnectionTimeout() const { return mConnectionTimeoutMs; }
    
    /**
     * 设置最大重试次数
     * 
     * @param maxRetries 最大重试次数
     */
    void SetMaxRetries(uint32_t maxRetries);
    
    /**
     * 获取最大重试次数
     * 
     * @return 最大重试次数
     */
    uint32_t GetMaxRetries() const { return mMaxRetries; }
    
    /**
     * 启用或禁用自动重连
     * 
     * @param enabled 是否启用
     */
    void SetAutoReconnectEnabled(bool enabled);
    
    /**
     * 检查是否启用自动重连
     * 
     * @return true 启用，false 禁用
     */
    bool IsAutoReconnectEnabled() const { return mAutoReconnectEnabled; }

private:
    // 初始化状态
    std::atomic<bool> mInitialized;
    std::atomic<bool> mRunning;
    
    // 回调函数
    ConnectionStateCallback mStateCallback;
    
    // 连接器
    std::unique_ptr<WifiConnection> mWifiConnection;
    std::unique_ptr<BluetoothConnection> mBluetoothConnection;
    std::unique_ptr<BleConnection> mBleConnection;
    std::unique_ptr<UsbConnection> mUsbConnection;
    
    // 连接管理
    mutable std::mutex mConnectionMutex;
    std::map<std::string, std::shared_ptr<ConnectionInfo>> mConnections;
    
    // 会话服务器管理
    mutable std::mutex mServerMutex;
    std::map<int, std::shared_ptr<SessionServerInfo>> mSessionServers;
    
    // QoS管理
    mutable std::mutex mQosMutex;
    std::map<std::string, QosPolicy> mQosPolicies;
    
    // 统计信息
    mutable std::mutex mStatsMutex;
    ConnectionStatistics mStatistics;
    
    // 配置参数
    std::atomic<uint32_t> mConnectionTimeoutMs;
    std::atomic<uint32_t> mMaxRetries;
    std::atomic<bool> mAutoReconnectEnabled;
    
    // 工作线程
    std::thread mWorkerThread;
    std::atomic<bool> mWorkerRunning;
    std::mutex mWorkerMutex;
    std::condition_variable mWorkerCondition;
    
    // ID生成器
    std::atomic<int> mNextServerId;
    std::atomic<int> mNextSessionId;
    
    // 内部方法
    bool InitializeConnectors();
    void DeinitializeConnectors();
    void WorkerThreadFunc();
    void ProcessConnectionTimeout();
    void ProcessAutoReconnect();
    void ProcessHeartbeat();
    void OnConnectionStateChangedInternal(const std::string& sessionId, ConnectionState state, int reason);
    std::string GenerateSessionId();
    bool IsValidConnectionConfig(const ConnectionConfig& config) const;
    LinkType SelectOptimalLinkType(const std::string& deviceId, const ConnectionConfig& config) const;
    void UpdateStatistics();
    
    // 协议特定的连接方法
    std::string ConnectViaWifi(const std::string& deviceId, const ConnectionConfig& config);
    std::string ConnectViaBluetooth(const std::string& deviceId, const ConnectionConfig& config);
    std::string ConnectViaBle(const std::string& deviceId, const ConnectionConfig& config);
    std::string ConnectViaUsb(const std::string& deviceId, const ConnectionConfig& config);
    
    int DisconnectViaWifi(const std::string& sessionId);
    int DisconnectViaBluetooth(const std::string& sessionId);
    int DisconnectViaBle(const std::string& sessionId);
    int DisconnectViaUsb(const std::string& sessionId);
};

} // namespace android

#endif // CONNECTION_MANAGER_H
