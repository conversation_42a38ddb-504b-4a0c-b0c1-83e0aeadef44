--- a/frameworks/base/core/res/AndroidManifest.xml
+++ b/frameworks/base/core/res/AndroidManifest.xml
@@ -1500,6 +1500,42 @@
         android:protectionLevel="signature|privileged" />
 
+    <!-- =============================================================== -->
+    <!-- Permissions for SoftBus distributed communication framework    -->
+    <!-- =============================================================== -->
+
+    <!-- Allows an application to access SoftBus services -->
+    <permission android:name="android.permission.ACCESS_SOFTBUS"
+        android:label="@string/permlab_accessSoftBus"
+        android:description="@string/permdesc_accessSoftBus"
+        android:protectionLevel="normal" />
+
+    <!-- Allows an application to discover nearby devices using SoftBus -->
+    <permission android:name="android.permission.SOFTBUS_DISCOVERY"
+        android:label="@string/permlab_softBusDiscovery"
+        android:description="@string/permdesc_softBusDiscovery"
+        android:protectionLevel="dangerous"
+        android:permissionGroup="android.permission-group.NEARBY_DEVICES" />
+
+    <!-- Allows an application to transmit data using SoftBus -->
+    <permission android:name="android.permission.SOFTBUS_TRANSMISSION"
+        android:label="@string/permlab_softBusTransmission"
+        android:description="@string/permdesc_softBusTransmission"
+        android:protectionLevel="dangerous"
+        android:permissionGroup="android.permission-group.NEARBY_DEVICES" />
+
+    <!-- Allows an application to manage SoftBus connections -->
+    <permission android:name="android.permission.SOFTBUS_CONNECTION"
+        android:label="@string/permlab_softBusConnection"
+        android:description="@string/permdesc_softBusConnection"
+        android:protectionLevel="dangerous"
+        android:permissionGroup="android.permission-group.NEARBY_DEVICES" />
+
+    <!-- Allows system applications to manage SoftBus service -->
+    <permission android:name="android.permission.MANAGE_SOFTBUS"
+        android:label="@string/permlab_manageSoftBus"
+        android:description="@string/permdesc_manageSoftBus"
+        android:protectionLevel="signature|privileged" />
+
     <!-- =============================================================== -->
     <!-- Permissions for special development tools                      -->
     <!-- =============================================================== -->
