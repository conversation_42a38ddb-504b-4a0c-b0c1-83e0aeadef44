/*
 * Copyright (c) 2024 SentientNotes Project
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * SoftBus Common Definitions - 公共定义和数据结构
 * 基于OpenHarmony SoftBus架构移植到Android用户态
 */

#ifndef SOFTBUS_COMMON_H
#define SOFTBUS_COMMON_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 基础常量定义 */
#define SOFTBUS_OK                      0
#define SOFTBUS_ERR                     (-1)
#define SOFTBUS_INVALID_PARAM           (-2)
#define SOFTBUS_NO_INIT                 (-3)
#define SOFTBUS_FUNC_NOT_SUPPORT        (-4)
#define SOFTBUS_NETWORK_NOT_INIT        (-5)
#define SOFTBUS_PERMISSION_DENIED       (-6)

/* 尺寸限制常量 */
#define DEVICE_ID_SIZE_MAX              65
#define DEVICE_NAME_SIZE_MAX            129
#define DEVICE_TYPE_SIZE_MAX            65
#define NETWORK_ID_SIZE_MAX             65
#define SESSION_NAME_SIZE_MAX           256
#define SESSION_KEY_LENGTH              32
#define CONNECTION_ADDR_MAX             10
#define CAPABILITY_NUM                  2
#define CAPABILITY_SIZE_MAX             513
#define GROUP_ID_SIZE_MAX               65
#define PKG_NAME_SIZE_MAX               65

/* 连接类型 */
typedef enum {
    CONNECTION_ADDR_WLAN = 0,
    CONNECTION_ADDR_BR,
    CONNECTION_ADDR_BLE,
    CONNECTION_ADDR_ETH,
    CONNECTION_ADDR_MAX
} ConnectionAddrType;

/* 连接地址结构 */
typedef struct {
    ConnectionAddrType type;
    union {
        struct {
            char ip[46];  // IPv6最大长度
            uint16_t port;
        } ip;
        struct {
            char mac[18]; // MAC地址格式 XX:XX:XX:XX:XX:XX
        } br;
        struct {
            char mac[18];
            uint8_t protocol;
            int32_t psm;
        } ble;
    } info;
    char peerUid[MAX_ACCOUNT_HASH_LEN];
} ConnectionAddr;

/* 会话类型 */
typedef enum {
    TYPE_MESSAGE = 1,
    TYPE_BYTES = 2,
    TYPE_FILE = 3,
    TYPE_STREAM = 4,
    TYPE_BUTT
} SessionType;

/* 链路类型 */
typedef enum {
    LINK_TYPE_WIFI_WLAN_5G = 0,
    LINK_TYPE_WIFI_WLAN_2G = 1,
    LINK_TYPE_WIFI_P2P = 2,
    LINK_TYPE_BR = 3,
    LINK_TYPE_BLE = 4,
    LINK_TYPE_ETH = 5,
    LINK_TYPE_BUTT
} LinkType;

/* QoS等级 */
typedef enum {
    QOS_TYPE_MIN_BW = 0,
    QOS_TYPE_MAX_LATENCY = 1,
    QOS_TYPE_MIN_LATENCY = 2,
    QOS_TYPE_BUTT
} QosType;

/* QoS信息 */
typedef struct {
    QosType qosType;
    int32_t value;
} QosTV;

/* 会话属性 */
typedef struct {
    int32_t dataType;
    int32_t linkTypeNum;
    LinkType linkType[LINK_TYPE_BUTT];
    SessionType attr;
    int32_t fastTransData;
    int32_t fastTransDataSize;
    QosTV qosCount;
    QosTV *qos;
} SessionAttribute;

/* 设备发现失败原因 */
typedef enum {
    DISCOVERY_FAIL_REASON_UNKNOWN = 0,
    DISCOVERY_FAIL_REASON_INTERNAL_ERROR = 1,
    DISCOVERY_FAIL_REASON_NOT_SUPPORT_MEDIUM = 2,
    DISCOVERY_FAIL_REASON_DEVICE_NOT_SUPPORT = 3,
    DISCOVERY_FAIL_REASON_SERVICE_DATA_NOT_FOUND = 4
} DiscoveryFailReason;

/* 发布结果 */
typedef enum {
    PUBLISH_LNN_SUCCESS = 0,
    PUBLISH_LNN_INTERNAL_ERROR = 1,
    PUBLISH_LNN_NOT_SUPPORT_MEDIUM = 2,
    PUBLISH_LNN_DEVICE_NOT_SUPPORT = 3
} PublishResult;

/* 节点设备信息键值 */
typedef enum {
    NODE_KEY_UDID = 0,
    NODE_KEY_UUID,
    NODE_KEY_BR_MAC,
    NODE_KEY_IP_ADDRESS,
    NODE_KEY_DEV_NAME,
    NODE_KEY_NETWORK_CAPABILITY,
    NODE_KEY_NETWORK_TYPE,
    NODE_KEY_DATA_CHANGE_FLAG,
    NODE_KEY_BUTT
} NodeDeviceInfoKey;

/* 时间同步精度 */
typedef enum {
    LOW_ACCURACY = 0,
    NORMAL_ACCURACY,
    HIGH_ACCURACY,
    SUPER_HIGH_ACCURACY
} TimeSyncAccuracy;

/* 时间同步周期 */
typedef enum {
    SHORT_PERIOD = 0,
    LONG_PERIOD
} TimeSyncPeriod;

/* 时间同步结果 */
typedef struct {
    int32_t result;
    int32_t accuracy;
    int64_t time;
} TimeSyncResultInfo;

/* 元节点信息 */
typedef struct {
    char metaNodeId[NETWORK_ID_SIZE_MAX];
    char deviceName[DEVICE_NAME_SIZE_MAX];
    int32_t metaNodeType;
} MetaNodeInfo;

/* 档位模式 */
typedef struct {
    bool hasMode;
    int32_t cycle;
    int32_t duration;
    bool wakeupFlag;
} GearMode;

/* 回调函数类型定义 */
typedef void (*OnJoinLNNResult)(ConnectionAddr *addr, const char *networkId, int32_t retCode);
typedef void (*OnLeaveLNNResult)(const char *networkId, int32_t retCode);

/* 时间同步回调 */
typedef struct {
    void (*onTimeSyncResult)(const TimeSyncResultInfo *info, int32_t retCode);
} ITimeSyncCb;

/* 会话监听器 */
typedef struct {
    int32_t (*OnSessionOpened)(int32_t sessionId, int32_t result);
    void (*OnSessionClosed)(int32_t sessionId);
    void (*OnBytesReceived)(int32_t sessionId, const void *data, uint32_t dataLen);
    void (*OnMessageReceived)(int32_t sessionId, const void *data, uint32_t dataLen);
    void (*OnStreamReceived)(int32_t sessionId, const StreamData *data, const StreamData *ext, const StreamFrameInfo *param);
    void (*OnQosEvent)(int32_t sessionId, int32_t eventId, int32_t tvCount, const QosTV *tvList);
} ISessionListener;

/* 流数据 */
typedef struct {
    char *buf;
    int32_t bufLen;
} StreamData;

/* 流帧信息 */
typedef struct {
    int32_t frameType;
    int64_t timeStamp;
    int32_t seqNum;
    int32_t seqSubNum;
    int32_t level;
    int32_t bitMap;
    int32_t tvCount;
    QosTV *tvList;
} StreamFrameInfo;

/* 文件监听器 */
typedef struct {
    int32_t (*OnSendFileProcess)(int32_t sessionId, uint64_t bytesUpload, uint64_t bytesTotal);
    int32_t (*OnSendFileFinished)(int32_t sessionId, const char *firstFile);
    void (*OnFileTransError)(int32_t sessionId);
    int32_t (*OnReceiveFileStarted)(int32_t sessionId, const char *files, int32_t fileCnt);
    int32_t (*OnReceiveFileProcess)(int32_t sessionId, const char *firstFile, uint64_t bytesUpload, uint64_t bytesTotal);
    void (*OnReceiveFileFinished)(int32_t sessionId, const char *files, int32_t fileCnt);
} IFileCb;

/* 统计信息 */
typedef struct {
    uint64_t totalTime;
    uint64_t totalBytes;
    uint64_t throughput;
    int32_t retCode;
} FileEvent;

/* 认证类型 */
typedef enum {
    AUTH_IDENTICAL_ACCOUNT = 1,
    AUTH_DEVICE_LEVEL = 2
} AuthForm;

/* 设备安全等级 */
typedef enum {
    DEVICE_SECURITY_LEVEL_SL0 = 0,
    DEVICE_SECURITY_LEVEL_SL1 = 1,
    DEVICE_SECURITY_LEVEL_SL2 = 2,
    DEVICE_SECURITY_LEVEL_SL3 = 3,
    DEVICE_SECURITY_LEVEL_SL4 = 4,
    DEVICE_SECURITY_LEVEL_SL5 = 5
} DeviceSecurityLevel;

/* 最大账户哈希长度 */
#define MAX_ACCOUNT_HASH_LEN 65

/* 错误码定义 */
#define SOFTBUS_TRANS_SESSION_ADDPKG_FAILED         (-980019)
#define SOFTBUS_TRANS_SESSION_NAME_NO_EXIST         (-980020)
#define SOFTBUS_TRANS_SESSION_INFO_NOT_FOUND        (-980021)
#define SOFTBUS_TRANS_SESSION_ESTABLISH_FAILED      (-980022)
#define SOFTBUS_TRANS_SESSION_OPEN_FAILED           (-980023)
#define SOFTBUS_TRANS_SESSION_NO_ENABLE             (-980024)
#define SOFTBUS_TRANS_SESSION_REPEATED              (-980025)
#define SOFTBUS_TRANS_SESSION_SERVER_NOINIT         (-980026)
#define SOFTBUS_TRANS_SESSION_CANCEL                (-980027)
#define SOFTBUS_TRANS_SESSION_TIMEOUT               (-980028)

/* 工具宏定义 */
#define SOFTBUS_LOG_ERROR(fmt, ...) \
    printf("[ERROR][%s:%d] " fmt "\n", __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define SOFTBUS_LOG_WARN(fmt, ...) \
    printf("[WARN][%s:%d] " fmt "\n", __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define SOFTBUS_LOG_INFO(fmt, ...) \
    printf("[INFO][%s:%d] " fmt "\n", __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define SOFTBUS_LOG_DEBUG(fmt, ...) \
    printf("[DEBUG][%s:%d] " fmt "\n", __FUNCTION__, __LINE__, ##__VA_ARGS__)

/* 内存管理宏 */
#define SoftBusMalloc(size) malloc(size)
#define SoftBusCalloc(nmemb, size) calloc(nmemb, size)
#define SoftBusFree(ptr) do { if (ptr) { free(ptr); ptr = NULL; } } while (0)

/* 字符串操作宏 */
#define SOFTBUS_STRCPY_S(dest, destMax, src) strncpy(dest, src, destMax - 1)
#define SOFTBUS_STRCAT_S(dest, destMax, src) strncat(dest, src, destMax - strlen(dest) - 1)

#ifdef __cplusplus
}
#endif

#endif // SOFTBUS_COMMON_H
