# SoftBus Native for Android

基于OpenHarmony SoftBus架构的Android用户态移植实现，为灵境笔记项目提供分布式设备发现、连接管理和数据传输能力。

## 项目概述

SoftBus Native是OpenHarmony分布式软总线在Android平台的用户态移植，实现了完整的SoftBus协议栈，包括：

- **设备发现与管理** - 支持WiFi、蓝牙、BLE等多种发现方式
- **连接管理** - 自动连接建立、维护和故障恢复
- **会话传输** - 字节流、消息、文件、音视频流传输
- **安全认证** - 设备身份验证和数据加密
- **总线中心** - 分布式网络拓扑管理

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    Android Application                      │
├─────────────────────────────────────────────────────────────┤
│                    SoftBus JNI Layer                       │
├─────────────────────────────────────────────────────────────┤
│                  SoftBus Client SDK                        │
├─────────────────────────────────────────────────────────────┤
│                  SoftBus Core Library                      │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ Discovery   │ Connection  │Transmission │    Auth     │  │
│  │   Manager   │   Manager   │   Manager   │   Manager   │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                Bus Center                               │  │
│  └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                  SoftBus Server Daemon                     │
├─────────────────────────────────────────────────────────────┤
│              Android System (User Space)                   │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块

### 1. 设备发现模块 (Discovery)
- **WiFi发现**: 基于mDNS/DNS-SD的局域网设备发现
- **蓝牙发现**: 经典蓝牙和BLE设备扫描
- **主动/被动模式**: 支持主动扫描和被动广播
- **能力匹配**: 基于设备能力的智能匹配

### 2. 连接管理模块 (Connection)
- **多链路支持**: WiFi、蓝牙、BLE、以太网
- **自动重连**: 连接断开后自动重试
- **负载均衡**: 多连接间的负载分配
- **QoS保证**: 不同业务的服务质量保证

### 3. 传输管理模块 (Transmission)
- **会话管理**: 端到端会话建立和维护
- **数据类型**: 字节流、消息、文件、音视频流
- **可靠传输**: TCP/可靠UDP传输保证
- **流控制**: 拥塞控制和流量整形

### 4. 安全认证模块 (Authentication)
- **设备认证**: 基于证书或预共享密钥
- **数据加密**: AES-256-GCM端到端加密
- **密钥管理**: 会话密钥协商和更新
- **权限控制**: 细粒度的访问权限管理

### 5. 总线中心模块 (Bus Center)
- **网络拓扑**: 分布式网络拓扑发现和维护
- **节点管理**: 设备节点生命周期管理
- **时间同步**: 分布式时间同步协议
- **元能力**: 设备能力抽象和管理

## 编译构建

### 环境要求

**开发环境**:
- Ubuntu 18.04+ / macOS 10.15+ / Windows 10+
- CMake 3.18+
- GCC 7.0+ / Clang 8.0+
- OpenSSL 1.1.1+
- Android NDK r21+

**Android环境**:
- Android Studio 4.2+
- Android SDK API 26+
- Android NDK r21+

### 构建步骤

#### 1. 本地开发构建

```bash
# 克隆项目
git clone https://github.com/your-org/sentient-notes.git
cd sentient-notes/android/softbus-native

# 安装依赖
sudo apt-get update
sudo apt-get install build-essential cmake libssl-dev

# 构建
./build.sh

# 或者手动构建
mkdir build && cd build
cmake ..
make -j$(nproc)

# 安装
sudo make install
```

#### 2. Android交叉编译

```bash
# 设置Android NDK路径
export ANDROID_NDK=/path/to/android-ndk

# Android ARM64构建
./build.sh --android-ndk $ANDROID_NDK --target-arch arm64

# Android ARM构建  
./build.sh --android-ndk $ANDROID_NDK --target-arch arm

# Android x86_64构建
./build.sh --android-ndk $ANDROID_NDK --target-arch x86_64
```

#### 3. Android Studio集成

```bash
# 在Android项目中
cd android/app/src/main/jni

# 使用ndk-build
ndk-build

# 或者使用CMake（推荐）
# 在app/build.gradle中配置：
android {
    externalNativeBuild {
        cmake {
            path "src/main/jni/CMakeLists.txt"
        }
    }
}
```

## API使用指南

### 1. 初始化SoftBus

```c
#include "softbus_client.h"

// 初始化客户端
int32_t result = SoftBusClientInit("com.sentientnotes.android");
if (result != SOFTBUS_OK) {
    printf("SoftBus init failed: %d\n", result);
    return -1;
}
```

### 2. 设备发现

```c
#include "softbus_bus_center.h"

// 设备发现回调
void OnDeviceFound(const DeviceFoundInfo* device) {
    printf("Device found: %s (%s)\n", device->deviceName, device->deviceId);
}

void OnDiscoverySuccess(int32_t subscribeId) {
    printf("Discovery started: %d\n", subscribeId);
}

void OnDiscoverFailed(int32_t subscribeId, DiscoveryFailReason failReason) {
    printf("Discovery failed: %d, reason: %d\n", subscribeId, failReason);
}

// 设置发现回调
IDiscoveryCallback callback = {
    .OnDeviceFound = OnDeviceFound,
    .OnDiscoverySuccess = OnDiscoverySuccess,
    .OnDiscoverFailed = OnDiscoverFailed
};

// 配置订阅信息
SubscribeInfo subscribeInfo = {
    .subscribeId = 1,
    .mode = DISCOVER_MODE_ACTIVE,
    .freq = FREQ_HIGH,
    .isSameAccount = false,
    .isWakeRemote = false,
    .capability = "osdData",
    .capabilityData = 0,
    .dataLen = 0
};

// 开始发现
int32_t result = StartDiscovery("com.sentientnotes.android", &subscribeInfo, &callback);
```

### 3. 会话通信

```c
#include "session.h"

// 会话回调
int32_t OnSessionOpened(int32_t sessionId, int32_t result) {
    if (result == SOFTBUS_OK) {
        printf("Session opened: %d\n", sessionId);
        return SOFTBUS_OK;
    } else {
        printf("Session open failed: %d\n", result);
        return SOFTBUS_ERR;
    }
}

void OnSessionClosed(int32_t sessionId) {
    printf("Session closed: %d\n", sessionId);
}

void OnBytesReceived(int32_t sessionId, const void* data, uint32_t dataLen) {
    printf("Received %u bytes on session %d\n", dataLen, sessionId);
    // 处理接收到的数据
}

// 设置会话监听器
ISessionListener sessionListener = {
    .OnSessionOpened = OnSessionOpened,
    .OnSessionClosed = OnSessionClosed,
    .OnBytesReceived = OnBytesReceived,
    .OnMessageReceived = NULL,
    .OnStreamReceived = NULL,
    .OnQosEvent = NULL
};

// 创建会话服务器
int32_t result = CreateSessionServer("com.sentientnotes.android", "SentientNotesSession", &sessionListener);

// 打开会话
SessionAttribute attr = {
    .dataType = TYPE_BYTES,
    .attr = TYPE_BYTES
};

int32_t sessionId = OpenSession("SentientNotesSession", "SentientNotesSession", 
                                "target_device_network_id", "group_id", &attr);

// 发送数据
const char* message = "Hello SoftBus!";
result = SendBytes(sessionId, message, strlen(message));

// 关闭会话
CloseSession(sessionId);
```

### 4. Android Kotlin集成

```kotlin
class SoftBusManager @Inject constructor(
    private val context: Context
) {
    private val nativeManager = SoftBusNativeManager(context)
    
    suspend fun initialize(): Result<Unit> {
        return nativeManager.initialize()
    }
    
    suspend fun startDiscovery(callback: DiscoveryCallback): Result<Int> {
        val subscribeInfo = SubscribeInfo(
            mode = DiscoverMode.ACTIVE,
            freq = ExchangeFreq.HIGH,
            capability = "osdData",
            capabilityData = 0,
            isSameAccount = false,
            isWakeRemote = false
        )
        return nativeManager.startDiscovery(subscribeInfo, callback)
    }
    
    suspend fun connectToDevice(deviceId: String): Result<Int> {
        return nativeManager.openSession(
            mySessionName = "SentientNotes",
            peerSessionName = "SentientNotes", 
            peerNetworkId = deviceId,
            callback = object : SessionCallback {
                override fun onSessionOpened(sessionId: Int, success: Boolean) {
                    if (success) {
                        Log.i(TAG, "Connected to device: $deviceId")
                    }
                }
                
                override fun onBytesReceived(sessionId: Int, data: ByteArray) {
                    // 处理接收到的数据
                    handleReceivedData(data)
                }
            }
        )
    }
}
```

## 性能指标

### 延迟指标
- **设备发现延迟**: <2秒 (局域网环境)
- **连接建立延迟**: <500ms (WiFi), <1秒 (蓝牙)
- **数据传输延迟**: <10ms (局域网), <50ms (蓝牙)
- **会话建立延迟**: <200ms

### 吞吐量指标
- **WiFi传输**: >100MB/s (千兆网络)
- **蓝牙传输**: >1MB/s (蓝牙5.0)
- **BLE传输**: >100KB/s (BLE 5.0)
- **并发会话**: >100个会话

### 可靠性指标
- **连接成功率**: >99% (正常网络环境)
- **数据传输成功率**: >99.9%
- **自动重连成功率**: >95%
- **设备发现成功率**: >98%

## 测试验证

### 单元测试

```bash
# 编译测试
./build.sh --enable-testing

# 运行测试
cd build
ctest --output-on-failure

# 或者直接运行测试程序
./test_discovery
./test_connection  
./test_session
./test_auth
```

### 集成测试

```bash
# Android设备测试
adb push build/android/softbus_test /data/local/tmp/
adb shell chmod 755 /data/local/tmp/softbus_test
adb shell /data/local/tmp/softbus_test
```

### 性能测试

```bash
# 启动性能测试
./build.sh --enable-testing
cd build
./perf_test --duration=60 --connections=10
```

## 故障排除

### 常见问题

1. **编译错误**
   ```bash
   # 检查依赖
   pkg-config --exists openssl
   
   # 安装缺失依赖
   sudo apt-get install libssl-dev
   ```

2. **运行时错误**
   ```bash
   # 检查权限
   adb shell ls -l /data/local/tmp/softbus_socket
   
   # 检查服务状态
   adb shell ps | grep softbus
   ```

3. **连接问题**
   ```bash
   # 检查网络
   adb shell ping ***********
   
   # 检查防火墙
   adb shell iptables -L
   ```

### 调试方法

1. **启用详细日志**
   ```c
   // 在代码中启用调试日志
   #define SOFTBUS_DEBUG 1
   ```

2. **使用GDB调试**
   ```bash
   gdb ./softbus_server
   (gdb) set args -d
   (gdb) run
   ```

3. **Android日志查看**
   ```bash
   adb logcat | grep SoftBus
   ```

## 贡献指南

1. Fork项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建Pull Request

### 代码规范

- 遵循Linux内核编码风格
- 使用clang-format格式化代码
- 添加必要的注释和文档
- 编写单元测试

## 许可证

本项目采用Apache 2.0许可证 - 详见 [LICENSE](LICENSE) 文件。

## 联系我们

- 项目主页: https://github.com/your-org/sentient-notes
- 问题反馈: https://github.com/your-org/sentient-notes/issues
- 邮箱: <EMAIL>

---

**SoftBus Native for Android** - 让分布式连接更简单 🚀
